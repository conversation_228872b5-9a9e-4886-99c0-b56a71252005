const { MongoClient, ObjectId } = require('mongodb');

async function emergencyDisableMFA() {
    const client = new MongoClient('mongodb://localhost:27017');
    
    try {
        await client.connect();
        console.log('✅ Connected to MongoDB');
        
        const db = client.db('CAM_TRANSPORT_SYSTEM');
        const collection = db.collection('logins');
        
        const userId = new ObjectId('685923aa3135ef8ef080a6fe');
        
        // Update the user to disable MFA
        const result = await collection.updateOne(
            { _id: userId },
            { 
                $set: { 
                    mfaEnabled: false,
                    mfaSecret: null,
                    mfaDevices: [],
                    backupCodes: []
                }
            }
        );
        
        console.log('✅ MFA DISABLED!');
        console.log('   Matched:', result.matchedCount);
        console.log('   Modified:', result.modifiedCount);
        
        // Verify the change
        const user = await collection.findOne({ _id: userId });
        console.log('✅ Verification:');
        console.log('   Username:', user.username);
        console.log('   Email:', user.email);
        console.log('   MFA Enabled:', user.mfaEnabled);
        
        console.log('\n🎯 YOU CAN NOW LOG IN WITHOUT MFA!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await client.close();
    }
}

emergencyDisableMFA();
