const mongoose = require('mongoose');
const chalk = require('chalk');
require('dotenv').config();

async function testMongoConnection() {
    console.log('🔍 Testing MongoDB Connection...');
    console.log('Environment:', process.env.NODE_ENV);
    console.log('MongoDB URL:', process.env.MONGO_URL ? 'Present' : 'Missing');
    
    if (!process.env.MONGO_URL) {
        console.error(chalk.red.bold('❌ MONGO_URL environment variable is missing!'));
        return false;
    }

    try {
        console.log('🔗 Attempting to connect to MongoDB...');
        
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true, // Always use SSL for MongoDB Atlas
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log(chalk.green.bold('✅ MongoDB connected successfully!'));
        
        // Test a simple operation
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log(`📊 Found ${collections.length} collections in database`);
        
        // Close the connection
        await mongoose.connection.close();
        console.log('🔌 Connection closed');
        
        return true;
        
    } catch (error) {
        console.error(chalk.red.bold('❌ MongoDB connection failed:'));
        console.error(chalk.red(error.message));
        
        if (error.message.includes('authentication failed')) {
            console.log(chalk.yellow('💡 This looks like an authentication issue. Check your MongoDB credentials.'));
        } else if (error.message.includes('network')) {
            console.log(chalk.yellow('💡 This looks like a network issue. Check your internet connection.'));
        } else if (error.message.includes('timeout')) {
            console.log(chalk.yellow('💡 Connection timeout. The MongoDB server might be unreachable.'));
        }
        
        return false;
    }
}

// Run the test
if (require.main === module) {
    testMongoConnection().then(success => {
        if (success) {
            console.log(chalk.green.bold('\n🎉 MongoDB connection test passed!'));
            process.exit(0);
        } else {
            console.log(chalk.red.bold('\n💥 MongoDB connection test failed!'));
            process.exit(1);
        }
    });
}

module.exports = { testMongoConnection };
