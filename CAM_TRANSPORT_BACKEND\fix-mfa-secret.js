const mongoose = require('mongoose');
const speakeasy = require('speakeasy');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/CAM_TRANSPORT_SYSTEM');

// Define the Login schema
const LoginSchema = new mongoose.Schema({
    username: String,
    email: String,
    password: String,
    mfaSecret: String,
    mfaEnabled: <PERSON><PERSON><PERSON>,
    isVerified: <PERSON><PERSON><PERSON>,
    role: String
}, { timestamps: true });

const Login = mongoose.model('Login', LoginSchema);

async function fixMFASecret() {
    console.log('🔧 FIXING MFA SECRET FOR USER DHRUV');
    console.log('===================================\n');
    
    try {
        // Find the user
        const user = await Login.findOne({
            $or: [
                { username: 'dhruv' },
                { email: '<EMAIL>' }
            ]
        });
        
        if (!user) {
            console.log('❌ User not found!');
            process.exit(1);
        }
        
        console.log('✅ User found:', user.username);
        console.log('   Current MFA Enabled:', user.mfaEnabled);
        console.log('   Current Secret Length:', user.mfaSecret ? user.mfaSecret.length : 'null');
        
        // Use the SAME fixed secret as admin for testing
        const fixedSecret = 'JBSWY3DPEHPK3PXP';
        
        console.log('\n🔧 Setting FIXED MFA secret for testing...');
        
        // Update the user with the fixed secret
        user.mfaSecret = fixedSecret;
        user.mfaEnabled = true;
        
        await user.save();
        
        console.log('✅ MFA secret updated successfully!');
        console.log('\n🔑 IMPORTANT: Add this secret to your authenticator app:');
        console.log('   Secret: JBSWY3DPEHPK3PXP');
        console.log('   Account: CAM Transport (dhruv)');
        console.log('   Issuer: CAM Transport');
        
        // Generate current token for verification
        const currentToken = speakeasy.totp({
            secret: fixedSecret,
            encoding: 'base32'
        });
        
        console.log('\n🔐 Current TOTP token (for testing):');
        console.log('   Token:', currentToken);
        console.log('   Time:', new Date().toLocaleTimeString());
        
        console.log('\n📱 STEPS TO FIX:');
        console.log('1. Open your authenticator app');
        console.log('2. DELETE the existing CAM Transport entry');
        console.log('3. Add a new entry with secret: JBSWY3DPEHPK3PXP');
        console.log('4. The current token should be:', currentToken);
        console.log('5. Try logging in again with the new token');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        mongoose.connection.close();
    }
}

fixMFASecret();
