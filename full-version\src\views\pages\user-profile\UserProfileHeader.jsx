'use client'

// React Imports
import { useState, useRef, useEffect } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardMedia from '@mui/material/CardMedia'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Tooltip from '@mui/material/Tooltip'
import Avatar from '@mui/material/Avatar'
import CircularProgress from '@mui/material/CircularProgress'

// Next Auth
import { useSession } from 'next-auth/react'

// API Imports
import { getUserProfile, formatDateTime, getUserInitials } from '@/services/userProfileApi'
import { getCurrentUser } from '@/middleware/authCheck'

// Custom Hooks
import { useUserLocation } from '@/hooks/useUserLocation'
import { useProfileImage } from '@/contexts/ProfileImageContext'

const UserProfileHeader = ({ data }) => {
  const { data: session } = useSession()
  const userLocation = useUserLocation()
  const { profileImage, updateProfileImage, getCurrentProfileDisplay, getFirstLetter } = useProfileImage()
  const [isHovering, setIsHovering] = useState(false)
  const fileInputRef = useRef(null)

  // States for dynamic user data
  const [userProfile, setUserProfile] = useState(null)
  const [loading, setLoading] = useState(true)

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        // Get current user from localStorage (set during email verification)
        const currentUser = getCurrentUser()

        if (!currentUser || !currentUser.id) {
          console.log('❌ No user session found in header')
          setLoading(false)
          return
        }

        console.log('🔍 Fetching user profile for header - Admin:', currentUser.adminId || currentUser.id)
        const profile = await getUserProfile(currentUser.id)
        setUserProfile(profile)
      } catch (err) {
        console.error('❌ Error fetching user profile:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [])

  // Handle file selection
  const handleFileSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        updateProfileImage(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  // Trigger file input
  const handlePhotoClick = () => {
    fileInputRef.current?.click()
  }
  return (
    <Card>
      <CardMedia image={data?.coverImg} className='bs-[250px]' />
      <CardContent className='flex gap-5 flex-col md:flex-row !pt-0 md:justify-start md:items-end'>
        <div className='flex rounded-bs-md mbs-[-40px] border-[5px] mis-[-5px] border-be-0 border-backgroundPaper bg-backgroundPaper overflow-hidden'>
          <div
            className='relative cursor-pointer group w-[120px] h-[120px]'
            onMouseEnter={() => setIsHovering(true)}
            onMouseLeave={() => setIsHovering(false)}
            onClick={handlePhotoClick}
          >
            {profileImage ? (
              <img
                width={120}
                height={120}
                src={profileImage}
                className='w-full h-full rounded object-cover'
                alt='Profile'
              />
            ) : (
              <Avatar
                sx={{
                  width: '100%',
                  height: '100%',
                  fontSize: '3rem',
                  fontWeight: 'bold',
                  bgcolor: 'primary.main',
                  color: 'white',
                  fontFamily: 'Georgia, serif'
                }}
                className='rounded'
              >
                {userProfile ? getUserInitials(userProfile.username) : getFirstLetter()}
              </Avatar>
            )}

            {/* Hover Overlay */}
            <div className={`absolute top-0 left-0 w-full h-full bg-black/60 rounded flex flex-col items-center justify-center transition-opacity duration-200 ${isHovering ? 'opacity-100' : 'opacity-0'}`}>
              <i className='tabler-camera text-2xl text-white mb-2' />
              <Typography variant='caption' className='font-medium text-xs text-white'>
                Change Photo
              </Typography>
            </div>

            {/* Tooltip */}
            <Tooltip title="Click to change profile photo" placement="top">
              <div className='absolute inset-0' />
            </Tooltip>
          </div>

          {/* Hidden File Input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileSelect}
            accept="image/*"
            className="hidden"
          />
        </div>
        <div className='flex flex-col justify-end gap-2 flex-grow'>
          {loading ? (
            <div className='flex items-center gap-2'>
              <CircularProgress size={20} />
              <Typography variant='h4'>Loading...</Typography>
            </div>
          ) : (
            <Typography variant='h4'>{userProfile?.username || 'User'}</Typography>
          )}

          <div className='flex flex-wrap gap-6 items-center'>
            <div className='flex items-center gap-2'>
              <i className='tabler-crown' />
              <Typography className='font-medium'>
                Administrator {userProfile?.adminId ? `(${userProfile.adminId})` : ''}
              </Typography>
            </div>
            <div className='flex items-center gap-2'>
              <i className='tabler-map-pin' />
              <Typography className='font-medium'>
                {loading
                  ? 'Loading location...'
                  : userProfile?.location || 'Unknown Location'}
              </Typography>
            </div>
            <div className='flex items-center gap-2'>
              <i className='tabler-calendar' />
              <Typography className='font-medium'>
                Last login: {loading
                  ? 'Loading...'
                  : formatDateTime(userProfile?.lastLoginDate) || 'Not available'}
              </Typography>
            </div>
            <div className='flex items-center gap-2'>
              <i className='tabler-world' />
              <Typography className='font-medium' style={{ fontFamily: 'monospace', fontSize: '0.85rem' }}>
                IP: {loading
                  ? 'Loading...'
                  : userProfile?.ipAddress || 'Not recorded'}
              </Typography>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default UserProfileHeader
