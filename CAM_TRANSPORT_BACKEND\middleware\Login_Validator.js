
const dotenv = require('dotenv');
dotenv.config();

const ValidateLogin = async (req, res, next) => {
    const { username, email, password, step, mfaToken } = req.body;

    // Skip password validation for MFA verification step
    if (step === 'mfa' && mfaToken) {
        console.log('🔐 Skipping password validation for MFA step');
        return next();
    }

    // Validate required fields
    if (!email || !password) {
        return res.status(400).json({ message: "Email and password are required" });
    }

    // Validate data types
    if (typeof email !== 'string' || typeof password !== 'string') {
        return res.status(400).json({ message: "Email and password must be strings" });
    }

    if (username && typeof username !== 'string') {
        return res.status(400).json({ message: "Username must be a string" });
    }

    // Validate minimum lengths
    if (email.length < 5 || password.length < 3) {
        return res.status(400).json({ message: "Email must be at least 5 characters and password at least 3 characters" });
    }

    // SECURITY FIX: Removed hardcoded password validation
    // Password verification should be done in the controller using bcrypt.compare()
    // This validator should only check format and presence, not actual password values

    next();
}

module.exports = {
    ValidateLogin
};
