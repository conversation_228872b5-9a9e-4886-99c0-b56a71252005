'use client'

// React Imports
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

// MUI Imports
import CircularProgress from '@mui/material/CircularProgress'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'

// Auth Imports
import { checkUserExists, isUserAuthenticated } from '@/middleware/authCheck'

const ProtectedRoute = ({ children }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    const checkAccess = async () => {
      try {
        console.log('🔒 Checking protected route access...')
        
        // First check if any users exist in database
        const usersExist = await checkUserExists()
        
        if (!usersExist) {
          console.log('❌ No users exist in database, redirecting to login...')
          router.push('/en/login')
          return
        }
        
        // Then check if current user is authenticated and verified
        const isAuthenticated = await isUserAuthenticated()
        
        if (!isAuthenticated) {
          console.log('❌ User not authenticated, redirecting to login...')
          router.push('/en/login')
          return
        }
        
        console.log('✅ User has access to protected route')
        setHasAccess(true)
        
      } catch (error) {
        console.error('❌ Error checking route access:', error)
        router.push('/en/login')
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [router])

  if (loading) {
    return (
      <Box className='flex flex-col items-center justify-center min-h-screen gap-4'>
        <CircularProgress size={60} />
        <Typography variant='h6' color='text.secondary'>
          Checking authentication...
        </Typography>
        <Typography variant='body2' color='text.secondary'>
          Please wait while we verify your access
        </Typography>
      </Box>
    )
  }

  if (!hasAccess) {
    return (
      <Box className='flex flex-col items-center justify-center min-h-screen gap-4'>
        <CircularProgress size={60} />
        <Typography variant='h6' color='text.secondary'>
          Redirecting to login...
        </Typography>
      </Box>
    )
  }

  return children
}

export default ProtectedRoute
