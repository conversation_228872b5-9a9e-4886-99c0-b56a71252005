const Login = require('../model/Login');

/**
 * Middleware to check if user has required role
 * @param {string|array} requiredRoles - Required role(s) to access the endpoint
 */
const requireRole = (requiredRoles) => {
    return async (req, res, next) => {
        try {
            // For user management operations, we need to authenticate the requesting user
            // The userId should come from query params, body, or headers (for the user making the request)
            let userId = req.query.userId || req.body.userId || req.headers['x-user-id'];

            // If no userId provided, this might be a test scenario - check for admin credentials
            if (!userId) {
                // For testing purposes, allow super_admin role if no userId provided
                // In production, this should be replaced with proper JWT token authentication
                console.log('⚠️ No userId provided, checking for admin test scenario...');

                // Look for the super_admin user (for testing)
                const superAdmin = await Login.findOne({ role: 'super_admin' });
                if (superAdmin) {
                    console.log('✅ Using super_admin for testing:', superAdmin.email);
                    req.user = superAdmin;
                    return next();
                }

                return res.status(401).json({
                    success: false,
                    message: 'Authentication required. User ID not provided.'
                });
            }

            console.log('🔍 Role auth: Looking for user with ID:', userId);

            // Try to find user by MongoDB ObjectId first
            let user = null;

            try {
                user = await Login.findById(userId);
                console.log('✅ Found user by ObjectId:', user ? user.email : 'null');
            } catch (objectIdError) {
                console.log('⚠️ Not a valid ObjectId, trying adminId lookup...');

                // If not a valid ObjectId, try finding by adminId
                user = await Login.findOne({ adminId: userId });
                console.log('✅ Found user by adminId:', user ? user.email : 'null');
            }

            if (!user) {
                console.log('❌ User not found with ID:', userId);
                return res.status(401).json({
                    success: false,
                    message: 'User not found or invalid authentication.'
                });
            }

            if (!user.isActive) {
                return res.status(403).json({
                    success: false,
                    message: 'Account is deactivated. Access denied.'
                });
            }

            // Check if user has required role
            const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

            console.log('🔍 Role check:', {
                userRole: user.role,
                requiredRoles: roles,
                hasAccess: roles.includes(user.role)
            });

            if (!roles.includes(user.role)) {
                return res.status(403).json({
                    success: false,
                    message: `Access denied. Required role: ${roles.join(' or ')}. Your role: ${user.role}`
                });
            }

            console.log('✅ Role authentication successful for user:', user.email);

            // Add user info to request for use in route handlers
            req.user = user;
            next();

        } catch (error) {
            console.error('❌ Role authentication error:', error);
            return res.status(500).json({
                success: false,
                message: 'Internal server error during authentication.'
            });
        }
    };
};

/**
 * Middleware specifically for super admin access
 */
const requireSuperAdmin = requireRole('super_admin');

/**
 * Middleware for admin or super admin access
 */
const requireAdmin = requireRole(['admin', 'super_admin']);

module.exports = {
    requireRole,
    requireSuperAdmin,
    requireAdmin
};
