const Joi = require('joi');
const { isValidPhoneNumber } = require('libphonenumber-js');

const noMongoKeys = (value, helpers) => {
    if (value.includes("$")) {
        return helpers.error("any.invalid", { message: 'Input cannot contain "$" characters' });
    }
    return value;
};

const jobValidationSchema = Joi.object({
    first_name: Joi.string()
        .min(5)
        .max(100)
        .trim()
        .custom(noMongoKeys)
        .required()
        .messages({
            'string.min': 'Name must be at least 3 characters',
            'string.max': 'Name must be less than 50 characters',
            'any.invalid': 'Name cannot contain $ characters',
            'any.required': 'Name is required',
        }),
    last_name: Joi.string()
        .min(5)
        .max(100)
        .trim()
        .custom(noMongoKeys)
        .required()
        .messages({
            'string.min': 'Name must be at least 3 characters',
            'string.max': 'Name must be less than 50 characters',
            'any.invalid': 'Name cannot contain $ characters',
            'any.required': 'Name is required',
        }),

    email: Joi.string()
        .email()
        .trim()
        .required()
        .messages({
            'string.email': 'Please enter a valid email address',
            'any.required': 'Email is required',
        }),

    phone_number: Joi.string()
        .required()
        .custom((value, helpers) => {
            const cleaned = value.replace(/\s+/g, '');
            if (!isValidPhoneNumber(cleaned)) {
                return helpers.error('any.invalid');
            }
            return value;
        })
        .messages({
            'any.invalid': 'Please enter a valid phone number',
            'any.required': 'Phone number is required',
        }),

    dob: Joi.date()
        .less('now')
        .messages({
            'date.less': 'DOB must be in the past',
        }),

    address: Joi.string()
        .min(5)
        .max(100)
        .trim()
        .custom(noMongoKeys)
        .required()
        .messages({
            'string.min': 'Address must be at least 5 characters long',
            'string.max': 'Address must be less than 100 characters',
            'any.invalid': 'Address cannot contain $ or . characters',
            'any.required': 'Address is required',
        }),

    position: Joi.string()
        .valid('Driving Position', 'Non-Driving Position')
        .required()
        .messages({
            'any.only': 'Position must be either Driving Position or Non-Driving Position',
            'any.required': 'Position is required',
        }),

    specific_driving_role: Joi.string()
        .valid('CDL Driver', 'Non-CDL Driver', 'Heavy Equipment Operator', 'Local Driver', 'Long Haul Driver', 'Other Driving Role')
        .when('position', {
            is: 'Driving Position',
            then: Joi.required().messages({
                'any.required': 'Specific driving role is required for Driving Position.',
                'any.only': 'Specific driving role must be a valid option.',
            }),
            otherwise: Joi.optional(),
        }),

    specific_non_driving_role: Joi.string()
        .valid('Dispatcher', 'Admin', 'Manager', 'Technician', 'HR', 'Accountant', 'Other Non-Driving Role')
        .when('position', {
            is: 'Non-Driving Position',
            then: Joi.required().messages({
                'any.required': 'Specific non-driving role is required for Non-Driving Position.',
                'any.only': 'Specific non-driving role must be a valid option.',
            }),
            otherwise: Joi.optional(),
        }),

    commercial_license: Joi.boolean()
        .when('position', {
            is: 'Driving Position',
            then: Joi.required().messages({
                'any.required': 'Commercial license is required for Driving Position.',
            }),
            otherwise: Joi.optional(),
        }),

    other_job: Joi.string()
        .max(50)
        .trim()
        .custom(noMongoKeys)
        .when(Joi.ref('specific driving role'), {
            is: 'Other Driving Role',
            then: Joi.required().messages({
                'any.required': 'Other driving job title is required',
                'string.max': 'Other driving job title must be 50 characters or less',
                'any.invalid': 'Other driving job title cannot contain $ or . characters',
            }),
            otherwise: Joi.when(Joi.ref('specific_non_driving_role'), {
                is: 'Other Non-Driving Role',
                then: Joi.required().messages({
                    'any.required': 'Other non-driving job title is required',
                    'string.max': 'Other non-driving job title must be 50 characters or less',
                    'any.invalid': 'Other non-driving job title cannot contain $ or . characters',
                }),
                otherwise: Joi.optional(),
            })
        }),

    employment_type: Joi.string()
        .valid('Full-time', 'Part-time', 'Contract', 'Temporary', 'Freelance')
        .required()
        .messages({
            'any.only': 'Employment type must be a valid option',
            'any.required': 'Employment type is required',
        }),

    preferred_start_date: Joi.date()
        .min('now')
        .required()
        .messages({
            'date.min': 'Start date must be today or in the future',
            'any.required': 'Preferred start date is required',
        }),

    relocate: Joi.string()
        .valid('Yes', 'No')
        .required()
        .messages({
            'any.only': 'Relocate must be Yes or No',
            'any.required': 'Relocate field is required',
        }),

    experience: Joi.string()
        .valid('Fresher', '<6 months', '6-12 months', '1-2 years', '2-5 years', '5+ years', '10+ years')
        .required()
        .messages({
            'any.only': 'Experience must be a valid option',
            'any.required': 'Experience is required',
        }),

    resume: Joi.string()
        .pattern(/\.(pdf|doc|docx)$/i)
        .required()
        .messages({
            'string.pattern.base': 'Resume must be a PDF, DOC, or DOCX file',
            'any.required': 'Resume is required',
        }),

    work_reason: Joi.string()
        .min(10)
        .max(300)
        .trim()
        .custom(noMongoKeys)
        .required()
        .messages({
            'string.min': 'Reason must be at least 10 characters long',
            'string.max': 'Reason must be less than 300 characters',
            'any.invalid': 'Reason cannot contain $ or . characters',
            'any.required': 'Reason for applying is required',
        }),

    reference: Joi.string()
        .valid(
            'Job Portal(Indeed,Monster,etc.)',
            'Social Media(LinkedIN,Facebook,Instagram)',
            'Friends or Colleagues',
            'Advertisement',
            'Walk-in or On-site Visit',
            'Other(Please specify)'
        )
        .required()
        .messages({
            'any.only': 'Reference must be a valid option',
            'any.required': 'Reference is required',
        }),

    other_reference: Joi.string()
        .max(150)
        .trim()
        .custom(noMongoKeys)
        .when('reference', {
            is: 'Other(Please specify)',
            then: Joi.required().messages({
                'any.required': 'Other reference must be provided',
                'string.max': 'Other reference must be 150 characters or less',
                'any.invalid': 'Other reference cannot contain $ or . characters',
            }),
            otherwise: Joi.optional(),
        }),

    _honeypot: Joi.string().allow(''),

    ip: Joi.string().ip({ version: ['ipv4', 'ipv6'], cidr: 'forbidden' }).optional(),
});

module.exports = { jobValidationSchema };
