import { NextResponse } from 'next/server'

export async function GET(request) {
  try {
    // Get client IP address
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const clientIp = forwarded ? forwarded.split(',')[0] : realIp || '127.0.0.1'
    
    console.log('Client IP:', clientIp)
    
    // For localhost/development, use a default IP for testing
    const ipToCheck = clientIp === '127.0.0.1' || clientIp === '::1' ? '*******' : clientIp
    
    // Use ipapi.co for IP geolocation (free tier: 1000 requests/day)
    const response = await fetch(`https://ipapi.co/${ipToCheck}/json/`, {
      headers: {
        'User-Agent': 'CAM Transport App'
      }
    })
    
    if (!response.ok) {
      throw new Error('Failed to fetch location data')
    }
    
    const locationData = await response.json()
    
    // Extract relevant information
    const result = {
      ip: clientIp,
      city: locationData.city || 'Unknown City',
      region: locationData.region || 'Unknown Region',
      country: locationData.country_name || 'Unknown Country',
      countryCode: locationData.country_code || 'XX',
      timezone: locationData.timezone || 'UTC',
      latitude: locationData.latitude || null,
      longitude: locationData.longitude || null,
      isp: locationData.org || 'Unknown ISP',
      fullLocation: `${locationData.city || 'Unknown'}, ${locationData.region || 'Unknown'}, ${locationData.country_name || 'Unknown'}`
    }
    
    console.log('Location result:', result)
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error('Error fetching location:', error)
    
    // Return fallback data
    return NextResponse.json({
      ip: 'Unknown',
      city: 'Unknown City',
      region: 'Unknown Region', 
      country: 'Unknown Country',
      countryCode: 'XX',
      timezone: 'UTC',
      latitude: null,
      longitude: null,
      isp: 'Unknown ISP',
      fullLocation: 'Location unavailable'
    })
  }
}
