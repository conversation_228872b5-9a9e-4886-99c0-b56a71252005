const Login = require('../model/Login');
const { sendEmail } = require('../service/Mailer');
const crypto = require('crypto');

/**
 * Generate and send email OTP for new user verification
 */
const generateEmailOTP = async (req, res) => {
    try {
        const { email, username } = req.body;

        if (!email || !username) {
            return res.status(400).json({
                success: false,
                message: 'Email and username are required'
            });
        }

        // Find user by email and username
        const user = await Login.findOne({ 
            email: email.toLowerCase().trim(),
            username: username.trim()
        });

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if user is already verified
        if (user.isVerified) {
            return res.status(400).json({
                success: false,
                message: 'User is already verified'
            });
        }

        // Check if user has MFA enabled (should not for new users)
        if (user.mfaEnabled) {
            return res.status(400).json({
                success: false,
                message: 'User should use TOTP authentication instead of email OTP'
            });
        }

        // Generate 6-digit OTP
        const otp = Math.floor(100000 + Math.random() * 900000);
        const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

        // Save OTP to user
        user.otp = otp;
        user.otpExpiry = otpExpiry;
        await user.save();

        // Send OTP email
        const emailTemplate = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .otp-code { font-size: 24px; font-weight: bold; color: #007bff; text-align: center; padding: 20px; background: white; border: 2px dashed #007bff; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Welcome to CAM Transport</h1>
                </div>
                <div class="content">
                    <h2>Email Verification Required</h2>
                    <p>Hello ${user.username},</p>
                    <p>Welcome to CAM Transport! To complete your account setup, please verify your email address using the OTP code below:</p>
                    
                    <div class="otp-code">${otp}</div>
                    
                    <p><strong>Important:</strong></p>
                    <ul>
                        <li>This OTP will expire in 10 minutes</li>
                        <li>After verification, you'll be able to set up your Multi-Factor Authentication (MFA)</li>
                        <li>Do not share this code with anyone</li>
                    </ul>
                    
                    <p>If you didn't request this verification, please contact your administrator immediately.</p>
                </div>
                <div class="footer">
                    <p>© 2024 CAM Transport Ltd. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        `;

        await sendEmail(
            user.email,
            'CAM Transport - Email Verification Required',
            emailTemplate
        );

        console.log(`✅ Email OTP sent to: ${user.email}`);

        return res.status(200).json({
            success: true,
            message: 'OTP sent to your email address',
            userId: user._id,
            expiresIn: '10 minutes'
        });

    } catch (error) {
        console.error('❌ Error generating email OTP:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Verify email OTP
 */
const verifyEmailOTP = async (req, res) => {
    try {
        const { userId, otp } = req.body;

        if (!userId || !otp) {
            return res.status(400).json({
                success: false,
                message: 'User ID and OTP are required'
            });
        }

        // Find user
        const user = await Login.findById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if OTP exists and is not expired
        if (!user.otp || !user.otpExpiry) {
            return res.status(400).json({
                success: false,
                message: 'No OTP found. Please request a new one.'
            });
        }

        if (new Date() > user.otpExpiry) {
            return res.status(400).json({
                success: false,
                message: 'OTP has expired. Please request a new one.'
            });
        }

        // Verify OTP
        if (parseInt(otp) !== user.otp) {
            return res.status(400).json({
                success: false,
                message: 'Invalid OTP'
            });
        }

        // Mark user as verified and clear OTP
        user.isVerified = true;
        user.otp = null;
        user.otpExpiry = null;
        await user.save();

        console.log(`✅ Email verified for user: ${user.email}`);

        return res.status(200).json({
            success: true,
            message: 'Email verified successfully',
            user: {
                id: user._id,
                email: user.email,
                username: user.username,
                isVerified: user.isVerified
            }
        });

    } catch (error) {
        console.error('❌ Error verifying email OTP:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

module.exports = {
    generateEmailOTP,
    verifyEmailOTP
};
