// React Imports
import React, { useState } from 'react'

// MUI Imports
import Chip from '@mui/material/Chip'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'

// Status configuration for quotes
const statusConfig = {
  pending: { 
    label: 'Pending', 
    color: 'warning',
    icon: 'tabler-clock',
    bgColor: '#fff3cd',
    textColor: '#856404'
  },
  'in-review': { 
    label: 'In Review', 
    color: 'info',
    icon: 'tabler-eye',
    bgColor: '#d1ecf1',
    textColor: '#0c5460'
  },
  approved: { 
    label: 'Approved', 
    color: 'success',
    icon: 'tabler-check',
    bgColor: '#d4edda',
    textColor: '#155724'
  },
  rejected: { 
    label: 'Rejected', 
    color: 'error',
    icon: 'tabler-x',
    bgColor: '#f8d7da',
    textColor: '#721c24'
  }
}

const StatusDropdown = ({ currentStatus, onStatusChange, quoteId }) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const open = Boolean(anchorEl)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleStatusSelect = (newStatus) => {
    onStatusChange(quoteId, newStatus)
    handleClose()
  }

  const currentConfig = statusConfig[currentStatus] || statusConfig.pending

  return (
    <>
      <Chip
        label={currentConfig.label}
        color={currentConfig.color}
        variant="tonal"
        size="small"
        icon={<i className={currentConfig.icon} style={{ fontSize: '14px' }} />}
        onClick={handleClick}
        sx={{
          cursor: 'pointer',
          minWidth: '90px',
          height: '28px',
          fontSize: '0.75rem',
          fontWeight: 500,
          '&:hover': {
            backgroundColor: currentConfig.bgColor,
            color: currentConfig.textColor,
            transform: 'scale(1.02)'
          },
          transition: 'all 0.2s ease-in-out'
        }}
      />
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left'
        }}
        PaperProps={{
          sx: {
            minWidth: 140,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            border: '1px solid rgba(0,0,0,0.05)'
          }
        }}
      >
        {Object.entries(statusConfig).map(([status, config]) => (
          <MenuItem
            key={status}
            onClick={() => handleStatusSelect(status)}
            selected={currentStatus === status}
            sx={{
              '&:hover': {
                backgroundColor: config.bgColor,
                color: config.textColor
              },
              '&.Mui-selected': {
                backgroundColor: config.bgColor,
                color: config.textColor,
                '&:hover': {
                  backgroundColor: config.bgColor,
                  color: config.textColor
                }
              }
            }}
          >
            <ListItemIcon>
              <i 
                className={config.icon} 
                style={{ 
                  fontSize: '16px',
                  color: currentStatus === status ? config.textColor : 'inherit'
                }} 
              />
            </ListItemIcon>
            <ListItemText 
              primary={config.label}
              sx={{
                '& .MuiListItemText-primary': {
                  fontSize: '0.875rem',
                  fontWeight: currentStatus === status ? 600 : 400
                }
              }}
            />
          </MenuItem>
        ))}
      </Menu>
    </>
  )
}

export default StatusDropdown
