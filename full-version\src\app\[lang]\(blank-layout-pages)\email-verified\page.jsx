'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'

// Component Imports
import Logo from '@components/layout/shared/Logo'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'
import { useSettings } from '@core/hooks/useSettings'

// Styled Components
const MaskImg = styled('img')({
  blockSize: '100%',
  inlineSize: '100%',
  position: 'absolute'
})

const VerificationIllustration = styled('img')(({ theme }) => ({
  zIndex: 2,
  blockSize: 'auto',
  maxInlineSize: '100%',
  margin: theme.spacing(20, 0),
  [theme.breakpoints.down(1536)]: {
    margin: theme.spacing(15, 0)
  },
  [theme.breakpoints.down(1200)]: {
    margin: theme.spacing(10, 0)
  }
}))

const EmailVerified = () => {
  // States
  const [isLoading, setIsLoading] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState(null)
  const [errorMessage, setErrorMessage] = useState('')

  // Hooks
  const { settings } = useSettings()
  const router = useRouter()
  const searchParams = useSearchParams()

  // Vars
  const darkImg = '/images/pages/auth-mask-dark.png'
  const lightImg = '/images/pages/auth-mask-light.png'
  const darkIllustration = '/images/illustrations/auth/v2-verify-dark.png'
  const lightIllustration = '/images/illustrations/auth/v2-verify-light.png'
  const mode = settings.mode

  // Image variants based on theme
  const authBackground = useImageVariant(mode, lightImg, darkImg)
  const characterIllustration = useImageVariant(mode, lightIllustration, darkIllustration)

  useEffect(() => {
    // Get verification parameters from URL
    const userId = searchParams.get('userId')
    const token = searchParams.get('token')
    
    if (!userId || !token) {
      setVerificationStatus('error')
      setErrorMessage('Invalid verification link. Missing required parameters.')
      setIsLoading(false)
      return
    }
    
    // Verify the email with backend
    const verifyEmail = async () => {
      try {
        const response = await fetch(`/api/verify-email?userId=${userId}&token=${token}`)
        const data = await response.json()
        
        if (response.ok && data.success) {
          setVerificationStatus('success')
        } else {
          setVerificationStatus('error')
          setErrorMessage(data.message || 'Email verification failed. Please try again.')
        }
      } catch (error) {
        console.error('Error verifying email:', error)
        setVerificationStatus('error')
        setErrorMessage('An error occurred during verification. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }
    
    verifyEmail()
  }, [searchParams])

  const handleGoToProfile = () => {
    router.push('/pages/user-profile')
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  if (isLoading) {
    return (
      <div className='flex bs-full justify-center items-center min-bs-[100dvh]'>
        <Card className='flex flex-col items-center p-8'>
          <CardContent className='flex flex-col items-center gap-4'>
            <CircularProgress size={60} />
            <Typography variant='h5'>Verifying your email...</Typography>
            <Typography variant='body2' color='text.secondary' className='text-center'>
              Please wait while we verify your email. This may take a few moments.
            </Typography>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className='flex bs-full justify-center min-bs-[100dvh]'>
      <div className='flex bs-full items-center justify-center flex-1 relative p-6 max-md:hidden'>
        <VerificationIllustration src={characterIllustration} alt='verification-illustration' />
        <MaskImg alt='mask' src={authBackground} />
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <div className='absolute block-start-5 sm:block-start-[33px] inline-start-6 sm:inline-start-[38px]'>
          <Logo />
        </div>
        <div className='flex flex-col gap-6 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset] mbs-8 sm:mbs-11 md:mbs-0'>
          <Card className='flex flex-col items-center p-8'>
            <CardContent className='flex flex-col items-center gap-6 text-center'>
              {verificationStatus === 'success' ? (
                <>
                  <Box className='flex items-center justify-center w-20 h-20 rounded-full bg-success-light'>
                    <i className='tabler-check text-4xl text-success-main' />
                  </Box>
                  
                  <div className='flex flex-col gap-2'>
                    <Typography variant='h4' className='text-success-main'>
                      Email Verified Successfully!
                    </Typography>
                    <Typography variant='body1'>
                      Your email has been verified. You can now access your account.
                    </Typography>
                  </div>
                  
                  <Button 
                    fullWidth 
                    variant='contained' 
                    color='primary'
                    onClick={handleGoToProfile}
                    size='large'
                  >
                    Go to Profile Page
                  </Button>
                  
                  <Button 
                    fullWidth 
                    variant='outlined' 
                    onClick={handleBackToLogin}
                  >
                    Back to Login
                  </Button>
                </>
              ) : (
                <>
                  <Box className='flex items-center justify-center w-20 h-20 rounded-full bg-error-light'>
                    <i className='tabler-alert-circle text-4xl text-error-main' />
                  </Box>

                  <div className='flex flex-col gap-2'>
                    <Typography variant='h4' className='text-error-main'>
                      Verification Failed
                    </Typography>
                    <Typography variant='body1'>
                      {errorMessage}
                    </Typography>
                  </div>

                  <Alert severity='error' className='w-full'>
                    Please try again or contact support if the problem persists.
                  </Alert>

                  <Button
                    fullWidth
                    variant='outlined'
                    onClick={handleBackToLogin}
                  >
                    Back to Login
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default EmailVerified
