// React Imports
import { Suspense } from 'react'

// Authentication Imports
import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'

const QuotesLayout = async ({ children, params }) => {
    // Check for session
    const session = await getServerSession()

    // If no session, redirect to login
    if (!session) {
        return redirect(`/en/login`)
    }

    return (
        <Suspense fallback={<div className="flex items-center justify-center p-6">Loading...</div>}>
            {children}
        </Suspense>
    )
}

export default QuotesLayout 
