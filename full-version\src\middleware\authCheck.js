// Authentication check middleware
import { checkUsersExistInDB } from '@/app/server/actions'

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

// Check if any user exists in the database
export const checkUserExists = async () => {
  try {
    console.log('🔍 Checking if any user exists in database via server action...')

    const hasUsers = await checkUsersExistInDB()

    console.log('✅ User existence check result:', { hasUsers })

    return hasUsers
  } catch (error) {
    console.error('❌ Error checking user existence:', error)
    return false
  }
}

// Check if specific user is verified
export const checkUserVerified = async (userId) => {
  try {
    console.log('🔍 Checking if user is verified:', userId)

    const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      console.error('❌ Failed to check user verification')
      return false
    }

    const result = await response.json()
    console.log('✅ User verification check result:', result)

    return result.user?.isVerified || false
  } catch (error) {
    console.error('❌ Error checking user verification:', error)
    return false
  }
}

// Redirect to login if no users exist
export const redirectToLoginIfNoUsers = async (router) => {
  const hasUsers = await checkUserExists()

  if (!hasUsers) {
    console.log('🔄 No users found, redirecting to login...')
    router.push('/en/login')
    return false
  }

  return true
}

// Get current user from localStorage or session
export const getCurrentUser = () => {
  if (typeof window === 'undefined') return null

  try {
    const userData = localStorage.getItem('currentUser')
    return userData ? JSON.parse(userData) : null
  } catch (error) {
    console.error('❌ Error getting current user:', error)
    return null
  }
}

// Set current user in localStorage
export const setCurrentUser = (userData) => {
  if (typeof window === 'undefined') return

  try {
    localStorage.setItem('currentUser', JSON.stringify(userData))
    console.log('✅ Current user saved to localStorage')
  } catch (error) {
    console.error('❌ Error saving current user:', error)
  }
}

// Clear current user from localStorage
export const clearCurrentUser = () => {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem('currentUser')
    console.log('✅ Current user cleared from localStorage')
  } catch (error) {
    console.error('❌ Error clearing current user:', error)
  }
}

// Check if user is authenticated and verified
export const isUserAuthenticated = async () => {
  const currentUser = getCurrentUser()

  if (!currentUser || !currentUser.id) {
    console.log('❌ No current user found')
    return false
  }

  const isVerified = await checkUserVerified(currentUser.id)

  if (!isVerified) {
    console.log('❌ User is not verified')
    return false
  }

  console.log('✅ User is authenticated and verified')
  return true
}

// ==================== ROLE-BASED ACCESS CONTROL ====================

// Check if user has admin role
export const isUserAdmin = async (userId) => {
  try {
    console.log('🔍 Checking admin status for user:', userId)

    const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ User role check result:', result.user?.role)

    return (result.user?.role === 'admin' || result.user?.role === 'super_admin') && result.user?.isActive === true
  } catch (error) {
    console.error('❌ Error checking admin status:', error)
    return false
  }
}

// Check if user has permission to manage other users
export const canManageUsers = async (userId) => {
  try {
    console.log('🔍 Checking user management permissions for user:', userId)

    // For now, only admins can manage users
    const isAdmin = await isUserAdmin(userId)

    if (isAdmin) {
      console.log('✅ User has user management permissions')
      return true
    } else {
      console.log('❌ User does not have user management permissions')
      return false
    }
  } catch (error) {
    console.error('❌ Error checking user management permissions:', error)
    return false
  }
}

// Validate user session and permissions
export const validateUserSession = async (sessionUser) => {
  try {
    if (!sessionUser || !sessionUser.id) {
      console.log('❌ No valid session user provided')
      return { isValid: false, hasAdminAccess: false }
    }

    console.log('🔍 Validating session for user:', sessionUser.email)

    // Check if user still exists and is active
    const response = await fetch(`${API_BASE_URL}/user-profile/${sessionUser.id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      console.log('❌ User not found in database')
      return { isValid: false, hasAdminAccess: false }
    }

    const result = await response.json()
    const user = result.user

    if (!user.isActive) {
      console.log('❌ User account is disabled')
      return { isValid: false, hasAdminAccess: false }
    }

    if (!user.isVerified) {
      console.log('❌ User account is not verified')
      return { isValid: false, hasAdminAccess: false }
    }

    const hasAdminAccess = user.role === 'admin' || user.role === 'super_admin' || user.role === 'normal_user'
    const isSuperAdmin = user.role === 'super_admin'

    console.log('✅ Session validation result:', {
      isValid: true,
      hasAdminAccess,
      isSuperAdmin,
      userRole: user.role
    })

    return {
      isValid: true,
      hasAdminAccess,
      isSuperAdmin,
      user: {
        id: user.id,
        adminId: user.adminId,
        username: user.username,
        email: user.email,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        isSuperAdmin
      }
    }
  } catch (error) {
    console.error('❌ Error validating user session:', error)
    return { isValid: false, hasAdminAccess: false }
  }
}

// Role-based access control helper
export const hasPermission = (userRole, requiredRole) => {
  const roleHierarchy = {
    'super_admin': 4,
    'admin': 3,
    'normal_user': 2,
    'user': 1
  }

  const userLevel = roleHierarchy[userRole] || 0
  const requiredLevel = roleHierarchy[requiredRole] || 0

  return userLevel >= requiredLevel
}

// Check if user is super admin
export const isUserSuperAdmin = async (userId) => {
  try {
    console.log('🔍 Checking super admin status for user:', userId)

    const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ User role check result:', result.user?.role)

    return result.user?.role === 'super_admin' && result.user?.isActive === true
  } catch (error) {
    console.error('❌ Error checking super admin status:', error)
    return false
  }
}

// Check if user can perform specific actions
export const canPerformAction = async (userId, action) => {
  try {
    const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    const user = result.user

    if (!user || !user.isActive) {
      return false
    }

    const isSuperAdmin = user.role === 'super_admin'
    const isAdmin = user.role === 'admin' || user.role === 'super_admin'
    const isNormalUser = user.role === 'normal_user'

    const permissions = {
      // User management - only super admins
      'manage_users': isSuperAdmin,
      'view_users': isSuperAdmin,
      'create_users': isSuperAdmin,
      'edit_users': isSuperAdmin,
      'delete_users': isSuperAdmin,
      'manage_roles': isSuperAdmin,

      // General admin panel access - super admin and normal users with admin access
      'view_admin_panel': isSuperAdmin || isNormalUser,
      'manage_settings': isSuperAdmin || isNormalUser,

      // MFA management - super admin and normal users
      'manage_mfa': isSuperAdmin || isNormalUser,
      'view_security': isSuperAdmin || isNormalUser
    }

    return permissions[action] || false
  } catch (error) {
    console.error('❌ Error checking action permissions:', error)
    return false
  }
}
