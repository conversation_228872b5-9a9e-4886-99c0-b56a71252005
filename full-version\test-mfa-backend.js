const fetch = require('node-fetch');

async function testMFABackend() {
    const API_BASE_URL = 'http://localhost:8090';
    
    console.log('🧪 Testing MFA Backend Endpoints...\n');
    
    try {
        // Test 1: Regular login
        console.log('1️⃣ Testing regular login...');
        const loginResponse = await fetch(`${API_BASE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin'
            })
        });
        
        const loginResult = await loginResponse.json();
        console.log('Login Response:', JSON.stringify(loginResult, null, 2));
        
        if (loginResult.success && loginResult.user) {
            const userId = loginResult.user.id;
            console.log(`✅ Login successful. User ID: ${userId}`);
            console.log(`🔐 MFA Enabled: ${loginResult.user.mfaEnabled}`);
            console.log(`🔐 Requires MFA: ${loginResult.user.requiresMFA}`);
            
            // Test 2: Check MFA status
            console.log('\n2️⃣ Testing MFA status...');
            const mfaStatusResponse = await fetch(`${API_BASE_URL}/mfa/status/${userId}`);
            const mfaStatus = await mfaStatusResponse.json();
            console.log('MFA Status:', JSON.stringify(mfaStatus, null, 2));
            
            // Test 3: Test MFA verification with a test token
            if (loginResult.user.mfaEnabled) {
                console.log('\n3️⃣ Testing MFA verification...');
                
                // Generate a test token using the fixed secret
                const speakeasy = require('speakeasy');
                const testToken = speakeasy.totp({
                    secret: 'JBSWY3DPEHPK3PXP', // Fixed secret from backend
                    encoding: 'base32'
                });
                
                console.log(`🔑 Generated test token: ${testToken}`);
                
                const mfaVerifyResponse = await fetch(`${API_BASE_URL}/login/mfa-verify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        email: '<EMAIL>',
                        password: 'verified',
                        mfaToken: testToken,
                        step: 'mfa'
                    })
                });
                
                const mfaResult = await mfaVerifyResponse.json();
                console.log('MFA Verification Response:', JSON.stringify(mfaResult, null, 2));
                
                if (mfaResult.success) {
                    console.log('✅ MFA verification successful!');
                } else {
                    console.log('❌ MFA verification failed:', mfaResult.message);
                }
            }
        } else {
            console.log('❌ Login failed:', loginResult.message);
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

// Run the test
testMFABackend();
