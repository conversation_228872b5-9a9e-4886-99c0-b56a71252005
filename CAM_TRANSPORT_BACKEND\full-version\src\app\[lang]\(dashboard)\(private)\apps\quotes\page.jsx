'use client'

import { useState } from 'react';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import IconButton from '@mui/material/IconButton';
import RemoveRedEyeIcon from '@mui/icons-material/RemoveRedEye';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import { format } from 'date-fns';

const QuotesPage = () => {
  const [pickupDate, setPickupDate] = useState(null);
  const [deliveryDate, setDeliveryDate] = useState(null);
  const [dateError, setDateError] = useState('');

  // Validate dates when either pickup or delivery date changes
  const validateDates = (newPickupDate, newDeliveryDate) => {
    if (newPickupDate && newDeliveryDate) {
      if (newPickupDate > newDeliveryDate) {
        setDateError('Pickup date cannot be later than delivery date');
        return false;
      }
      setDateError('');
      return true;
    }
    return true;
  };

  // Handle pickup date change
  const handlePickupDateChange = (newDate) => {
    if (validateDates(newDate, deliveryDate)) {
      setPickupDate(newDate);
    }
  };

  // Handle delivery date change
  const handleDeliveryDateChange = (newDate) => {
    if (validateDates(pickupDate, newDate)) {
      setDeliveryDate(newDate);
    }
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            {/* Pickup Date Picker */}
            <DatePicker
              label="Pickup Date"
              value={pickupDate}
              onChange={handlePickupDateChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  error={!!dateError}
                  helperText={dateError}
                />
              )}
              minDate={new Date()} // Can't pick dates in the past
              inputFormat="MM/dd/yyyy"
            />

            {/* Delivery Date Picker */}
            <DatePicker
              label="Delivery Date"
              value={deliveryDate}
              onChange={handleDeliveryDateChange}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  error={!!dateError}
                  helperText={dateError}
                />
              )}
              minDate={pickupDate || new Date()} // Can't pick dates before pickup date
              inputFormat="MM/dd/yyyy"
            />
          </LocalizationProvider>

          {/* Display selected dates for confirmation */}
          {(pickupDate || deliveryDate) && (
            <Box sx={{ mt: 2 }}>
              {pickupDate && (
                <TextField
                  fullWidth
                  label="Selected Pickup Date"
                  value={format(pickupDate, 'PPP')}
                  InputProps={{ readOnly: true }}
                  sx={{ mb: 2 }}
                />
              )}
              {deliveryDate && (
                <TextField
                  fullWidth
                  label="Selected Delivery Date"
                  value={format(deliveryDate, 'PPP')}
                  InputProps={{ readOnly: true }}
                />
              )}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

export default QuotesPage; 