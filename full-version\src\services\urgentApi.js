// Server Actions Imports
import { fetchUrgentInquiriesFromServer } from '@/app/server/actions'

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8090'

// Fetch all urgent inquiries
export const fetchUrgentInquiries = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/urgent/get-urgents`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch urgent inquiries: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('❌ Error fetching urgent inquiries:', error);
    throw error;
  }
};

// Update urgent inquiry status
export const updateUrgentInquiryStatus = async (inquiryId, status) => {
  try {
    console.log('API: Updating urgent inquiry status', inquiryId, 'to', status);
    console.log('API URL:', `${API_BASE_URL}/urgent/update-status/${inquiryId}`);

    const response = await fetch(`${API_BASE_URL}/urgent/update-status/${inquiryId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status })
    });

    console.log('API Response status:', response.status);
    console.log('API Response ok:', response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error response:', errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('API Success result:', result);
    return result;
  } catch (error) {
    console.error('❌ Error updating urgent inquiry status:', error);
    throw error;
  }
};

// Delete urgent inquiry
export const deleteUrgentInquiry = async (inquiryId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/urgent/delete-urgent/${inquiryId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to delete urgent inquiry: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('❌ Error deleting urgent inquiry:', error);
    throw error;
  }
};

// Download document
export const downloadUrgentDocument = async (documentPath, inquiryName) => {
  try {
    if (!documentPath) {
      throw new Error('No document path provided');
    }
    // Construct the full document URL
    const documentUrl = documentPath.startsWith('http')
      ? documentPath
      : `${API_BASE_URL}/${documentPath.replace(/^\//, '')}`;

    const response = await fetch(documentUrl, {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get the blob data
    const blob = await response.blob();
    // Extract filename from path or use default
    const filename = documentPath.split('/').pop() || `${inquiryName}_document.pdf`;
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    // Cleanup
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
    return { success: true, filename };
  } catch (error) {
    console.error('❌ Error downloading document:', error);
    throw error;
  }
};

// Get urgent inquiry by ID
export const getUrgentInquiryById = async (inquiryId) => {
  try {
    const inquiries = await fetchUrgentInquiries();
    return inquiries.find(inquiry => inquiry.id === inquiryId);
  } catch (error) {
    console.error('Error fetching urgent inquiry by ID:', error);
    throw error;
  }
};
