'use client'

// React Imports
import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'
import Box from '@mui/material/Box'
import Link from '@mui/material/Link'
import Divider from '@mui/material/Divider'
import InputAdornment from '@mui/material/InputAdornment'
import IconButton from '@mui/material/IconButton'
import useMediaQuery from '@mui/material/useMediaQuery'
import { styled, useTheme } from '@mui/material/styles'

// Third-party Imports
import { signIn, getSession, useSession } from 'next-auth/react'
import { Controller, useForm } from 'react-hook-form'
import { valibotResolver } from '@hookform/resolvers/valibot'
import { object, string, pipe, nonEmpty, minLength } from 'valibot'

// Component Imports
import Logo from '@components/layout/shared/Logo'
import CustomTextField from '@core/components/mui/TextField'

// Config Imports
import themeConfig from '@configs/themeConfig'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'
import { useSettings } from '@core/hooks/useSettings'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

// Styled Components
const MFAIllustration = styled('img')(({ theme }) => ({
  zIndex: 2,
  blockSize: 'auto',
  maxBlockSize: 550,
  maxInlineSize: '100%',
  margin: theme.spacing(12, 0, 8),
  [theme.breakpoints.down(1536)]: {
    maxBlockSize: 400
  },
  [theme.breakpoints.down('lg')]: {
    maxBlockSize: 300
  }
}))

const schema = object({
  mfaToken: pipe(string(), nonEmpty('MFA token is required'), minLength(6, 'Token must be at least 6 characters'))
})

const MFAVerificationPage = ({ mode }) => {
  // States
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [useBackupCode, setUseBackupCode] = useState(false)
  const [showBackupCode, setShowBackupCode] = useState(false)

  // Hooks
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  const { settings } = useSettings()
  const theme = useTheme()
  const hidden = useMediaQuery(theme.breakpoints.down('md'))

  // Get user info from URL params or session
  const email = searchParams.get('email') || session?.user?.email || ''
  const username = searchParams.get('username') || session?.user?.name || session?.user?.username || ''

  // Debug logging
  console.log('🔍 MFA Verification Page - URL params:', {
    email,
    username,
    sessionEmail: session?.user?.email,
    sessionName: session?.user?.name,
    sessionUsername: session?.user?.username,
    allParams: Object.fromEntries(searchParams.entries()),
    fullSession: session
  })

  // If we don't have user info, try to get it from localStorage or redirect back to login
  useEffect(() => {
    if (!email || !username) {
      console.log('❌ Missing user info for MFA verification:', { email, username })

      // Try to get from localStorage (if stored during login)
      const storedEmail = localStorage.getItem('mfa_user_email')
      const storedUsername = localStorage.getItem('mfa_user_username')

      if (!storedEmail || !storedUsername) {
        console.log('❌ No stored user info found, redirecting to login')
        router.push('/en/login')
        return
      }

      console.log('✅ Found stored user info:', { storedEmail, storedUsername })
    }
  }, [email, username, router])

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: valibotResolver(schema),
    defaultValues: {
      mfaToken: ''
    }
  })

  const characterIllustration = useImageVariant(
    mode,
    '/images/pages/auth-v2-login-illustration-light.png',
    '/images/pages/auth-v2-login-illustration-dark.png',
    '/images/pages/auth-v2-login-illustration-bordered-light.png',
    '/images/pages/auth-v2-login-illustration-bordered-dark.png'
  )

  const onSubmit = async (data) => {
    setIsLoading(true)
    setError('')

    // Get user info from URL params, session, or localStorage
    const finalEmail = email || localStorage.getItem('mfa_user_email') || ''
    const finalUsername = username || localStorage.getItem('mfa_user_username') || ''

    console.log('🔐 MFA Verification - Submitting with:', {
      username: finalUsername,
      email: finalEmail,
      mfaToken: data.mfaToken,
      step: 'mfa'
    })

    if (!finalEmail || !finalUsername) {
      setError('Missing user information. Please log in again.')
      setIsLoading(false)
      return
    }

    try {
      const res = await signIn('credentials', {
        username: finalUsername,
        email: finalEmail,
        password: 'verified', // Password already verified
        mfaToken: data.mfaToken,
        step: 'mfa',
        redirect: false
      })

      console.log('🔐 SignIn response:', res)
      console.log('🔐 SignIn response details:', {
        ok: res?.ok,
        error: res?.error,
        status: res?.status,
        url: res?.url
      })

      if (res && res.ok && !res.error) {
        console.log('✅ MFA verification successful, checking session...')

        // Clear stored user info from localStorage
        localStorage.removeItem('mfa_user_email')
        localStorage.removeItem('mfa_user_username')

        // Update session to mark MFA as verified for this session only
        try {
          const { update } = await import('next-auth/react')
          console.log('🔄 Updating session with MFA verification...')
          await update({ mfaVerified: true })
          console.log('✅ Session update completed')
        } catch (updateError) {
          console.error('❌ Session update failed:', updateError)
        }

        // Force session refresh to get updated user data
        const session = await getSession()
        console.log('🔍 Updated session after MFA:', session?.user)

        // Check if MFA was properly verified in session
        if (session?.user?.mfaVerified) {
          console.log('✅ MFA verification confirmed in session')
          setTimeout(() => {
            console.log('🚀 Redirecting to profile...')
            router.push('/en/pages/user-profile')
          }, 500)
        } else {
          console.log('⚠️ MFA verification not reflected in session, redirecting anyway...')
          // Redirect anyway since backend verification was successful
          setTimeout(() => {
            console.log('🚀 Redirecting to profile (fallback)...')
            router.push('/en/pages/user-profile')
          }, 1000)
        }
      } else {
        console.log('❌ MFA verification failed:', res?.error)
        console.log('❌ Full response object:', res)

        // Try to parse error message if it's JSON
        let errorMessage = 'Invalid MFA token. Please try again.'
        if (res?.error) {
          try {
            const errorObj = JSON.parse(res.error)
            errorMessage = errorObj.message || errorMessage
          } catch (e) {
            errorMessage = res.error
          }
        }

        setError(errorMessage)
      }
    } catch (err) {
      console.error('❌ MFA verification error:', err)
      setError('An error occurred during verification. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToLogin = () => {
    router.push('/en/pages/auth/login-v2')
  }

  return (
    <div className='flex bs-full justify-center'>
      <div className='flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden'>
        <MFAIllustration src={characterIllustration} alt='character-illustration' />
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <div className='absolute block-start-5 sm:block-start-8 inline-start-6 sm:inline-start-8'>
          <Logo />
        </div>
        <div className='flex flex-col gap-5 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset]'>
          <div>
            <Typography variant='h4'>{`Two-Factor Authentication`}</Typography>
            <Typography className='mbs-1'>
              Please enter the 6-digit code from your authenticator app
            </Typography>
            {email && (
              <Typography variant='body2' color='text.secondary' className='mbs-2'>
                Signing in as: {email}
              </Typography>
            )}
          </div>

          {error && (
            <Alert severity='error' className='mbs-2'>
              {error}
            </Alert>
          )}

          <form noValidate autoComplete='off' onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-5'>
            <Controller
              name='mfaToken'
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  autoFocus
                  fullWidth
                  type={useBackupCode && showBackupCode ? 'text' : useBackupCode ? 'password' : 'text'}
                  label={useBackupCode ? 'Backup Code' : 'Authentication Code'}
                  placeholder={useBackupCode ? 'XXXXXXXX' : '000000'}
                  onChange={(e) => {
                    const value = useBackupCode
                      ? e.target.value.toUpperCase().replace(/[^A-F0-9]/g, '').slice(0, 8)
                      : e.target.value.replace(/\D/g, '').slice(0, 6)
                    field.onChange(value)
                  }}
                  InputProps={{
                    style: {
                      textAlign: 'center',
                      fontSize: useBackupCode ? '1.2rem' : '1.5rem',
                      letterSpacing: useBackupCode ? '0.2rem' : '0.5rem'
                    },
                    endAdornment: useBackupCode ? (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowBackupCode(!showBackupCode)}
                          edge="end"
                        >
                          {showBackupCode ? '🙈' : '👁️'}
                        </IconButton>
                      </InputAdornment>
                    ) : null
                  }}
                  {...(errors.mfaToken && { error: true, helperText: errors.mfaToken.message })}
                />
              )}
            />

            <Button fullWidth variant='contained' type='submit' disabled={isLoading}>
              {isLoading ? 'Verifying...' : 'Verify'}
            </Button>

            <Divider className='gap-3'>or</Divider>

            <Box className='flex flex-col gap-2'>
              <Button
                variant='outlined'
                onClick={() => {
                  setUseBackupCode(!useBackupCode)
                  setError('')
                }}
                fullWidth
              >
                {useBackupCode ? 'Use Authenticator App' : 'Use Backup Code'}
              </Button>

              <Button
                variant='text'
                onClick={handleBackToLogin}
                fullWidth
              >
                Back to Login
              </Button>
            </Box>
          </form>

          {useBackupCode && (
            <Alert severity='warning' className='mbs-2'>
              Each backup code can only be used once. Make sure to generate new backup codes after using several of them.
            </Alert>
          )}

          <Box className='flex justify-center'>
            <Typography variant='body2' color='text.secondary'>
              Having trouble? Contact support for assistance.
            </Typography>
          </Box>
        </div>
      </div>
    </div>
  )
}

export default MFAVerificationPage
