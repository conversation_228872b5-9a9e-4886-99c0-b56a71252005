const axios = require('axios');

const BASE_URL = 'http://localhost:8090';

async function testSimpleLogin() {
    console.log('🧪 Testing Simple Login...');
    
    try {
        const response = await axios.post(`${BASE_URL}/login`, {
            username: 'dhruv',
            email: '<EMAIL>',
            password: 'testpassword123'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        console.log('✅ Login Response Status:', response.status);
        console.log('✅ Login Response:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ Login Error Status:', error.response?.status);
        console.log('❌ Login Error Response:', JSON.stringify(error.response?.data, null, 2));
        console.log('❌ Login Error Message:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Server might not be running on port 8090');
        }
    }
}

async function testServerHealth() {
    console.log('🏥 Testing Server Health...');
    
    try {
        const response = await axios.get(`${BASE_URL}/`, {
            timeout: 5000
        });
        
        console.log('✅ Server Health Status:', response.status);
        console.log('✅ Server Response:', JSON.stringify(response.data, null, 2));
        return true;
        
    } catch (error) {
        console.log('❌ Server Health Check Failed:', error.message);
        return false;
    }
}

async function runSimpleTests() {
    console.log('🚀 Running Simple Authentication Tests...');
    console.log('=' .repeat(50));
    
    // Test server health first
    const serverHealthy = await testServerHealth();
    if (!serverHealthy) {
        console.log('❌ Server is not responding. Please check if it\'s running.');
        return;
    }
    
    // Test login
    await testSimpleLogin();
    
    console.log('\n' + '=' .repeat(50));
    console.log('✅ Simple tests completed.');
}

if (require.main === module) {
    runSimpleTests();
}
