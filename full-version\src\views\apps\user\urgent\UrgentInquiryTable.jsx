'use client'

// React Imports
import React, { useState, useMemo, useEffect } from 'react'

// Next Imports
import Link from 'next/link'
import { useParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Checkbox from '@mui/material/Checkbox'
import IconButton from '@mui/material/IconButton'
import { styled } from '@mui/material/styles'
import TablePagination from '@mui/material/TablePagination'
import MenuItem from '@mui/material/MenuItem'
import Chip from '@mui/material/Chip'
import Menu from '@mui/material/Menu'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'

// Third-party Imports
import classnames from 'classnames'
import { rankItem } from '@tanstack/match-sorter-utils'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

// PDF Export
import jsPDF from 'jspdf'
// Import autotable plugin
import 'jspdf-autotable'

// Component Imports
import TableFilters from './TableFilters'
import UrgentInquiryDetailsModal from './UrgentInquiryDetailsModal'
import StatusDropdown from './StatusDropdown'
import CustomTextField from '@core/components/mui/TextField'
import CustomAvatar from '@core/components/mui/Avatar'
import TablePaginationComponent from '@components/TablePaginationComponent'

// API Imports
import { fetchUrgentInquiries, updateUrgentInquiryStatus } from '@/services/urgentApi'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

const fuzzyFilter = (row, columnId, value, addMeta) => {
  // Rank the item
  const itemRank = rankItem(row.getValue(columnId), value)

  // Store the itemRank info
  addMeta({
    itemRank
  })

  // Return if the item should be filtered in/out
  return itemRank.passed
}

const DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {
  // States
  const [value, setValue] = useState(initialValue)

  React.useEffect(() => {
    setValue(initialValue)
  }, [initialValue])

  React.useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value)
    }, debounce)

    return () => clearTimeout(timeout)
  }, [value])

  return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />
}

// Status configuration
const statusConfig = {
  pending: { label: 'Pending', color: 'warning' },
  'in-view': { label: 'In View', color: 'info' },
  completed: { label: 'Completed', color: 'success' }
}

// Styled Components
const Icon = styled('i')({})

// Column Definitions
const columnHelper = createColumnHelper()

const UrgentInquiryTable = () => {
  // States
  const [rowSelection, setRowSelection] = useState({})
  const [data, setData] = useState([])
  const [filteredData, setFilteredData] = useState([])
  const [globalFilter, setGlobalFilter] = useState('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [selectedInquiry, setSelectedInquiry] = useState(null)
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null)
  const [selectedInquiryId, setSelectedInquiryId] = useState(null)

  // Hooks
  const { lang: locale } = useParams()

  // Load urgent inquiries from backend
  const loadUrgentInquiries = async () => {
    try {
      console.log('🔄 Loading urgent inquiries from backend...')
      setLoading(true)
      setError(null)

      const inquiries = await fetchUrgentInquiries()
      console.log('✅ Fetched urgent inquiries:', inquiries.length, 'items')
      console.log('📋 Sample inquiry:', inquiries[0])
      console.log('📋 All inquiries:', inquiries)

      // Restore saved statuses from localStorage
      const savedStatuses = JSON.parse(localStorage.getItem('urgentInquiryStatuses') || '{}')
      const inquiriesWithSavedStatuses = inquiries.map(inquiry => ({
        ...inquiry,
        status: savedStatuses[inquiry.id] || inquiry.status || 'pending'
      }))

      console.log('📦 Restored urgent inquiry statuses from localStorage:', Object.keys(savedStatuses).length, 'items')

      setData(inquiriesWithSavedStatuses)
      setFilteredData(inquiriesWithSavedStatuses)
    } catch (err) {
      console.error('❌ Error loading urgent inquiries:', err)
      setError('Failed to load urgent inquiries. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Load urgent inquiries on component mount
  useEffect(() => {
    loadUrgentInquiries()
  }, [])

  // Handle status change
  const handleStatusChange = async (inquiryId, newStatus) => {
    console.log('Updating status for inquiry:', inquiryId, 'to:', newStatus)

    try {
      // Try to call API to update status in backend
      await updateUrgentInquiryStatus(inquiryId, newStatus)
      console.log('Urgent inquiry status updated in backend successfully')
    } catch (error) {
      console.error('Backend API error, using localStorage fallback:', error)

      // Fallback: Save status to localStorage for persistence
      const savedStatuses = JSON.parse(localStorage.getItem('urgentInquiryStatuses') || '{}')
      savedStatuses[inquiryId] = newStatus
      localStorage.setItem('urgentInquiryStatuses', JSON.stringify(savedStatuses))
      console.log('Urgent inquiry status saved to localStorage as fallback')
    }

    // Update local state regardless of backend success/failure
    setData(prevData =>
      prevData.map(item =>
        item.id === inquiryId
          ? { ...item, status: newStatus }
          : item
      )
    )
    setFilteredData(prevData =>
      prevData.map(item =>
        item.id === inquiryId
          ? { ...item, status: newStatus }
          : item
      )
    )

    console.log('Urgent inquiry status updated in frontend successfully')
  }

  // Handle view details
  const handleViewDetails = (inquiryData) => {
    setSelectedInquiry(inquiryData)
    setDetailsModalOpen(true)
  }

  // Handle action menu
  const handleActionMenuOpen = (event, inquiryId) => {
    setActionMenuAnchor(event.currentTarget)
    setSelectedInquiryId(inquiryId)
  }

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null)
    setSelectedInquiryId(null)
  }

  // Get selected rows data
  const getSelectedRowsData = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows
    return selectedRows.map(row => row.original)
  }

  // PDF Export function with detailed user information
  const exportSelectedToPDF = () => {
    const selectedData = getSelectedRowsData()

    console.log('=== PDF EXPORT DEBUG ===')
    console.log('Selected inquiries count:', selectedData.length)
    console.log('Selected data:', selectedData)
    console.log('Sample inquiry data:', selectedData[0])

    if (selectedData.length === 0) {
      alert('Please select at least one inquiry to export.')
      return
    }

    try {
      const doc = new jsPDF()
      let yPosition = 20

      // Add title
      doc.setFontSize(20)
      doc.setTextColor(40, 40, 40)
      doc.text('CAM Transport - Urgent Inquiries Export', 20, yPosition)
      yPosition += 15

      // Add export info
      doc.setFontSize(12)
      doc.setTextColor(100, 100, 100)
      doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 20, yPosition)
      yPosition += 8
      doc.text(`Selected Inquiries: ${selectedData.length}`, 20, yPosition)
      yPosition += 20

      // Process each selected inquiry
      selectedData.forEach((inquiry, index) => {
        // Check if we need a new page
        if (yPosition > 250) {
          doc.addPage()
          yPosition = 20
        }

        // Inquiry header
        doc.setFontSize(16)
        doc.setTextColor(220, 53, 69) // Error color for urgency
        doc.setFont(undefined, 'bold')
        doc.text(`${index + 1}. ${inquiry.fullName || 'Unknown Person'}`, 20, yPosition)
        yPosition += 12

        // Draw a line under the name
        doc.setDrawColor(220, 53, 69)
        doc.line(20, yPosition - 2, 190, yPosition - 2)
        yPosition += 8

        // Contact Information Section
        doc.setFontSize(12)
        doc.setTextColor(0, 0, 0)
        doc.setFont(undefined, 'bold')
        doc.text('Contact Information:', 20, yPosition)
        yPosition += 8

        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')

        const contactInfo = [
          `Full Name: ${inquiry.fullName || 'Not provided'}`,
          `Email: ${inquiry.email || 'Not provided'}`,
          `Phone: ${inquiry.phone || inquiry.phoneNumber || 'Not provided'}`
        ]

        contactInfo.forEach(info => {
          doc.text(info, 25, yPosition)
          yPosition += 6
        })

        yPosition += 5

        // Urgency Information Section
        doc.setFontSize(12)
        doc.setFont(undefined, 'bold')
        doc.text('Urgency Information:', 20, yPosition)
        yPosition += 8

        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')

        const urgencyInfo = [
          `Urgency Type: ${inquiry.urgencyType || inquiry.urgency_type || 'Not specified'}`,
          `Other Urgency: ${inquiry.otherUrgency || inquiry.other_urgency || 'Not provided'}`,
          `Reference Number: ${inquiry.refNumber || inquiry.ref_number || 'Not provided'}`,
          `Brief Description: ${inquiry.briefDescription || inquiry.brief_description || 'Not provided'}`,
          `Documents: ${inquiry.documents ? 'Uploaded' : 'Not uploaded'}`
        ]

        urgencyInfo.forEach(info => {
          doc.text(info, 25, yPosition)
          yPosition += 6
        })

        yPosition += 5

        // Inquiry Details Section
        doc.setFontSize(12)
        doc.setFont(undefined, 'bold')
        doc.text('Inquiry Details:', 20, yPosition)
        yPosition += 8

        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')

        // Format date with time for PDF (using actual submission time from database)
        const formatDateTimeForPDF = (dateString) => {
          if (!dateString) return 'Not available'
          try {
            // Parse the actual timestamp from database (createdAt field)
            const date = new Date(dateString)
            if (isNaN(date.getTime())) return dateString

            const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
            const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }

            const formattedDate = date.toLocaleDateString('en-US', dateOptions)
            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

            return `${formattedDate} ${formattedTime}`
          } catch (error) {
            return dateString
          }
        }

        const inquiryDetails = [
          `Submitted Date: ${formatDateTimeForPDF(inquiry.submittedDate || inquiry.createdAt)}`,
          `Current Status: ${statusConfig[inquiry.status || 'pending']?.label || 'Pending'}`,
          `IP Address: ${inquiry.ip || 'Not recorded'}`
        ]

        inquiryDetails.forEach(info => {
          doc.text(info, 25, yPosition)
          yPosition += 6
        })

        // Add separator line between inquiries
        yPosition += 10
        doc.setDrawColor(200, 200, 200)
        doc.line(20, yPosition, 190, yPosition)
        yPosition += 15
      })

      // Save the PDF
      const fileName = `CAM_Transport_Urgent_Inquiries_${new Date().toISOString().split('T')[0]}.pdf`
      doc.save(fileName)

      alert(`Successfully exported ${selectedData.length} inquiry(ies) with full details to ${fileName}`)

    } catch (error) {
      console.error('PDF Export Error:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  const columns = useMemo(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      columnHelper.accessor('fullName', {
        header: 'Contact Person',
        cell: ({ row }) => (
          <div className='flex items-center gap-4'>
            <CustomAvatar
              variant='rounded'
              color='error'
              skin='light'
              size={34}
            >
              {row.original.fullName?.charAt(0)?.toUpperCase()}
            </CustomAvatar>
            <div className='flex flex-col'>
              <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                {row.original.fullName}
              </Typography>
              <Typography variant='body1' color='text.primary' className='font-medium' style={{ fontSize: '1rem', letterSpacing: '1px' }}>
                {row.original.email}
              </Typography>
            </div>
          </div>
        )
      }),
      columnHelper.accessor('phone', {
        header: 'Contact',
        cell: ({ row }) => (
          <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
            {row.original.phone || row.original.phoneNumber || 'Not provided'}
          </Typography>
        )
      }),
      columnHelper.accessor('urgencyType', {
        header: 'Type of Urgency',
        cell: ({ row }) => {
          const urgencyType = row.original.urgency_type || row.original.urgencyType || 'Emergency'
          const urgencyColors = {
            'shipment delay': 'warning',
            'vehicle breakdown': 'error',
            'delivery issue': 'error',
            'lost/damaged cargo': 'error',
            'delivery refusal': 'warning',
            'other(please specify)': 'info'
          }
          return (
            <Chip
              variant='tonal'
              label={urgencyType}
              size='small'
              color={urgencyColors[urgencyType.toLowerCase()] || 'error'}
              className='capitalize'
            />
          )
        }
      }),
      columnHelper.accessor('submittedDate', {
        header: 'Submitted Date',
        cell: ({ row }) => {
          const formatDateTime = (dateString) => {
            if (!dateString) return 'Not available'

            try {
              // Handle both ISO string and already formatted dates (using actual submission time from database)
              const date = new Date(dateString)
              if (isNaN(date.getTime())) return dateString // Return original if invalid date

              // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)
              const dateOptions = {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric'
              }
              const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              }

              const formattedDate = date.toLocaleDateString('en-US', dateOptions)
              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

              return `${formattedDate} ${formattedTime}`
            } catch (error) {
              return dateString // Return original if formatting fails
            }
          }

          return (
            <div className='flex flex-col'>
              <Typography color='text.primary' style={{ fontSize: '0.95rem', lineHeight: '1.2' }}>
                {formatDateTime(row.original.submittedDate || row.original.createdAt)}
              </Typography>
            </div>
          )
        }
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: ({ row }) => {
          const status = row.original.status || 'pending'

          return (
            <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>
              <StatusDropdown
                currentStatus={status}
                onStatusChange={handleStatusChange}
                inquiryId={row.original.id}
              />
            </div>
          )
        },
        enableSorting: false
      }),
      columnHelper.accessor('action', {
        header: 'Action',
        cell: ({ row }) => (
          <div className='flex items-center gap-1'>
            {/* Delete */}
            <IconButton
              onClick={() => handleDeleteInquiry(row.original.id)}
              title="Delete Inquiry"
              size='small'
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'error.main',
                  backgroundColor: 'error.light',
                  transform: 'scale(1.1)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className='tabler-trash' />
            </IconButton>

            {/* View Details */}
            <IconButton
              onClick={() => handleViewDetails(row.original)}
              title="View Details"
              size='small'
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'info.main',
                  backgroundColor: 'info.light',
                  transform: 'scale(1.1)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className='tabler-eye' />
            </IconButton>

            {/* Three Dots Menu */}
            <IconButton
              onClick={(e) => handleActionMenuOpen(e, row.original.id)}
              title="More Actions"
              size='small'
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  backgroundColor: 'primary.light',
                  transform: 'scale(1.1)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className='tabler-dots-vertical' />
            </IconButton>
          </div>
        ),
        enableSorting: false
      })
    ],
    [data, filteredData]
  )

  const table = useReactTable({
    data: filteredData,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter
    },
    state: {
      rowSelection,
      globalFilter
    },
    initialState: {
      pagination: {
        pageSize: 10
      }
    },
    enableRowSelection: true,
    globalFilterFn: fuzzyFilter,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  if (loading) {
    return (
      <Card>
        <div className='flex justify-center items-center p-6'>
          <CircularProgress />
          <Typography className='ml-2'>Loading urgent inquiries...</Typography>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <div className='p-6'>
          <Alert severity='error' className='mb-4'>
            {error}
          </Alert>
          <Button onClick={loadUrgentInquiries} variant='contained'>
            Retry
          </Button>
        </div>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader title='Urgent Inquiry Filters' className='pbe-4' />
        <TableFilters setData={setFilteredData} tableData={data} />
        <div className='flex justify-between flex-col items-start md:flex-row md:items-center p-6 border-bs gap-4'>
          <CustomTextField
            select
            value={table.getState().pagination.pageSize}
            onChange={e => table.setPageSize(Number(e.target.value))}
            className='max-sm:is-full sm:is-[70px]'
          >
            <MenuItem value='10'>10</MenuItem>
            <MenuItem value='25'>25</MenuItem>
            <MenuItem value='50'>50</MenuItem>
          </CustomTextField>
          <div className='flex flex-col sm:flex-row max-sm:is-full items-start sm:items-center gap-4'>
            <DebouncedInput
              value={globalFilter ?? ''}
              onChange={value => setGlobalFilter(String(value))}
              placeholder='Search Urgent Inquiries'
              className='max-sm:is-full'
            />
            <Button
              color='secondary'
              variant='tonal'
              startIcon={<i className='tabler-file-type-pdf' />}
              onClick={exportSelectedToPDF}
              disabled={Object.keys(rowSelection).length === 0}
              className='max-sm:is-full'
              sx={{
                '&:disabled': {
                  opacity: 0.5,
                  cursor: 'not-allowed'
                }
              }}
            >
              Export PDF ({Object.keys(rowSelection).length})
            </Button>

            <IconButton
              color='primary'
              onClick={loadUrgentInquiries}
              disabled={loading}
              title={loading ? 'Loading...' : 'Refresh Data'}
              sx={{
                border: '1px solid',
                borderColor: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  transform: 'scale(1.05)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />
            </IconButton>
          </div>
        </div>
        <div className='overflow-x-auto'>
          <table className={tableStyles.table}>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th key={header.id}>
                      {header.isPlaceholder ? null : (
                        <>
                          <div
                            className={classnames({
                              'flex items-center': header.column.getIsSorted(),
                              'cursor-pointer select-none': header.column.getCanSort()
                            })}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(header.column.columnDef.header, header.getContext())}
                            {{
                              asc: <i className='tabler-chevron-up text-xl' />,
                              desc: <i className='tabler-chevron-down text-xl' />
                            }[header.column.getIsSorted()] ?? null}
                          </div>
                        </>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            {table.getFilteredRowModel().rows.length === 0 ? (
              <tbody>
                <tr>
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>
                    No data available
                  </td>
                </tr>
              </tbody>
            ) : (
              <tbody>
                {table
                  .getRowModel()
                  .rows.slice(0, table.getState().pagination.pageSize)
                  .map(row => {
                    return (
                      <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>
                        {row.getVisibleCells().map(cell => (
                          <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
                        ))}
                      </tr>
                    )
                  })}
              </tbody>
            )}
          </table>
        </div>
        <TablePagination
          component={() => <TablePaginationComponent table={table} />}
          count={table.getFilteredRowModel().rows.length}
          rowsPerPage={table.getState().pagination.pageSize}
          page={table.getState().pagination.pageIndex}
          onPageChange={(_, page) => {
            table.setPageIndex(page)
          }}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        <MenuItem
          onClick={() => {
            const inquiry = data.find(item => item.id === selectedInquiryId)
            if (inquiry?.documents) {
              handleDownloadDocument(inquiry)
            } else {
              alert('No document available for this inquiry.')
            }
            handleActionMenuClose()
          }}
          disabled={!data.find(item => item.id === selectedInquiryId)?.documents}
        >
          <i className='tabler-download mr-2' />
          {data.find(item => item.id === selectedInquiryId)?.documents ? 'Download Document' : 'No Document Available'}
        </MenuItem>
      </Menu>

      <UrgentInquiryDetailsModal
        open={detailsModalOpen}
        onClose={() => setDetailsModalOpen(false)}
        inquiryData={selectedInquiry}
      />
    </>
  )
}

export default UrgentInquiryTable
