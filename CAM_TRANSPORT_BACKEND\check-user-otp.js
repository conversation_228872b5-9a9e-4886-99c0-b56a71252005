const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function checkUserOTP() {
    try {
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log('🔗 Connected to MongoDB');
        
        // Find the user
        const user = await Login.findOne({
            email: '<EMAIL>'
        });
        
        if (!user) {
            console.log('❌ User not found');
            return;
        }
        
        console.log('👤 User found:');
        console.log(`Email: ${user.email}`);
        console.log(`Username: ${user.username}`);
        console.log(`Current OTP: ${user.otp || 'NO OTP STORED'}`);
        console.log(`OTP Expiry: ${user.otpExpiry || 'NO EXPIRY SET'}`);
        console.log(`Current Time: ${new Date()}`);
        
        if (user.otp && user.otpExpiry) {
            const isExpired = new Date() > user.otpExpiry;
            console.log(`OTP Status: ${isExpired ? '❌ EXPIRED' : '✅ VALID'}`);
            
            if (isExpired) {
                const expiredMinutesAgo = Math.floor((new Date() - user.otpExpiry) / (1000 * 60));
                console.log(`Expired ${expiredMinutesAgo} minutes ago`);
            } else {
                const expiresInMinutes = Math.floor((user.otpExpiry - new Date()) / (1000 * 60));
                console.log(`Expires in ${expiresInMinutes} minutes`);
            }
        }
        
        // Generate a fresh OTP for testing
        console.log('\n🔧 Generating fresh OTP...');
        const newOtp = Math.floor(100000 + Math.random() * 900000);
        const newExpiry = new Date(Date.now() + 10 * 60 * 1000);
        
        user.otp = newOtp;
        user.otpExpiry = newExpiry;
        await user.save();
        
        console.log(`✅ New OTP generated: ${newOtp}`);
        console.log(`✅ Expires at: ${newExpiry}`);
        console.log('\n📧 You can now use this OTP to test verification!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
    }
}

checkUserOTP();
