const axios = require('axios');

async function verifyEmailOTPFlow() {
    console.log('🧪 Testing Complete Email OTP Flow...');
    console.log('=' .repeat(50));
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    console.log('Step 1: Attempting login (should trigger Email OTP)...');
    console.log(`Username: ${credentials.username}`);
    console.log(`Email: ${credentials.email}`);
    console.log(`Password: ${credentials.password}`);
    
    try {
        const loginResponse = await axios.post('http://localhost:8090/login', credentials, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('\n✅ Login API Response:');
        console.log('Status:', loginResponse.status);
        console.log('Response Data:', JSON.stringify(loginResponse.data, null, 2));
        
        if (loginResponse.data.requiresEmailOTP) {
            console.log('\n🎯 SUCCESS: Email OTP is required!');
            console.log('📧 An OTP should have been sent to:', credentials.email);
            console.log('👤 User ID for verification:', loginResponse.data.user.id);
            
            console.log('\n📋 Next Steps:');
            console.log('1. Check your email for the 6-digit OTP code');
            console.log('2. Use the frontend at: http://localhost:3000/pages/auth/email-otp-verify');
            console.log('3. Or use this API endpoint to verify:');
            console.log('   POST http://localhost:8090/login/verify-email-otp');
            console.log('   Body: { "userId": "' + loginResponse.data.user.id + '", "otp": "YOUR_6_DIGIT_CODE" }');
            
            return {
                success: true,
                requiresEmailOTP: true,
                userId: loginResponse.data.user.id,
                email: credentials.email
            };
            
        } else if (loginResponse.data.requiresMFA) {
            console.log('\n🔐 User requires MFA (TOTP) verification');
            return {
                success: true,
                requiresMFA: true,
                userId: loginResponse.data.user.id
            };
            
        } else {
            console.log('\n⚠️ WARNING: Login completed without additional verification!');
            console.log('This should not happen for users without MFA enabled.');
            return {
                success: true,
                noAdditionalVerification: true
            };
        }
        
    } catch (error) {
        console.log('\n❌ Login Failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
            
            if (error.response.status === 401) {
                console.log('\n💡 Analysis: User credentials are incorrect or user doesn\'t exist');
            }
        } else {
            console.log('Network Error:', error.message);
        }
        
        return {
            success: false,
            error: error.response?.data || error.message
        };
    }
}

async function testOTPVerification(userId, otp) {
    console.log('\n🧪 Testing OTP Verification...');
    console.log(`User ID: ${userId}`);
    console.log(`OTP: ${otp}`);
    
    try {
        const response = await axios.post('http://localhost:8090/login/verify-email-otp', {
            userId: userId,
            otp: parseInt(otp)
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ OTP Verification Success!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        return true;
        
    } catch (error) {
        console.log('❌ OTP Verification Failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error:', JSON.stringify(error.response.data, null, 2));
        }
        return false;
    }
}

if (require.main === module) {
    verifyEmailOTPFlow().then(result => {
        console.log('\n' + '=' .repeat(50));
        
        if (result.success && result.requiresEmailOTP) {
            console.log('🎉 EMAIL OTP FLOW IS WORKING CORRECTLY!');
            console.log('✅ Login triggers Email OTP requirement');
            console.log('✅ OTP should be sent to your email');
            console.log('✅ Frontend will redirect to OTP verification page');
            console.log('\n📧 Please check your email and test the complete flow!');
        } else if (result.success && result.requiresMFA) {
            console.log('🔐 MFA flow is working (TOTP required)');
        } else if (result.success && result.noAdditionalVerification) {
            console.log('⚠️ WARNING: No additional verification required');
            console.log('This might indicate the user has MFA disabled but the flow is not working correctly.');
        } else {
            console.log('❌ Email OTP flow is not working correctly');
        }
        
        process.exit(0);
    }).catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
