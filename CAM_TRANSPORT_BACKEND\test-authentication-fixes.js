const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Base URL for the API
const BASE_URL = 'http://localhost:8090';

// Test configuration
const TEST_CONFIG = {
    // Test user credentials
    testUser: {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'testpassword123'
    },
    // Admin credentials for testing
    adminUser: {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin'
    }
};

// Helper function to make API requests
async function makeRequest(method, endpoint, data = null, headers = {}) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: {
                'Content-Type': 'application/json',
                ...headers
            }
        };

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        return { success: true, data: response.data, status: response.status };
    } catch (error) {
        return {
            success: false,
            error: error.response?.data || error.message,
            status: error.response?.status || 500
        };
    }
}

// Test functions
async function testCreateUser() {
    console.log('\n🧪 Testing User Creation...');
    
    const result = await makeRequest('POST', '/user-profile/users/create', {
        username: TEST_CONFIG.testUser.username,
        email: TEST_CONFIG.testUser.email,
        password: TEST_CONFIG.testUser.password,
        role: 'admin'
    });

    if (result.success) {
        console.log('✅ User creation successful:', result.data.user?.email);
        return result.data.user;
    } else {
        console.log('❌ User creation failed:', result.error);
        return null;
    }
}

async function testUserLogin() {
    console.log('\n🧪 Testing User Login (Fixed Authentication)...');
    
    const result = await makeRequest('POST', '/login', {
        username: TEST_CONFIG.testUser.username,
        email: TEST_CONFIG.testUser.email,
        password: TEST_CONFIG.testUser.password
    });

    if (result.success) {
        console.log('✅ User login successful:', result.data.message);
        console.log('📧 Requires email verification:', result.data.requiresEmailVerification);
        console.log('📧 Requires email OTP:', result.data.requiresEmailOTP);
        console.log('🔐 Requires MFA:', result.data.requiresMFA);
        return result.data.user;
    } else {
        console.log('❌ User login failed:', result.error);
        return null;
    }
}

async function testAdminLogin() {
    console.log('\n🧪 Testing Admin Login...');
    
    const result = await makeRequest('POST', '/login', {
        username: TEST_CONFIG.adminUser.username,
        email: TEST_CONFIG.adminUser.email,
        password: TEST_CONFIG.adminUser.password
    });

    if (result.success) {
        console.log('✅ Admin login successful:', result.data.message);
        console.log('🔐 Requires MFA:', result.data.requiresMFA);
        return result.data.user;
    } else {
        console.log('❌ Admin login failed:', result.error);
        return null;
    }
}

async function testGetAllUsers(adminUserId) {
    console.log('\n🧪 Testing Get All Users...');
    
    const result = await makeRequest('GET', `/user-profile/users?userId=${adminUserId}`);

    if (result.success) {
        console.log('✅ Get all users successful. Found:', result.data.users?.length, 'users');
        return result.data.users;
    } else {
        console.log('❌ Get all users failed:', result.error);
        return null;
    }
}

async function testUpdateUser(adminUserId, targetUserId) {
    console.log('\n🧪 Testing Update User...');
    
    const result = await makeRequest('PUT', `/user-profile/users/${targetUserId}?userId=${adminUserId}`, {
        username: 'dhruv_updated',
        email: '<EMAIL>',
        role: 'admin'
    });

    if (result.success) {
        console.log('✅ Update user successful:', result.data.user?.email);
        return result.data.user;
    } else {
        console.log('❌ Update user failed:', result.error);
        return null;
    }
}

async function testToggleUserStatus(adminUserId, targetUserId) {
    console.log('\n🧪 Testing Toggle User Status...');
    
    const result = await makeRequest('PATCH', `/user-profile/users/${targetUserId}/toggle-status?userId=${adminUserId}`);

    if (result.success) {
        console.log('✅ Toggle user status successful:', result.data.message);
        return result.data.user;
    } else {
        console.log('❌ Toggle user status failed:', result.error);
        return null;
    }
}

async function testEmailOTPVerification(userId, otp) {
    console.log('\n🧪 Testing Email OTP Verification...');
    
    const result = await makeRequest('POST', '/login/verify-email-otp', {
        userId: userId,
        otp: otp
    });

    if (result.success) {
        console.log('✅ Email OTP verification successful:', result.data.message);
        return result.data.user;
    } else {
        console.log('❌ Email OTP verification failed:', result.error);
        return null;
    }
}

// Main test function
async function runTests() {
    console.log('🚀 Starting CAM Transport Authentication & User Management Tests...');
    console.log('=' .repeat(60));

    try {
        // Test 1: Create a test user
        const createdUser = await testCreateUser();
        if (!createdUser) {
            console.log('❌ Cannot proceed without creating a user');
            return;
        }

        // Test 2: Test user login (should work now with fixed authentication)
        const loginResult = await testUserLogin();
        if (!loginResult) {
            console.log('❌ User login failed - authentication fix may not be working');
            return;
        }

        // Test 3: Test admin login
        const adminLoginResult = await testAdminLogin();
        if (!adminLoginResult) {
            console.log('❌ Admin login failed');
            return;
        }

        // Test 4: Test user management operations
        const users = await testGetAllUsers(adminLoginResult.id);
        if (users && users.length > 0) {
            const testUser = users.find(u => u.email === TEST_CONFIG.testUser.email);
            if (testUser) {
                // Test update user
                await testUpdateUser(adminLoginResult.id, testUser.id);
                
                // Test toggle user status
                await testToggleUserStatus(adminLoginResult.id, testUser.id);
            }
        }

        console.log('\n' + '=' .repeat(60));
        console.log('🎉 All tests completed! Check the results above.');
        
    } catch (error) {
        console.error('❌ Test execution failed:', error);
    }
}

// Run the tests
if (require.main === module) {
    runTests().then(() => {
        console.log('\n✅ Test script finished.');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Test script failed:', error);
        process.exit(1);
    });
}

module.exports = { runTests };
