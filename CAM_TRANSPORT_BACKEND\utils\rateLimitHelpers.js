const Contact = require('../model/contact');
const Jobs = require('../model/jobs');
const Urgent = require('../model/urgent_Inquiry');

/**
 * Checks if an email is currently blocked based on the 24-hour, 15-submission limit (for Contact).
 * @param {string} email The email address to check.
 * @param {Date} now The current date/time.
 * @param {Date} oneDayAgo The date/time 24 hours ago.
 * @returns {Promise<{isBlocked: boolean, unblockDate: Date | null}>} An object indicating if blocked and the unblock date.
 */
async function checkEmail24hBlock(email, now, oneDayAgo) {
    const recent24hEmailContacts = await Contact.find({
        email: email.trim(),
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    if (recent24hEmailContacts.length >= 15) {
        const oldestEmail24h = recent24hEmailContacts[0].createdAt;
        const unblockDate24h = new Date(oldestEmail24h.getTime() + 24 * 60 * 60 * 1000);
        if (now < unblockDate24h) {
            return { isBlocked: true, unblockDate: unblockDate24h };
        }
    }
    return { isBlocked: false, unblockDate: null };
}

/**
 * Checks if an IP address is currently blocked based on the 24-hour, 5-submission limit (for Contact).
 * @param {string} ip The IP address to check.
 * @param {Date} now The current date/time.
 * @param {Date} oneDayAgo The date/time 24 hours ago.
 * @returns {Promise<{isBlocked: boolean, unblockDate: Date | null}>} An object indicating if blocked and the unblock date.
 */
async function checkIp24hBlock(ip, now, oneDayAgo) {
    const recentIpContacts = await Contact.find({
        ip,
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    if (recentIpContacts.length >= 5) {
        const oldestIp = recentIpContacts[0].createdAt;
        const unblockDateIp = new Date(oldestIp.getTime() + 24 * 60 * 60 * 1000);
        if (now < unblockDateIp) {
            return { isBlocked: true, unblockDate: unblockDateIp };
        }
    }
    return { isBlocked: false, unblockDate: null };
}

/**
 * Checks if an email is currently blocked based on the 24-hour, 3-submission limit (for Jobs).
 * @param {string} email The email address to check.
 * @param {Date} now The current date/time.
 * @param {Date} oneDayAgo The date/time 24 hours ago.
 * @returns {Promise<{isBlocked: boolean, unblockDate: Date | null}>} An object indicating if blocked and the unblock date.
 */
async function checkJobsEmail24hBlock(email, now, oneDayAgo) {
    const recent24hEmailJobs = await Jobs.find({
        email: email.trim(),
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    if (recent24hEmailJobs.length >= 3) {
        const oldestEmail24h = recent24hEmailJobs[0].createdAt;
        const unblockDate24h = new Date(oldestEmail24h.getTime() + 24 * 60 * 60 * 1000);
        if (now < unblockDate24h) {
            return { isBlocked: true, unblockDate: unblockDate24h };
        }
    }
    return { isBlocked: false, unblockDate: null };
}

/**
 * Checks if an IP address is currently blocked based on the 24-hour, 3-submission limit (for Jobs).
 * @param {string} ip The IP address to check.
 * @param {Date} now The current date/time.
 * @param {Date} oneDayAgo The date/time 24 hours ago.
 * @returns {Promise<{isBlocked: boolean, unblockDate: Date | null}>} An object indicating if blocked and the unblock date.
 */
async function checkJobsIp24hBlock(ip, now, oneDayAgo) {
    const recentIpJobs = await Jobs.find({
        ip,
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    if (recentIpJobs.length >= 3) {
        const oldestIp = recentIpJobs[0].createdAt;
        const unblockDateIp = new Date(oldestIp.getTime() + 24 * 60 * 60 * 1000);
        if (now < unblockDateIp) {
            return { isBlocked: true, unblockDate: unblockDateIp };
        }
    }
    return { isBlocked: false, unblockDate: null };
}

/**
 * Checks if an email is currently blocked based on the 24-hour, 15-submission limit (for Urgent Inquiry).
 * @param {string} email The email address to check.
 * @param {Date} now The current date/time.
 * @param {Date} oneDayAgo The date/time 24 hours ago.
 * @returns {Promise<{isBlocked: boolean, unblockDate: Date | null}>} An object indicating if blocked and the unblock date.
 */
async function checkUrgentEmail24hBlock(email, now, oneDayAgo) {
    const recent24hEmailUrgent = await Urgent.find({
        email: email.trim(),
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    if (recent24hEmailUrgent.length >= 15) {
        const oldestEmail24h = recent24hEmailUrgent[0].createdAt;
        const unblockDate24h = new Date(oldestEmail24h.getTime() + 24 * 60 * 60 * 1000);
        if (now < unblockDate24h) {
            return { isBlocked: true, unblockDate: unblockDate24h };
        }
    }
    return { isBlocked: false, unblockDate: null };
}

/**
 * Checks if an IP address is currently blocked based on the 24-hour, 5-submission limit (for Urgent Inquiry).
 * @param {string} ip The IP address to check.
 * @param {Date} now The current date/time.
 * @param {Date} oneDayAgo The date/time 24 hours ago.
 * @returns {Promise<{isBlocked: boolean, unblockDate: Date | null}>} An object indicating if blocked and the unblock date.
 */
async function checkUrgentIp24hBlock(ip, now, oneDayAgo) {
    const recentIpUrgent = await Urgent.find({
        ip,
        createdAt: { $gte: oneDayAgo }
    }).sort({ createdAt: 1 });

    if (recentIpUrgent.length >= 5) {
        const oldestIp = recentIpUrgent[0].createdAt;
        const unblockDateIp = new Date(oldestIp.getTime() + 24 * 60 * 60 * 1000);
        if (now < unblockDateIp) {
            return { isBlocked: true, unblockDate: unblockDateIp };
        }
    }
    return { isBlocked: false, unblockDate: null };
}

module.exports = {
    checkEmail24hBlock,
    checkIp24hBlock,
    checkJobsEmail24hBlock,
    checkJobsIp24hBlock,
    checkUrgentEmail24hBlock,
    checkUrgentIp24hBlock,
}; 