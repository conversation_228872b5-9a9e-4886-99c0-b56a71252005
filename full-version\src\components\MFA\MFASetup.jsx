'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>alog<PERSON>ctions,
  <PERSON>ton,
  <PERSON>po<PERSON>,
  Box,
  TextField,
  Stepper,
  Step,
  StepLabel,
  Alert,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material'
import { ContentCopy, Download, Security, Smartphone, Key } from '@mui/icons-material'
import { generateMFASetup, verifyMFASetup } from '@/services/mfaApi'

const MFASetup = ({ open, onClose, userId, onSuccess, isAddingDevice = false }) => {
  const [activeStep, setActiveStep] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [mfaData, setMfaData] = useState(null)
  const [verificationToken, setVerificationToken] = useState('')
  const [backupCodes, setBackupCodes] = useState([])
  const [deviceName, setDeviceName] = useState('')

  const steps = isAddingDevice
    ? ['Device Info', 'Scan QR Code', 'Verify Setup', 'Complete']
    : ['Generate Setup', 'Scan QR Code', 'Verify Setup', 'Save Backup Codes']

  const handleGenerateSetup = async () => {
    try {
      setLoading(true)
      setError('')

      const deviceNameToUse = isAddingDevice ? deviceName || 'New Device' : 'Authenticator App'
      const result = await generateMFASetup(userId, deviceNameToUse)
      setMfaData(result.data)
      setActiveStep(isAddingDevice ? 1 : 1)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleVerifySetup = async () => {
    if (!verificationToken || verificationToken.length !== 6) {
      setError('Please enter a valid 6-digit code')
      return
    }

    if (!mfaData?.deviceId) {
      setError('Device setup data is missing. Please restart the setup.')
      return
    }

    try {
      setLoading(true)
      setError('')

      const result = await verifyMFASetup(userId, verificationToken, mfaData.deviceId)

      if (result.data.isFirstDevice) {
        setBackupCodes(result.data.backupCodes)
      }

      setActiveStep(3)

      // Show success message
      if (onSuccess) {
        const message = isAddingDevice
          ? `Device "${mfaData.deviceName}" has been successfully added!`
          : 'MFA has been successfully enabled for your account!'
        onSuccess(message)
      }
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  const downloadBackupCodes = () => {
    const content = `CAM Transport - MFA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\n\nBackup Codes:\n${backupCodes.join('\n')}\n\nImportant:\n- Keep these codes safe and secure\n- Each code can only be used once\n- Use these codes if you lose access to your authenticator app\n- Generate new codes if you suspect these have been compromised`

    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'cam-transport-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleClose = () => {
    setActiveStep(0)
    setMfaData(null)
    setVerificationToken('')
    setBackupCodes([])
    setError('')
    onClose()
  }

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        if (isAddingDevice) {
          return (
            <Box sx={{ py: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Smartphone /> Add New Device
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Give your new authenticator device a name to help you identify it later.
              </Typography>

              <TextField
                fullWidth
                label="Device Name"
                value={deviceName}
                onChange={(e) => setDeviceName(e.target.value)}
                placeholder="e.g., iPhone, Work Phone, Personal Tablet"
                sx={{ mb: 3 }}
              />

              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  onClick={handleGenerateSetup}
                  disabled={loading}
                >
                  {loading ? 'Generating...' : 'Continue'}
                </Button>
              </Box>
            </Box>
          )
        }

        return (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Security sx={{ fontSize: 64, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Enable Two-Factor Authentication
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Add an extra layer of security to your account by enabling MFA.
              You'll need an authenticator app like Google Authenticator or Authy.
            </Typography>
            <Button
              variant="contained"
              onClick={handleGenerateSetup}
              disabled={loading}
              size="large"
            >
              {loading ? 'Generating...' : 'Get Started'}
            </Button>
          </Box>
        )

      case 1:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Smartphone /> Scan QR Code
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Open your authenticator app and scan this QR code:
            </Typography>

            {mfaData && (
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <img
                  src={mfaData.qrCode}
                  alt="MFA QR Code"
                  style={{ maxWidth: '200px', border: '1px solid #ddd', borderRadius: '8px' }}
                />
              </Box>
            )}

            <Alert severity="info" sx={{ mb: 2 }}>
              Can't scan the QR code? Enter this key manually in your authenticator app:
            </Alert>

            {mfaData && (
              <Card variant="outlined" sx={{ mb: 3 }}>
                <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                    {mfaData.manualEntryKey}
                  </Typography>
                  <Tooltip title="Copy to clipboard">
                    <IconButton onClick={() => copyToClipboard(mfaData.manualEntryKey)}>
                      <ContentCopy />
                    </IconButton>
                  </Tooltip>
                </CardContent>
              </Card>
            )}

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button onClick={() => setActiveStep(0)}>
                Back
              </Button>
              <Button variant="contained" onClick={() => setActiveStep(2)}>
                I've Added the Account
              </Button>
            </Box>
          </Box>
        )

      case 2:
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Key /> Verify Setup
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Enter the 6-digit code from your authenticator app to verify the setup:
            </Typography>

            <TextField
              fullWidth
              label="Verification Code"
              value={verificationToken}
              onChange={(e) => setVerificationToken(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="000000"
              inputProps={{
                style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' },
                maxLength: 6
              }}
              sx={{ mb: 3 }}
            />

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button onClick={() => setActiveStep(1)}>
                Back
              </Button>
              <Button
                variant="contained"
                onClick={handleVerifySetup}
                disabled={loading || verificationToken.length !== 6}
              >
                {loading ? 'Verifying...' : 'Verify & Enable MFA'}
              </Button>
            </Box>
          </Box>
        )

      case 3:
        if (isAddingDevice) {
          return (
            <Box sx={{ py: 2, textAlign: 'center' }}>
              <Security sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Device Added Successfully!
              </Typography>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 Your device "{mfaData?.deviceName}" has been added to your account!
              </Alert>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                You can now use this device for two-factor authentication when logging in.
              </Typography>

              <Button variant="contained" onClick={handleClose}>
                Done
              </Button>
            </Box>
          )
        }

        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Download /> Save Backup Codes
            </Typography>
            <Alert severity="success" sx={{ mb: 2 }}>
              🎉 MFA has been successfully enabled for your account!
            </Alert>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Save these backup codes in a safe place. You can use them to access your account if you lose your authenticator device.
            </Typography>

            {backupCodes.length > 0 && (
              <Card variant="outlined" sx={{ mb: 3 }}>
                <CardContent>
                  <Grid container spacing={1}>
                    {backupCodes.map((code, index) => (
                      <Grid item xs={6} key={index}>
                        <Chip
                          label={code}
                          variant="outlined"
                          sx={{ fontFamily: 'monospace', width: '100%' }}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </CardContent>
              </Card>
            )}

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-between' }}>
              {backupCodes.length > 0 && (
                <Button
                  variant="outlined"
                  startIcon={<Download />}
                  onClick={downloadBackupCodes}
                >
                  Download Codes
                </Button>
              )}
              <Button variant="contained" onClick={handleClose}>
                Complete Setup
              </Button>
            </Box>
          </Box>
        )

      default:
        return null
    }
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Security />
          Multi-Factor Authentication Setup
        </Box>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {renderStepContent()}
      </DialogContent>
    </Dialog>
  )
}

export default MFASetup
