// Next Imports
import { NextResponse } from 'next/server'

export async function GET(req) {
  try {
    // Get the URL parameters
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')
    const token = searchParams.get('token')

    if (!userId || !token) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters' },
        { status: 400 }
      )
    }

    // Forward the request to your backend
    const backendUrl = process.env.API_URL || 'http://localhost:8090'
    const verifyUrl = `${backendUrl}/verify-email`

    const response = await fetch(verifyUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ userId, token })
    })

    if (response.ok) {
      return NextResponse.json(
        { success: true, message: 'Email verified successfully' },
        { status: 200 }
      )
    } else {
      const errorData = await response.json().catch(() => ({}))
      return NextResponse.json(
        { success: false, message: errorData.message || 'Email verification failed' },
        { status: response.status }
      )
    }
  } catch (error) {
    console.error('Error verifying email:', error)
    return NextResponse.json(
      { success: false, message: 'An error occurred while verifying email' },
      { status: 500 }
    )
  }
}
