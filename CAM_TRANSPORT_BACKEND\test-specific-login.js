const axios = require('axios');

async function testSpecificLogin() {
    console.log('🧪 Testing login with your specific credentials...');
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    console.log('Credentials being tested:');
    console.log(`Username: ${credentials.username}`);
    console.log(`Email: ${credentials.email}`);
    console.log(`Password: ${credentials.password}`);
    
    try {
        const response = await axios.post('http://localhost:8090/login', credentials, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('\n✅ Login Success!');
        console.log('Status:', response.status);
        console.log('Response:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('\n❌ Login Failed!');
        
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
            
            // Analyze the error
            if (error.response.status === 401) {
                console.log('\n🔍 Analysis: 401 Unauthorized');
                console.log('This means either:');
                console.log('1. User does not exist in database');
                console.log('2. Password is incorrect');
                console.log('3. User account is deactivated');
            } else if (error.response.status === 400) {
                console.log('\n🔍 Analysis: 400 Bad Request');
                console.log('This means validation failed (missing email/password)');
            } else if (error.response.status === 500) {
                console.log('\n🔍 Analysis: 500 Internal Server Error');
                console.log('This means there\'s a server-side error');
            }
            
        } else {
            console.log('Network Error:', error.message);
        }
    }
    
    // Also test with just email (no username)
    console.log('\n🧪 Testing with email only...');
    
    try {
        const response = await axios.post('http://localhost:8090/login', {
            email: credentials.email,
            password: credentials.password
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ Email-only login Success!');
        console.log('Response:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ Email-only login Failed!');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

if (require.main === module) {
    testSpecificLogin().then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
