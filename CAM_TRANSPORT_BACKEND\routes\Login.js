const { Router } = require('express');
const { ValidateLogin } = require('../middleware/Login_Validator');
const { LoginAdmin, VerifyOTP, VerifyEmailOTPLogin, ResetAdminMFA } = require('../controller/Login');


const LoginRoute = Router();


LoginRoute.post('/', ValidateLogin, LoginAdmin);
LoginRoute.post('/mfa-verify', ValidateLogin, LoginAdmin); // Handle MFA verification through same endpoint
LoginRoute.get('/verify-otp/:userId/:otp', VerifyOTP);
LoginRoute.post('/verify-email-otp', VerifyEmailOTPLogin); // Verify email OTP for login
LoginRoute.post('/reset-admin-mfa', ResetAdminMFA); // Reset MFA for admin user

/**
 * @swagger
 * /login:
 *   post:
 *     summary: Admin login
 *     description: Admin login with email and password.
 *     tags:
 *       - Login
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Successful login
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /verify-otp/{userId}/{otp}:
 *   get:
 *     summary: Verify OTP for admin login
 *     description: Verify the OTP sent to admin email for login verification.
 *     tags:
 *       - Login
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *       - in: path
 *         name: otp
 *         required: true
 *         schema:
 *           type: string
 *         description: The OTP code
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *       400:
 *         description: Invalid or expired OTP
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */


module.exports = LoginRoute;
