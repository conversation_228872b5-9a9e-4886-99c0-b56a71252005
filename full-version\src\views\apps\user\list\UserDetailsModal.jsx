'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Avatar from '@mui/material/Avatar'
import Divider from '@mui/material/Divider'
import TextField from '@mui/material/TextField'
import IconButton from '@mui/material/IconButton'

// PDF Export
import jsPDF from 'jspdf'
import 'jspdf-autotable'

const UserDetailsModal = ({ open, onClose, userData }) => {
  const [userNotes, setUserNotes] = useState({})

  // Get the current user's note
  const currentNote = userData?.id ? userNotes[userData.id] || '' : ''

  // Handle note change for current user
  const handleNoteChange = (value) => {
    if (userData?.id) {
      setUserNotes(prev => ({
        ...prev,
        [userData.id]: value
      }))
    }
  }

  // Handle save note
  const handleSaveNote = () => {
    if (userData?.id && currentNote.trim()) {
      // Here you could also send the note to a backend API
      console.log(`Note saved for user ${userData.id}:`, currentNote)
      // You could show a success message here
      alert('Note saved successfully!')
    }
  }

  const handleDownloadPDF = () => {
    try {
      const doc = new jsPDF()

      // Add header
      doc.setFontSize(24)
      doc.setTextColor(40, 40, 40)
      doc.text('CAM Transport - Contact Details', 20, 25)

      // Add date
      const today = new Date()
      const formattedDate = today.toLocaleDateString()
      doc.setFontSize(12)
      doc.setTextColor(100, 100, 100)
      doc.text(`Generated on: ${formattedDate}`, 20, 35)

      // Add user avatar placeholder
      doc.setFillColor(41, 128, 185)
      doc.circle(30, 55, 8, 'F')
      doc.setTextColor(255, 255, 255)
      doc.setFontSize(16)
      doc.text(userData?.fullName?.charAt(0)?.toUpperCase() || 'U', 27, 59)

      // Add user name
      doc.setFontSize(20)
      doc.setTextColor(40, 40, 40)
      doc.text(userData?.fullName || 'Unknown User', 50, 55)

      // Add email
      doc.setFontSize(14)
      doc.setTextColor(100, 100, 100)
      doc.text(userData?.email || userData?.username || 'No email provided', 50, 65)

      // Add separator line
      doc.setDrawColor(200, 200, 200)
      doc.line(20, 75, 190, 75)

      // Contact Details Section
      doc.setFontSize(16)
      doc.setTextColor(40, 40, 40)
      doc.text('Contact Information', 20, 90)

      let yPosition = 105
      const lineHeight = 15

      // Contact details
      const details = [
        { label: 'Contact Number:', value: userData?.phone || userData?.contact || '+****************' },
        { label: 'Company:', value: userData?.company || userData?.currentPlan || 'CAM Transport' },
        { label: 'Inquiry Type:', value: userData?.inquiryType || userData?.type || 'General Inquiry' },
        { label: 'Status:', value: userData?.status || 'Pending' }
      ]

      details.forEach(detail => {
        doc.setFontSize(12)
        doc.setTextColor(100, 100, 100)
        doc.text(detail.label, 20, yPosition)
        doc.setTextColor(40, 40, 40)
        doc.text(detail.value, 70, yPosition)
        yPosition += lineHeight
      })

      // User Message Section
      if (userData?.userMessage) {
        yPosition += 10
        doc.setFontSize(16)
        doc.setTextColor(40, 40, 40)
        doc.text('User Message', 20, yPosition)

        yPosition += 15
        doc.setFontSize(12)
        doc.setTextColor(40, 40, 40)

        // Split long message into multiple lines
        const message = userData.userMessage || 'Hello, I would like to inquire about your services. Please contact me at your earliest convenience.'
        const splitMessage = doc.splitTextToSize(message, 150)
        doc.text(splitMessage, 20, yPosition)
        yPosition += splitMessage.length * 6
      }

      // Add notes if available
      if (currentNote && currentNote.trim()) {
        yPosition += 15
        doc.setFontSize(16)
        doc.setTextColor(40, 40, 40)
        doc.text('Admin Notes', 20, yPosition)

        yPosition += 15
        doc.setFontSize(12)
        doc.setTextColor(40, 40, 40)
        const splitNotes = doc.splitTextToSize(currentNote, 150)
        doc.text(splitNotes, 20, yPosition)
      }

      // Add footer
      doc.setFontSize(10)
      doc.setTextColor(150, 150, 150)
      doc.text('CAM Transport - Contact Management System', 20, 280)

      // Save the PDF
      const fileName = `${userData?.fullName?.replace(/\s+/g, '_') || 'contact'}_details_${new Date().toISOString().split('T')[0]}.pdf`
      doc.save(fileName)

    } catch (error) {
      console.error('PDF Generation Error:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  if (!userData) return null

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h5">User Details</Typography>
        <div className="flex items-center gap-2">
          <Button
            variant="outlined"
            color="primary"
            size="large"
            startIcon={<i className="tabler-file-type-pdf" />}
            onClick={handleDownloadPDF}
            className="font-bold"
            sx={{ fontSize: '1rem', padding: '12px 24px' }}
          >
            Download PDF
          </Button>
          <IconButton onClick={onClose}>
            <i className="tabler-x" />
          </IconButton>
        </div>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={6}>
          {/* User Profile Section */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <div className="flex items-center gap-4 mb-6">
                  <Avatar
                    sx={{ width: 100, height: 100, fontSize: '3rem', fontWeight: 'bold' }}
                  >
                    {userData.fullName?.charAt(0)?.toUpperCase()}
                  </Avatar>
                  <div>
                    <Typography variant="h3" className="mb-3 font-bold" style={{ fontSize: '2rem' }}>
                      {userData.fullName}
                    </Typography>
                    <Typography variant="h5" color="text.secondary" className="font-medium" style={{ fontSize: '1.2rem' }}>
                      {userData.email || userData.username}
                    </Typography>
                  </div>
                </div>

                <Divider className="mb-6" />

                {/* User Details Grid */}
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Contact
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {userData.phone || userData.contact || '+****************'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Company
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {userData.company || userData.currentPlan}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Type
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {userData.inquiryType || userData.type || 'General Inquiry'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Inquiry Date & Time
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {userData?.inquiryDate || userData?.createdAt
                        ? new Date(userData.inquiryDate || userData.createdAt).toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                          })
                        : 'Not available'
                      }
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      User Message
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem', lineHeight: '1.2' }}>
                      {userData.userMessage || 'Hello, I would like to inquire about your services. Please contact me at your earliest convenience.'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Add Note Section */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <Typography variant="h4" className="mb-4 font-bold" style={{ fontSize: '1.2rem' }}>
                  Add a Note
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Add your note here..."
                  value={currentNote}
                  onChange={(e) => handleNoteChange(e.target.value)}
                  variant="outlined"
                  sx={{
                    '& .MuiInputBase-input': {
                      fontSize: '1.25rem',
                      lineHeight: '1.5'
                    },
                    '& .MuiInputLabel-root': {
                      fontSize: '1.25rem'
                    }
                  }}
                />
                <div className="flex justify-end mt-4">
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    className="font-bold"
                    sx={{ fontSize: '1rem', padding: '12px 24px' }}
                    onClick={handleSaveNote}
                  >
                    Save
                  </Button>
                </div>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default UserDetailsModal
