'use client'

// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import CardContent from '@mui/material/CardContent'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import Button from '@mui/material/Button'
import Box from '@mui/material/Box'

// Component Imports
import CustomTextField from '@core/components/mui/TextField'

const TableFilters = ({ setData, tableData }) => {
  // States
  const [urgencyType, setUrgencyType] = useState('')
  const [status, setStatus] = useState('')
  const [hasDocuments, setHasDocuments] = useState('')

  useEffect(() => {
    const filteredData = tableData?.filter(inquiry => {
      if (urgencyType && (inquiry.urgency_type || inquiry.urgencyType || '').toLowerCase() !== urgencyType.toLowerCase()) return false
      if (status && (inquiry.status || 'pending').toLowerCase() !== status.toLowerCase()) return false
      if (hasDocuments !== '') {
        const hasDoc = Boolean(inquiry.documents)
        if (hasDocuments === 'yes' && !hasDoc) return false
        if (hasDocuments === 'no' && hasDoc) return false
      }

      return true
    })

    setData(filteredData)
  }, [urgencyType, status, hasDocuments, tableData, setData])

  const clearAllFilters = () => {
    setUrgencyType('')
    setStatus('')
    setHasDocuments('')
  }

  const hasActiveFilters = urgencyType || status || hasDocuments

  return (
    <CardContent>
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
        <FormControl fullWidth>
          <InputLabel id='urgency-type-select'>Type of Urgency</InputLabel>
          <Select
            fullWidth
            id='select-urgency-type'
            value={urgencyType}
            onChange={e => setUrgencyType(e.target.value)}
            label='Type of Urgency'
            labelId='urgency-type-select'
            inputProps={{ placeholder: 'Select Urgency Type' }}
          >
            <MenuItem value=''>All Urgency Types</MenuItem>
            <MenuItem value='Shipment Delay'>Shipment Delay</MenuItem>
            <MenuItem value='Vehicle Breakdown'>Vehicle Breakdown</MenuItem>
            <MenuItem value='Delivery Issue'>Delivery Issue</MenuItem>
            <MenuItem value='Lost/Damaged Cargo'>Lost/Damaged Cargo</MenuItem>
            <MenuItem value='Delivery Refusal'>Delivery Refusal</MenuItem>
            <MenuItem value='Other(Please Specify)'>Other (Please Specify)</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel id='status-select'>Status</InputLabel>
          <Select
            fullWidth
            id='select-status'
            value={status}
            onChange={e => setStatus(e.target.value)}
            label='Status'
            labelId='status-select'
            inputProps={{ placeholder: 'Select Status' }}
          >
            <MenuItem value=''>All Statuses</MenuItem>
            <MenuItem value='pending'>Pending</MenuItem>
            <MenuItem value='in-view'>In View</MenuItem>
            <MenuItem value='completed'>Completed</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel id='documents-select'>Documents</InputLabel>
          <Select
            fullWidth
            id='select-documents'
            value={hasDocuments}
            onChange={e => setHasDocuments(e.target.value)}
            label='Documents'
            labelId='documents-select'
            inputProps={{ placeholder: 'Select Document Status' }}
          >
            <MenuItem value=''>All Document Status</MenuItem>
            <MenuItem value='yes'>Has Documents</MenuItem>
            <MenuItem value='no'>No Documents</MenuItem>
          </Select>
        </FormControl>
      </div>

      {/* Clear Filters Button */}
      {hasActiveFilters && (
        <Box className='mt-4 flex justify-center'>
          <Button
            variant='outlined'
            color='secondary'
            onClick={clearAllFilters}
            startIcon={<i className='tabler-filter-off' />}
            size='small'
          >
            Clear All Filters
          </Button>
        </Box>
      )}
    </CardContent>
  )
}

export default TableFilters
