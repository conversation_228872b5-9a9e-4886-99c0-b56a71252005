'use client'

// React Imports
import { useState, useEffect } from 'react'

// Next Imports
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

// MUI Imports
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'

// Component Imports
import UserManagementList from '@/views/apps/user-management/UserManagementList'
import AddUserDrawer from '@/views/apps/user-management/AddUserDrawer'

// Utils Imports
const isSuperAdmin = (userRole) => {
  return userRole === 'super_admin'
}

const UserManagementPage = () => {
  // Hooks
  const { data: session, status } = useSession()
  const router = useRouter()

  // States
  const [addUserOpen, setAddUserOpen] = useState(false)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // Check authentication and authorization
  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (status === 'unauthenticated') {
      router.push('/login')
      return
    }

    if (session?.user && !isSuperAdmin(session.user.role)) {
      router.push('/')
      return
    }
  }, [session, status, router])

  // Show loading while checking authentication
  if (status === 'loading') {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    )
  }

  // Show error if not authorized
  if (!session?.user || !isSuperAdmin(session.user.role)) {
    return (
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Alert severity="error">
            Access Denied. This page is only accessible to Super Administrators.
          </Alert>
        </Grid>
      </Grid>
    )
  }

  const handleAddUser = () => {
    setAddUserOpen(true)
  }

  const handleUserAdded = () => {
    setAddUserOpen(false)
    setRefreshTrigger(prev => prev + 1)
  }

  const handleUserUpdated = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <Card>
          <CardHeader
            title="User Management"
            subheader="Manage system users and their roles"
            action={
              <Button
                variant="contained"
                onClick={handleAddUser}
                startIcon={<i className="tabler-plus" />}
              >
                Add New User
              </Button>
            }
          />
          <CardContent>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
              As a Super Administrator, you can create, edit, and manage all user accounts in the system.
              You can assign roles, activate/deactivate accounts, and reset passwords.
            </Typography>
            
            <UserManagementList 
              refreshTrigger={refreshTrigger}
              onUserUpdated={handleUserUpdated}
            />
          </CardContent>
        </Card>
      </Grid>

      <AddUserDrawer
        open={addUserOpen}
        onClose={() => setAddUserOpen(false)}
        onUserAdded={handleUserAdded}
      />
    </Grid>
  )
}

export default UserManagementPage
