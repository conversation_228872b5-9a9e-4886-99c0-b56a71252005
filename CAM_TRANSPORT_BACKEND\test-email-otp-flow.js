const axios = require('axios');

async function testEmailOTPFlow() {
    console.log('🧪 Testing Email OTP Flow...');
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    console.log('Step 1: Testing login (should trigger Email OTP)...');
    
    try {
        const loginResponse = await axios.post('http://localhost:8090/login', credentials, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ Login Response:');
        console.log('Status:', loginResponse.status);
        console.log('Data:', JSON.stringify(loginResponse.data, null, 2));
        
        // Check if Email OTP is required
        if (loginResponse.data.requiresEmailOTP) {
            console.log('\n📧 Email OTP is required!');
            console.log('User should receive an OTP email at:', credentials.email);
            console.log('User ID for OTP verification:', loginResponse.data.user.id);
            
            // Simulate OTP verification (you would get the real OTP from email)
            console.log('\nStep 2: Testing OTP verification endpoint...');
            
            // Test with a dummy OTP first to see the error
            try {
                const otpResponse = await axios.post('http://localhost:8090/login/verify-email-otp', {
                    userId: loginResponse.data.user.id,
                    otp: '123456' // Dummy OTP
                }, {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('OTP Response:', JSON.stringify(otpResponse.data, null, 2));
                
            } catch (otpError) {
                console.log('❌ OTP Verification Error (expected with dummy OTP):');
                if (otpError.response) {
                    console.log('Status:', otpError.response.status);
                    console.log('Data:', JSON.stringify(otpError.response.data, null, 2));
                }
            }
            
        } else if (loginResponse.data.requiresMFA) {
            console.log('\n🔐 MFA is required (TOTP)');
            
        } else {
            console.log('\n⚠️ No additional verification required - this might be the issue!');
            console.log('The user should require Email OTP but doesn\'t.');
        }
        
    } catch (error) {
        console.log('❌ Login Error:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.log('Error:', error.message);
        }
    }
    
    // Also test the email service directly
    console.log('\n🧪 Testing email service configuration...');
    
    // Check if email service environment variables are set
    const emailEnvVars = {
        'EMAIL_USER': process.env.EMAIL_USER ? 'SET' : 'NOT SET',
        'EMAIL_PASS': process.env.EMAIL_PASS ? 'SET' : 'NOT SET',
        'PASS': process.env.PASS ? 'SET' : 'NOT SET',
        'GOOGLE_CLIENT_ID': process.env.GOOGLE_CLIENT_ID ? 'SET' : 'NOT SET',
        'GOOGLE_CLIENT_SECRET': process.env.GOOGLE_CLIENT_SECRET ? 'SET' : 'NOT SET',
        'GOOGLE_REFRESH_TOKEN': process.env.GOOGLE_REFRESH_TOKEN ? 'SET' : 'NOT SET'
    };
    
    console.log('Email Environment Variables:');
    Object.entries(emailEnvVars).forEach(([key, value]) => {
        console.log(`${key}: ${value}`);
    });
}

if (require.main === module) {
    // Load environment variables
    require('dotenv').config();
    
    testEmailOTPFlow().then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
