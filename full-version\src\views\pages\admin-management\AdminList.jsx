'use client'

// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Chip from '@mui/material/Chip'
import Avatar from '@mui/material/Avatar'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'

// API Imports
import { formatDateTime, getUserInitials } from '@/services/userProfileApi'

const AdminList = () => {
  const [admins, setAdmins] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch all admin accounts
  useEffect(() => {
    const fetchAdmins = async () => {
      try {
        console.log('🔍 Fetching all admin accounts...')

        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
        const response = await fetch(`${API_BASE_URL}/user-profile/admins/list`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('✅ Admin accounts fetched:', result)

        setAdmins(result.admins || [])
        setError(null)
      } catch (err) {
        console.error('❌ Error fetching admin accounts:', err)
        setError('Failed to load admin accounts')
      } finally {
        setLoading(false)
      }
    }

    fetchAdmins()
  }, [])

  const refreshAdmins = () => {
    setLoading(true)
    setError(null)
    // Re-fetch admins
    const fetchAdmins = async () => {
      try {
        const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
        const response = await fetch(`${API_BASE_URL}/user-profile/admins/list`)
        const result = await response.json()
        setAdmins(result.admins || [])
      } catch (err) {
        setError('Failed to refresh admin accounts')
      } finally {
        setLoading(false)
      }
    }
    fetchAdmins()
  }

  if (loading) {
    return (
      <Card>
        <CardContent className='flex justify-center items-center py-8'>
          <div className='flex flex-col items-center gap-4'>
            <CircularProgress />
            <Typography>Loading admin accounts...</Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent>
          <Alert severity='error' action={
            <Button color='inherit' size='small' onClick={refreshAdmins}>
              Retry
            </Button>
          }>
            {error}
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between items-center mb-6'>
          <div>
            <Typography variant='h5' className='mb-2'>
              Admin Management
            </Typography>
            <Typography color='text.secondary'>
              Total Administrators: {admins.length}
            </Typography>
          </div>
          <Button variant='outlined' onClick={refreshAdmins} startIcon={<i className='tabler-refresh' />}>
            Refresh
          </Button>
        </div>

        {admins.length === 0 ? (
          <Box className='text-center py-8'>
            <i className='tabler-users text-6xl text-gray-400 mb-4' />
            <Typography variant='h6' color='text.secondary'>
              No admin accounts found
            </Typography>
          </Box>
        ) : (
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Admin</TableCell>
                  <TableCell>Admin ID</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell>Created</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {admins.map((admin) => (
                  <TableRow key={admin.id}>
                    <TableCell>
                      <div className='flex items-center gap-3'>
                        <Avatar sx={{ width: 40, height: 40 }}>
                          {getUserInitials(admin.username)}
                        </Avatar>
                        <div>
                          <Typography variant='body2' className='font-medium'>
                            {admin.username}
                          </Typography>
                          <Typography variant='caption' color='text.secondary'>
                            {admin.role}
                          </Typography>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Typography variant='body2' style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                        {admin.adminId}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant='body2'>
                        {admin.email}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <div className='flex flex-col gap-1'>
                        <Chip
                          label={admin.isVerified ? 'Verified' : 'Pending'}
                          color={admin.isVerified ? 'success' : 'warning'}
                          size='small'
                          variant='tonal'
                        />
                        <Chip
                          label={admin.isActive ? 'Active' : 'Inactive'}
                          color={admin.isActive ? 'primary' : 'default'}
                          size='small'
                          variant='tonal'
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <Typography variant='body2'>
                        {formatDateTime(admin.lastLoginDate)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant='body2'>
                        {admin.location || 'Unknown'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant='body2'>
                        {formatDateTime(admin.createdAt)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </CardContent>
    </Card>
  )
}

export default AdminList
