// Next Imports
import { redirect } from 'next/navigation'

// Third-party Imports
import { getServerSession } from 'next-auth'

// Config Imports
import themeConfig from '@configs/themeConfig'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

const GuestOnlyRoute = async ({ children, lang }) => {
  const session = await getServerSession()

  if (session) {
    // If user has session, redirect to profile (regardless of verification status)
    redirect(getLocalizedUrl('/pages/user-profile', lang))
  }

  return <>{children}</>
}

export default GuestOnlyRoute
