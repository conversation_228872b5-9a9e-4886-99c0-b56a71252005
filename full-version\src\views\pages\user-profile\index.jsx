'use client'

// MUI Imports
import Grid from '@mui/material/Grid2'

// Component Imports
import UserProfileHeader from './UserProfileHeader'

const UserProfile = ({ tabContentList, data }) => {

  return (
    <Grid container spacing={6}>
      <Grid size={{ xs: 12 }}>
        <UserProfileHeader data={data?.profileHeader} />
      </Grid>
      <Grid size={{ xs: 12 }} className='flex flex-col gap-6'>
        {tabContentList['profile']}
      </Grid>
    </Grid>
  )
}

export default UserProfile
