const axios = require('axios');

async function verifyNewMFA() {
    console.log('🔐 VERIFYING NEW MFA SETUP');
    console.log('==========================\n');
    
    const userId = '685923aa3135ef8ef080a6fe';
    
    console.log('📱 Please enter the 6-digit code from your authenticator app:');
    console.log('   (The app should now show codes for the new secret)');
    console.log('   Secret: EU4D4L2WOVBFCN2VNF3FKVCKPJXFUPCBJVGDS4ZZOVRXW5D2IARQ');
    
    // You need to replace 'XXXXXX' with the actual code from your app
    const testToken = '123456'; // Replace with real code
    
    try {
        console.log(`\n🔐 Testing MFA verification with token: ${testToken}`);
        
        const verifyResponse = await axios.post(`http://localhost:8090/mfa/setup/verify/${userId}`, {
            token: testToken
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ MFA verification successful!');
        console.log('Response:', verifyResponse.data);
        
        console.log('\n🎯 NOW TRY LOGGING IN:');
        console.log('1. Go to the frontend login page');
        console.log('2. Enter your credentials');
        console.log('3. When prompted for MFA, enter the current 6-digit code');
        console.log('4. Login should work!');
        
    } catch (error) {
        console.log('❌ MFA verification failed:', error.response?.data || error.message);
        console.log('\n📱 INSTRUCTIONS:');
        console.log('1. Make sure you added the secret to your authenticator app');
        console.log('2. Get the CURRENT 6-digit code from your app');
        console.log('3. Replace "123456" in this script with the real code');
        console.log('4. Run this script again quickly (codes expire every 30 seconds)');
    }
}

verifyNewMFA();
