'use client'

// React Imports
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'

const EmailOTPVerifySimple = () => {
  // States
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [userInfo, setUserInfo] = useState(null)
  const [debugInfo, setDebugInfo] = useState('')

  // Hooks
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    console.log('🔍 EmailOTPVerifySimple page loaded')
    
    // Get user info from URL parameters
    const userId = searchParams.get('userId')
    const email = searchParams.get('email')
    const username = searchParams.get('username')

    console.log('🔍 URL Parameters:', { userId, email, username })

    const debugText = `
URL Parameters:
- userId: ${userId || 'MISSING'}
- email: ${email || 'MISSING'}
- username: ${username || 'MISSING'}

Full URL: ${window.location.href}
Search Params: ${searchParams.toString()}
    `
    
    setDebugInfo(debugText)

    if (!userId || !email) {
      setError('Invalid verification link. Missing userId or email parameters.')
      return
    }

    setUserInfo({ userId, email, username })
  }, [searchParams])

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP code')
      return
    }

    if (!userInfo) {
      setError('User information not found. Please try logging in again.')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      console.log('🔍 Submitting OTP verification:', {
        userId: userInfo.userId,
        otp: parseInt(otp)
      })

      const response = await fetch('http://localhost:8090/login/verify-email-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userInfo.userId,
          otp: parseInt(otp)
        })
      })

      const result = await response.json()
      console.log('🔍 OTP verification response:', result)

      if (response.ok && result.success) {
        setSuccess('Email OTP verified successfully! Redirecting to your profile...')
        
        // Redirect to profile after successful verification
        setTimeout(() => {
          router.push('/pages/user-profile')
        }, 2000)
      } else {
        setError(result.message || 'OTP verification failed. Please try again.')
      }
    } catch (error) {
      console.error('OTP verification error:', error)
      setError('An error occurred during verification. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Box sx={{ padding: 4, maxWidth: 600, margin: '0 auto' }}>
      <Typography variant='h4' gutterBottom>
        Email OTP Verification (Debug) 📧
      </Typography>
      
      <Alert severity='info' sx={{ mb: 3 }}>
        <Typography variant='body2' component='pre' sx={{ whiteSpace: 'pre-wrap' }}>
          {debugInfo}
        </Typography>
      </Alert>

      {userInfo && (
        <Alert severity='success' sx={{ mb: 3 }}>
          <Typography>
            Ready to verify OTP for: <strong>{userInfo.email}</strong>
          </Typography>
        </Alert>
      )}

      {error && (
        <Alert severity='error' sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity='success' sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {userInfo && (
        <form onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label='Enter 6-digit OTP'
            placeholder='123456'
            value={otp}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '').slice(0, 6)
              setOtp(value)
              setError('')
            }}
            inputProps={{
              maxLength: 6,
              style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
            }}
            disabled={isLoading || success}
            sx={{ mb: 3 }}
          />

          <Button
            fullWidth
            variant='contained'
            type='submit'
            disabled={isLoading || success || otp.length !== 6}
            sx={{ mb: 2 }}
          >
            {isLoading ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} />
                Verifying...
              </>
            ) : success ? (
              'Redirecting...'
            ) : (
              'Verify OTP'
            )}
          </Button>

          <Button
            fullWidth
            variant='outlined'
            onClick={() => router.push('/login')}
            disabled={isLoading}
          >
            Back to Login
          </Button>
        </form>
      )}
    </Box>
  )
}

export default EmailOTPVerifySimple
