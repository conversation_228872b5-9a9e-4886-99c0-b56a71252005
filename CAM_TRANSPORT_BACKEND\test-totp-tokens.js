const speakeasy = require('speakeasy');

// Test the fixed secret that should be used for admin user
const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';

console.log('🔐 Testing TOTP Token Generation with Master Secret');
console.log('==================================================');
console.log(`Master Secret: ${ADMIN_SECRET}`);
console.log('');

// Generate current TOTP token
const currentToken = speakeasy.totp({
    secret: ADMIN_SECRET,
    encoding: 'base32'
});

console.log(`Current TOTP Token: ${currentToken}`);
console.log(`Token Length: ${currentToken.length}`);
console.log('');

// Test verification
const verified = speakeasy.totp.verify({
    secret: ADMIN_SECRET,
    encoding: 'base32',
    token: currentToken,
    window: 1
});

console.log(`✅ Token verification test: ${verified ? 'PASS' : 'FAIL'}`);
console.log('');

// Simulate multiple devices using the same secret
console.log('📱 Simulating Multiple Devices with Same Secret:');
console.log('===============================================');

for (let i = 1; i <= 5; i++) {
    const deviceToken = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32'
    });
    
    const deviceVerified = speakeasy.totp.verify({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        token: deviceToken,
        window: 1
    });
    
    console.log(`Device ${i}: Token=${deviceToken}, Verified=${deviceVerified ? '✅' : '❌'}`);
}

console.log('');
console.log('🎯 Key Points:');
console.log('- All devices generate the SAME token because they use the SAME secret');
console.log('- This is the correct TOTP behavior');
console.log('- Users can scan the same QR code on multiple devices');
console.log('- Works with Google Authenticator, Microsoft Authenticator, Authy, etc.');
console.log('');

// Generate QR code URL for manual testing
const qrCodeUrl = speakeasy.otpauthURL({
    secret: ADMIN_SECRET,
    label: 'CAM Transport (<EMAIL>)',
    issuer: 'CAM Transport',
    encoding: 'base32'
});

console.log('📱 QR Code URL for Manual Testing:');
console.log('==================================');
console.log(qrCodeUrl);
console.log('');
console.log('🔧 Manual Setup Instructions:');
console.log('1. Open your authenticator app (Google, Microsoft, Authy, etc.)');
console.log('2. Add a new account manually');
console.log('3. Enter this secret: JBSWY3DPEHPK3PXP');
console.log('4. Account name: CAM Transport Admin');
console.log('5. Issuer: CAM Transport');
console.log('');
console.log('📲 You can add this SAME secret to multiple devices!');

// Generate a few tokens for the next minute for testing
console.log('');
console.log('📅 Next few tokens (for testing):');
for (let i = 0; i < 3; i++) {
    const futureTime = Math.floor(Date.now() / 1000) + (i * 30);
    const futureToken = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        time: futureTime
    });
    const timeStr = new Date(futureTime * 1000).toLocaleTimeString();
    console.log(`${timeStr}: ${futureToken}`);
}
