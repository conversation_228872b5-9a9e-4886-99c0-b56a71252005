/**
 * Simple authentication utilities for development
 * In production, this would be replaced with proper authentication
 */

export const getCurrentUser = () => {
  // For development, we'll simulate a super admin user
  // In production, this would get the user from your auth system
  if (typeof window !== 'undefined') {
    const user = localStorage.getItem('currentUser')
    if (user) {
      try {
        return JSON.parse(user)
      } catch (e) {
        return null
      }
    }
  }
  
  // Default super admin for development
  return {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'super_admin',
    permissions: {
      canViewUsers: true,
      canCreateUsers: true,
      canEditUsers: true,
      canDeleteUsers: true,
      canManageRoles: true,
      canViewReports: true,
      canManageSettings: true
    }
  }
}

export const isAuthenticated = () => {
  const user = getCurrentUser()
  return user !== null
}

export const isSuperAdmin = () => {
  const user = getCurrentUser()
  return user && user.role === 'super_admin'
}

export const hasPermission = (permission) => {
  const user = getCurrentUser()
  return user && user.permissions && user.permissions[permission] === true
}

export const getAuthHeaders = () => {
  const headers = {
    'Content-Type': 'application/json',
  }
  
  // For development, we'll use a simple approach
  // In production, you'd get the actual token from your auth system
  const user = getCurrentUser()
  if (user) {
    // Simulate an auth token - in production this would be a real JWT
    headers['Authorization'] = `Bearer dev-token-${user.id}`
  }
  
  return headers
}

export const setCurrentUser = (user) => {
  if (typeof window !== 'undefined') {
    if (user) {
      localStorage.setItem('currentUser', JSON.stringify(user))
    } else {
      localStorage.removeItem('currentUser')
    }
  }
}

export const logout = () => {
  setCurrentUser(null)
  // In production, you'd also clear server-side session
}
