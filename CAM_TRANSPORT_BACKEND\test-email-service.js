const { sendEmail } = require('./service/Mailer');
require('dotenv').config();

async function testEmailService() {
    console.log('🧪 Testing Email Service...');
    
    // Check environment variables
    console.log('\n📋 Environment Variables:');
    console.log(`CLIENT_ID: ${process.env.CLIENT_ID ? 'SET' : 'NOT SET'}`);
    console.log(`CLIENT_SECRET: ${process.env.CLIENT_SECRET ? 'SET' : 'NOT SET'}`);
    console.log(`REFRESH_TOKEN: ${process.env.REFRESH_TOKEN ? 'SET' : 'NOT SET'}`);
    console.log(`SMTP_FROM: ${process.env.SMTP_FROM ? 'SET' : 'NOT SET'}`);
    
    try {
        console.log('\n📧 Attempting to send test email...');
        
        const result = await sendEmail({
            to: '<EMAIL>',
            subject: 'CAM Transport - Test Email',
            html: `
                <h2>Test Email</h2>
                <p>This is a test email to verify the email service is working.</p>
                <p>If you receive this, the email service is configured correctly!</p>
                <p>Time: ${new Date().toISOString()}</p>
            `,
            importance: 'high'
        });
        
        console.log('✅ Email sent successfully!');
        console.log('Result:', result);
        
    } catch (error) {
        console.log('❌ Email sending failed:');
        console.log('Error:', error.message);
        console.log('Full error:', error);
        
        // Analyze the error
        if (error.message.includes('invalid_grant')) {
            console.log('\n💡 Analysis: OAuth token issue');
            console.log('The refresh token might be expired or invalid.');
            console.log('You may need to regenerate the OAuth tokens.');
        } else if (error.message.includes('authentication')) {
            console.log('\n💡 Analysis: Authentication issue');
            console.log('Check the CLIENT_ID, CLIENT_SECRET, and REFRESH_TOKEN.');
        } else if (error.message.includes('network')) {
            console.log('\n💡 Analysis: Network issue');
            console.log('Check your internet connection.');
        }
    }
}

if (require.main === module) {
    testEmailService().then(() => {
        console.log('\n✅ Email service test completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Email service test failed:', error);
        process.exit(1);
    });
}
