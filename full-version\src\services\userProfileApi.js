// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

// Get user profile by ID
export const getUserProfile = async (userId) => {
  try {
    console.log('🔍 Fetching user profile for ID:', userId)
    
    const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ User profile fetched successfully:', result)
    
    return result.user
  } catch (error) {
    console.error('❌ Error fetching user profile:', error)
    throw error
  }
}

// Update user profile
export const updateUserProfile = async (userId, profileData) => {
  try {
    console.log('🔄 Updating user profile for ID:', userId)
    console.log('Profile data:', profileData)
    
    const response = await fetch(`${API_BASE_URL}/user-profile/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(profileData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ User profile updated successfully:', result)
    
    return result.user
  } catch (error) {
    console.error('❌ Error updating user profile:', error)
    throw error
  }
}

// Get location from IP address (using a free service)
export const getLocationFromIP = async (ipAddress) => {
  try {
    if (!ipAddress || ipAddress === 'Unknown' || ipAddress.includes('127.0.0.1') || ipAddress.includes('::1')) {
      return 'Local/Development'
    }

    // Using ipapi.co free service (1000 requests per day)
    const response = await fetch(`https://ipapi.co/${ipAddress}/json/`)
    
    if (!response.ok) {
      throw new Error('Failed to get location')
    }

    const data = await response.json()
    
    if (data.error) {
      throw new Error(data.reason || 'Location service error')
    }

    // Format location as "City, Country"
    const location = `${data.city || 'Unknown'}, ${data.country_name || 'Unknown'}`
    console.log('📍 Location detected:', location)
    
    return location
  } catch (error) {
    console.error('❌ Error getting location:', error)
    return 'Unknown Location'
  }
}

// Format date and time for display
export const formatDateTime = (dateString) => {
  if (!dateString) return 'Not available'
  
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return dateString
    
    // Format: MM/DD/YYYY HH:MM AM/PM
    const dateOptions = {
      month: '2-digit',
      day: '2-digit', 
      year: 'numeric'
    }
    const timeOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }
    
    const formattedDate = date.toLocaleDateString('en-US', dateOptions)
    const formattedTime = date.toLocaleTimeString('en-US', timeOptions)
    
    return `${formattedDate} ${formattedTime}`
  } catch (error) {
    return dateString
  }
}

// Get user initials for avatar
export const getUserInitials = (fullName) => {
  if (!fullName) return 'U'
  
  const names = fullName.trim().split(' ')
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase()
  }
  
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase()
}
