'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  Container,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Button,
  TextField,
  Alert,
  Grid,
  Divider
} from '@mui/material'
import { Security, Login, Settings } from '@mui/icons-material'
import MFASetup from '@/components/MFA/MFASetup'
import MFAVerification from '@/components/MFA/MFAVerification'
import MFASettings from '@/components/MFA/MFASettings'

const MFADemoPage = () => {
  const { data: session, status } = useSession()
  const [setupOpen, setSetupOpen] = useState(false)
  const [verificationOpen, setVerificationOpen] = useState(false)
  const [demoUserId, setDemoUserId] = useState(null)
  const [demoUserEmail, setDemoUserEmail] = useState(null)

  useEffect(() => {
    // Get real user data from NextAuth session, fallback to demo for testing
    if (session?.user) {
      setDemoUserEmail(session.user.email)
      setDemoUserId(session.user.id)
    } else {
      // Fallback to demo data for testing when not logged in
      setDemoUserId('demo-user-123')
      setDemoUserEmail('<EMAIL>')
    }
  }, [session])

  const handleSetupSuccess = (message) => {
    alert(message)
    setSetupOpen(false)
  }

  const handleVerificationSuccess = (result) => {
    alert('MFA verification successful!')
    setVerificationOpen(false)
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          MFA Demo & Testing
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Test the Multi-Factor Authentication functionality with demo components.
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {/* Demo Controls */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Settings />
                Demo Controls
              </Typography>

              <Box sx={{ mb: 2 }}>
                <TextField
                  fullWidth
                  label="Demo User ID"
                  value={demoUserId}
                  onChange={(e) => setDemoUserId(e.target.value)}
                  size="small"
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Demo User Email"
                  value={demoUserEmail}
                  onChange={(e) => setDemoUserEmail(e.target.value)}
                  size="small"
                  sx={{ mb: 2 }}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<Security />}
                  onClick={() => setSetupOpen(true)}
                  fullWidth
                >
                  Test MFA Setup
                </Button>

                <Button
                  variant="outlined"
                  startIcon={<Login />}
                  onClick={() => setVerificationOpen(true)}
                  fullWidth
                >
                  Test MFA Verification
                </Button>
              </Box>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  Use these buttons to test the MFA components with demo data.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>

        {/* MFA Settings Component */}
        <Grid item xs={12} md={8}>
          <MFASettings userId={demoUserId} userEmail={demoUserEmail} />
        </Grid>

        {/* Instructions */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                How to Test MFA
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>1. Setup MFA:</strong> Click "Test MFA Setup" or use the "Enable Two-Factor Authentication" button in the settings above.
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>2. Scan QR Code:</strong> Use an authenticator app like Google Authenticator, Authy, or Microsoft Authenticator to scan the QR code.
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>3. Verify Setup:</strong> Enter the 6-digit code from your authenticator app to complete the setup.
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>4. Test Login:</strong> Click "Test MFA Verification" to simulate the login verification process.
              </Typography>

              <Typography variant="body2" paragraph>
                <strong>5. Backup Codes:</strong> Save the backup codes generated during setup. You can also generate new ones from the settings.
              </Typography>

              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Note:</strong> This is a demo environment. In production, make sure to:
                  <br />• Integrate with your actual user authentication system
                  <br />• Implement proper session management
                  <br />• Add password verification for sensitive operations
                  <br />• Use HTTPS for all MFA-related communications
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* MFA Setup Dialog */}
      <MFASetup
        open={setupOpen}
        onClose={() => setSetupOpen(false)}
        userId={demoUserId}
        onSuccess={handleSetupSuccess}
      />

      {/* MFA Verification Dialog */}
      <MFAVerification
        open={verificationOpen}
        onClose={() => setVerificationOpen(false)}
        userId={demoUserId}
        userEmail={demoUserEmail}
        onSuccess={handleVerificationSuccess}
      />
    </Container>
  )
}

export default MFADemoPage
