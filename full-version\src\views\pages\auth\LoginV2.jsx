'use client'

// React Imports
import { useState, useEffect } from 'react'

// Next Imports
import Link from 'next/link'
import { useParams, useRouter } from 'next/navigation'

// MUI Imports
import useMediaQuery from '@mui/material/useMediaQuery'
import { styled, useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import Checkbox from '@mui/material/Checkbox'
import Button from '@mui/material/Button'
import FormControlLabel from '@mui/material/FormControlLabel'
import Divider from '@mui/material/Divider'
import Alert from '@mui/material/Alert'

// Third-party Imports
import classnames from 'classnames'
import { signIn, signOut, getSession } from 'next-auth/react'
import { Controller, useForm } from 'react-hook-form'
import { valibotResolver } from '@hookform/resolvers/valibot'
import { email, object, minLength, string, pipe, nonEmpty } from 'valibot'

// Component Imports
import Logo from '@components/layout/shared/Logo'
import CustomTextField from '@core/components/mui/TextField'

// Config Imports
import themeConfig from '@configs/themeConfig'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'
import { useSettings } from '@core/hooks/useSettings'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

// Validation Schema
const schema = object({
  username: pipe(string(), minLength(1, 'Username is required')),
  email: pipe(string(), minLength(1, 'Email is required'), email('Email is invalid')),
  password: pipe(
    string(),
    nonEmpty('Password is required'),
    minLength(5, 'Password must be at least 5 characters long')
  )
})

// Styled Custom Components
const LoginIllustration = styled('img')(({ theme }) => ({
  zIndex: 2,
  blockSize: 'auto',
  maxBlockSize: 680,
  maxInlineSize: '100%',
  margin: theme.spacing(12),
  [theme.breakpoints.down(1536)]: {
    maxBlockSize: 550
  },
  [theme.breakpoints.down('lg')]: {
    maxBlockSize: 450
  }
}))

const MaskImg = styled('img')({
  blockSize: 'auto',
  maxBlockSize: 355,
  inlineSize: '100%',
  position: 'absolute',
  insetBlockEnd: 0,
  zIndex: -1
})

const LoginV2 = ({ mode }) => {
  // States
  const [isPasswordShown, setIsPasswordShown] = useState(false)
  const [errorState, setErrorState] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [otpSent, setOtpSent] = useState(false)
  const [redirecting, setRedirecting] = useState(false)
  const [needsEmailVerification, setNeedsEmailVerification] = useState(false)

  // Vars
  const darkImg = '/images/pages/auth-mask-dark.png'
  const lightImg = '/images/pages/auth-mask-light.png'
  const darkIllustration = '/images/illustrations/auth/v2-login-dark.png'
  const lightIllustration = '/images/illustrations/auth/v2-login-light.png'
  const borderedDarkIllustration = '/images/illustrations/auth/v2-login-dark-border.png'
  const borderedLightIllustration = '/images/illustrations/auth/v2-login-light-border.png'

  // Hooks
  const router = useRouter()
  const { lang: locale } = useParams()
  const { settings } = useSettings()
  const theme = useTheme()
  const hidden = useMediaQuery(theme.breakpoints.down('md'))
  const authBackground = useImageVariant(mode, lightImg, darkImg)

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: valibotResolver(schema),
    defaultValues: {
      username: '',
      email: '',
      password: ''
    }
  })

  const characterIllustration = useImageVariant(
    mode,
    lightIllustration,
    darkIllustration,
    borderedLightIllustration,
    borderedDarkIllustration
  )

  const handleClickShowPassword = () => setIsPasswordShown(show => !show)

  // CRITICAL SECURITY FIX: Clear any existing session when login page loads
  useEffect(() => {
    const clearExistingSession = async () => {
      try {
        console.log('🔐 LoginV2 page loaded - clearing any existing session for security')
        await signOut({ redirect: false })
      } catch (error) {
        console.log('Session clear error (expected if no session):', error)
      }
    }

    clearExistingSession()
  }, [])

  useEffect(() => {
    // Check if user was just verified
    const verificationSuccess = localStorage.getItem('verificationSuccess')
    const verificationTime = localStorage.getItem('verificationTime')

    // Only consider verification successful if it happened in the last 5 minutes
    const isRecentVerification = verificationTime &&
      (Date.now() - parseInt(verificationTime)) < 5 * 60 * 1000

    if (verificationSuccess === 'true' && isRecentVerification) {
      // Clear the verification flags
      localStorage.removeItem('verificationSuccess')
      localStorage.removeItem('verificationTime')

      // Show success message and set redirecting state
      setOtpSent(true)
      setRedirecting(true)

      console.log('Verification successful, redirecting to user profile...')

      // Redirect to user profile after a short delay
      setTimeout(() => {
        console.log('Executing redirect to user profile')
        router.push('/pages/user-profile')
      }, 2000)
    }
  }, [router])

  const onSubmit = async data => {
    setIsLoading(true)
    setErrorState(null)

    try {
      console.log('Submitting login with:', data)

      const res = await signIn('credentials', {
        username: data.username,
        email: data.email,
        password: data.password,
        redirect: false
      })

      console.log('SignIn response:', res)

      if (res && res.ok && !res.error) {
        // Check session for user status
        const session = await getSession()

        console.log('✅ Login successful, checking session:', {
          requiresMFA: session?.user?.requiresMFA,
          mfaVerified: session?.user?.mfaVerified,
          mfaEnabled: session?.user?.mfaEnabled
        });

        // Always require MFA for users with MFA enabled, regardless of previous verification
        if (session?.user?.requiresMFA) {
          console.log('🔐 User requires MFA, redirecting to verification');
          // Redirect to MFA verification
          const params = new URLSearchParams({
            email: data.email,
            username: data.username
          })
          router.push(`/pages/auth/mfa-verify?${params.toString()}`)
        } else {
          console.log('✅ Login successful, redirecting to profile');
          // Login successful - no MFA required
          setOtpSent(true)
          setIsLoading(false)

          // Redirect to profile page
          setTimeout(() => {
            router.push('/pages/user-profile')
          }, 2000)
        }
      } else {
        setIsLoading(false)
        if (res?.error) {
          try {
            const error = JSON.parse(res.error)
            setErrorState(error)
          } catch {
            // If error is not JSON parsable
            setErrorState({ message: [res.error || 'Authentication failed'] })
          }
        } else {
          setErrorState({ message: ['Invalid credentials. Please try again.'] })
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      setIsLoading(false)
      setErrorState({ message: ['An error occurred during login. Please try again.'] })
    }
  }

  return (
    <div className='flex bs-full justify-center'>
      <div
        className={classnames(
          'flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden',
          {
            'border-ie': settings.skin === 'bordered'
          }
        )}
      >
        <LoginIllustration src={characterIllustration} alt='character-illustration' className='h-[300px] w-auto min-w-[900px]' />
        {!hidden && (
          <MaskImg
            alt='mask'
            src={authBackground}
            className={classnames({ 'scale-x-[-1]': theme.direction === 'rtl' })}
          />
        )}
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <Link
          href={getLocalizedUrl('/', locale)}
          className='absolute block-start-5 sm:block-start-[33px] inline-start-6 sm:inline-start-[38px]'
        >
          <Logo />
        </Link>
        <div className='flex flex-col gap-6 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset] mbs-11 sm:mbs-14 md:mbs-0'>
          <div className='flex flex-col gap-1'>
            <Typography variant='h4'>{`Welcome to ${themeConfig.templateName}! 👋🏻`}</Typography>
            <Typography>Please sign-in to your account and start the adventure</Typography>
          </div>
          {!otpSent ? (
            <Alert icon={false} className='bg-[var(--mui-palette-primary-lightOpacity)]'>
              <Typography variant='body2' color='primary.main'>
                Please enter your username, email, and password to login.
              </Typography>
            </Alert>
          ) : (
            <Alert icon={false} className='bg-[var(--mui-palette-success-lightOpacity)]'>
              <Typography variant='body2' color='success.main'>
                {redirecting
                  ? '✅ Verification successful! Redirecting to your profile...'
                  : '✅ Login successful! Redirecting to your profile...'}
              </Typography>
            </Alert>
          )}
          <form noValidate autoComplete='off' onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-6'>
            <Controller
              name='username'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  autoFocus
                  fullWidth
                  label='Username'
                  placeholder='Enter your username'
                  onChange={e => {
                    field.onChange(e.target.value)
                    errorState !== null && setErrorState(null)
                  }}
                  {...(errors.username && {
                    error: true,
                    helperText: errors.username.message
                  })}
                />
              )}
            />
            <Controller
              name='email'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  label='Email'
                  placeholder='Enter your email'
                  onChange={e => {
                    field.onChange(e.target.value)
                    errorState !== null && setErrorState(null)
                  }}
                  {...((errors.email || errorState !== null) && {
                    error: true,
                    helperText: errors?.email?.message || errorState?.message[0]
                  })}
                />
              )}
            />
            <Controller
              name='password'
              control={control}
              render={({ field }) => (
                <CustomTextField
                  {...field}
                  fullWidth
                  label='Password'
                  placeholder='············'
                  id='outlined-adornment-password'
                  type={isPasswordShown ? 'text' : 'password'}
                  onChange={e => {
                    field.onChange(e.target.value)
                    errorState !== null && setErrorState(null)
                  }}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position='end'>
                          <IconButton edge='end' onClick={handleClickShowPassword} onMouseDown={e => e.preventDefault()}>
                            <i className={isPasswordShown ? 'tabler-eye-off' : 'tabler-eye'} />
                          </IconButton>
                        </InputAdornment>
                      )
                    }
                  }}
                  {...(errors.password && { error: true, helperText: errors.password.message })}
                />
              )}
            />
            <div className='flex justify-between items-center gap-x-3 gap-y-1 flex-wrap'>
              <FormControlLabel control={<Checkbox />} label='Remember me' />
              <Typography
                className='text-end'
                color='primary.main'
                component={Link}
                href={getLocalizedUrl('/pages/auth/forgot-password-v2', locale)}
              >
                Forgot password?
              </Typography>
            </div>
            <Button
              fullWidth
              variant='contained'
              type='submit'
              disabled={isLoading || otpSent}
            >
              {isLoading ? 'Logging in...' : otpSent ? (redirecting ? 'Redirecting...' : 'Redirecting to Profile...') : 'Login'}
            </Button>

          </form>
          {redirecting && (
            <Button
              fullWidth
              variant='outlined'
              color='success'
              onClick={() => router.push('/pages/user-profile')}
            >
              Go to Profile Now
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default LoginV2





