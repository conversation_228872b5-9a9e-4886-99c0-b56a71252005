const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function checkUsers() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/cam_transport', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('🔗 Connected to MongoDB');

        // Find all admin users
        const adminUsers = await Login.find({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' }
            ]
        });

        console.log(`📊 Found ${adminUsers.length} admin users:`);
        console.log('');

        adminUsers.forEach((user, index) => {
            console.log(`👤 Admin User ${index + 1}:`);
            console.log(`  ID: ${user._id}`);
            console.log(`  Admin ID: ${user.adminId}`);
            console.log(`  Username: ${user.username}`);
            console.log(`  Email: ${user.email}`);
            console.log(`  MFA Enabled: ${user.mfaEnabled}`);
            console.log(`  Has MFA Secret: ${!!user.mfaSecret}`);
            console.log(`  MFA Secret: ${user.mfaSecret || 'None'}`);
            console.log(`  Active Devices: ${user.mfaDevices?.filter(d => d.isActive).length || 0}`);
            console.log(`  Total Devices: ${user.mfaDevices?.length || 0}`);
            console.log(`  Is Verified: ${user.isVerified}`);
            console.log('');
        });

        // Check if there are duplicate admin users
        if (adminUsers.length > 1) {
            console.log('⚠️  WARNING: Multiple admin users found!');
            console.log('This explains why MFA verification is failing.');
            console.log('The login endpoint uses one user, but MFA verify uses another.');
            console.log('');
            
            // Find the user with MFA enabled
            const mfaUser = adminUsers.find(u => u.mfaEnabled && u.mfaSecret);
            if (mfaUser) {
                console.log('🔐 User with MFA enabled:');
                console.log(`  ID: ${mfaUser._id}`);
                console.log(`  Admin ID: ${mfaUser.adminId}`);
                console.log(`  MFA Secret: ${mfaUser.mfaSecret}`);
            }
        }

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

checkUsers();
