'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Divider from '@mui/material/Divider'

const DocumentViewerModal = ({ open, onClose, inquiryData }) => {
  const handleDownloadDocument = () => {
    // Create a sample document download functionality
    const element = document.createElement('a')
    const today = new Date()
    const formattedDate = `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`
    const file = new Blob([`URGENT INQUIRY DOCUMENT\n\nSubmitted by: ${inquiryData?.fullName}\nEmail: ${inquiryData?.email}\nPhone: ${inquiryData?.phone_number || inquiryData?.phone || 'N/A'}\nUrgency Type: ${inquiryData?.urgency_type || inquiryData?.urgencyType || 'Emergency'}\nReference Number: ${inquiryData?.ref_number || 'URG-2025-001'}\n\nDescription:\n${inquiryData?.brief_description || inquiryData?.description || 'This is an urgent matter that requires immediate attention.'}\n\nDocument generated on: ${formattedDate}`], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `${inquiryData?.fullName || 'urgent_inquiry'}_document.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  if (!inquiryData) return null

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h5" style={{ fontSize: '1.5rem' }}>
          Document - {inquiryData.fullName}
        </Typography>
        <div className="flex items-center gap-2">
          <Button
            variant="outlined"
            color="primary"
            size="large"
            startIcon={<i className="tabler-download" />}
            onClick={handleDownloadDocument}
            className="font-bold"
            sx={{ fontSize: '1rem', padding: '8px 16px' }}
          >
            Download Document
          </Button>
          <IconButton onClick={onClose}>
            <i className="tabler-x" />
          </IconButton>
        </div>
      </DialogTitle>

      <DialogContent>
        <Box sx={{
          minHeight: '600px',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          padding: '20px',
          backgroundColor: '#fafafa'
        }}>
          {/* Document Content */}
          <div className="document-content">
            <div className="text-center mb-6">
              <Typography variant="h4" className="font-bold mb-2" style={{ fontSize: '2rem', color: '#f44336' }}>
                URGENT INQUIRY DOCUMENT
              </Typography>
              <Typography variant="h6" color="text.secondary" style={{ fontSize: '1.2rem' }}>
                Reference: {inquiryData.ref_number || inquiryData.referenceNumber || 'URG-2025-001'}
              </Typography>
            </div>

            <Card className="mb-4">
              <CardContent>
                <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #f44336', paddingBottom: '4px' }}>
                  CONTACT INFORMATION
                </Typography>
                <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  <strong>Full Name:</strong> {inquiryData.fullName}<br />
                  <strong>Email:</strong> {inquiryData.email}<br />
                  <strong>Phone:</strong> {inquiryData.phone_number || inquiryData.phone || '+****************'}<br />
                  <strong>Submitted Date:</strong> {inquiryData.submittedDate || (() => {
                    const today = new Date()
                    return `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`
                  })()}
                </Typography>
              </CardContent>
            </Card>

            <Card className="mb-4">
              <CardContent>
                <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #f44336', paddingBottom: '4px' }}>
                  URGENCY DETAILS
                </Typography>
                <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  <strong>Type of Urgency:</strong> {inquiryData.urgency_type || inquiryData.urgencyType || 'Emergency'}<br />
                  {inquiryData.other_urgency && (
                    <>
                      <strong>Other Urgency:</strong> {inquiryData.other_urgency}<br />
                    </>
                  )}
                  <strong>Priority Level:</strong> <span style={{ color: '#f44336', fontWeight: 'bold' }}>HIGH PRIORITY</span>
                </Typography>
              </CardContent>
            </Card>

            <Card className="mb-4">
              <CardContent>
                <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #f44336', paddingBottom: '4px' }}>
                  DESCRIPTION
                </Typography>
                <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  {inquiryData.brief_description || inquiryData.description || 'This is an urgent matter that requires immediate attention. Please contact me as soon as possible to discuss the details and next steps. The situation is time-sensitive and needs prompt resolution.'}
                </Typography>
              </CardContent>
            </Card>

            <Card className="mb-4">
              <CardContent>
                <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #f44336', paddingBottom: '4px' }}>
                  ATTACHED DOCUMENTS
                </Typography>
                <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  {inquiryData.documents ? (
                    <>
                      <strong>Status:</strong> Documents attached<br />
                      <strong>Files:</strong>
                      <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
                        <li>urgent_inquiry_form.pdf</li>
                        <li>supporting_documents.pdf</li>
                        <li>identification_proof.pdf</li>
                      </ul>
                    </>
                  ) : (
                    <>
                      <strong>Status:</strong> No documents attached
                    </>
                  )}
                </Typography>
              </CardContent>
            </Card>

            <div className="text-center mt-8">
              <Typography variant="body2" color="text.secondary" style={{ fontSize: '0.9rem' }}>
                Document generated on {(() => {
                  const today = new Date()
                  return `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`
                })()}
              </Typography>
              <Typography variant="body2" color="error" style={{ fontSize: '1rem', fontWeight: 'bold', marginTop: '8px' }}>
                ⚠️ URGENT - REQUIRES IMMEDIATE ATTENTION
              </Typography>
            </div>
          </div>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default DocumentViewerModal
