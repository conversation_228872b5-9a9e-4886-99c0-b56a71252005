const User = props => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='64' height='65' viewBox='0 0 64 65' fill='none' {...props}>
      <path
        opacity='0.2'
        d='M31.9999 8.69434C27.1437 8.69292 22.4012 10.1648 18.399 12.9154C14.3969 15.6661 11.3233 19.5661 9.58436 24.1004C7.84542 28.6346 7.52291 33.5897 8.65945 38.3112C9.79598 43.0326 12.3381 47.2981 15.9499 50.5443C17.4549 47.5807 19.7511 45.0916 22.5841 43.353C25.417 41.6144 28.676 40.6942 31.9999 40.6943C30.0221 40.6943 28.0887 40.1078 26.4442 39.009C24.7997 37.9102 23.518 36.3484 22.7611 34.5212C22.0043 32.6939 21.8062 30.6832 22.1921 28.7434C22.5779 26.8036 23.5303 25.0218 24.9289 23.6233C26.3274 22.2247 28.1092 21.2723 30.049 20.8865C31.9888 20.5006 33.9995 20.6987 35.8268 21.4555C37.654 22.2124 39.2158 23.4941 40.3146 25.1386C41.4135 26.7831 41.9999 28.7165 41.9999 30.6943C41.9999 33.3465 40.9464 35.89 39.071 37.7654C37.1956 39.6408 34.6521 40.6943 31.9999 40.6943C35.3238 40.6942 38.5829 41.6144 41.4158 43.353C44.2487 45.0916 46.545 47.5807 48.0499 50.5443C51.6618 47.2981 54.2039 43.0326 55.3404 38.3112C56.477 33.5897 56.1545 28.6346 54.4155 24.1004C52.6766 19.5661 49.603 15.6661 45.6008 12.9154C41.5987 10.1648 36.8562 8.69292 31.9999 8.69434Z'
        fill='currentColor'
      />
      <path
        d='M32 40.6943C37.5228 40.6943 42 36.2172 42 30.6943C42 25.1715 37.5228 20.6943 32 20.6943C26.4772 20.6943 22 25.1715 22 30.6943C22 36.2172 26.4772 40.6943 32 40.6943ZM32 40.6943C28.6759 40.6943 25.4168 41.6133 22.5839 43.3521C19.7509 45.091 17.4548 47.5804 15.95 50.5443M32 40.6943C35.3241 40.6943 38.5832 41.6133 41.4161 43.3521C44.2491 45.091 46.5452 47.5804 48.05 50.5443M56 32.6943C56 45.9492 45.2548 56.6943 32 56.6943C18.7452 56.6943 8 45.9492 8 32.6943C8 19.4395 18.7452 8.69434 32 8.69434C45.2548 8.69434 56 19.4395 56 32.6943Z'
        stroke='currentColor'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default User
