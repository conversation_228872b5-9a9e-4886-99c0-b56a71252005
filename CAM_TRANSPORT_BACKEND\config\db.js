const mongoose = require('mongoose');
const chalk = require('chalk');
require('dotenv').config();

const MAX_RETRIES = 5;
const RETRY_DELAY_MS = 3000;

const delay = ms => new Promise(res => setTimeout(res, ms));

const ConnectDB = async (retries = MAX_RETRIES) => {
    if (!process.env.MONGO_URL) {
        console.error(chalk.red.bold('🚨 MONGO_URL environment variable is missing!'));
        process.exit(1);
    }
    try {
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true, // Always use SSL for MongoDB Atlas
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        console.log(chalk.blue.bold('MongoDB connected successfully'));
    } catch (error) {
        console.error(chalk.red.bold('MongoDB connection failed:'));
        console.error(chalk.red(error.message || error));
        if (retries > 0) {
            console.log(chalk.yellow(`Retrying to connect in ${RETRY_DELAY_MS / 1000}s... (${retries} attempts left)`));
            await delay(RETRY_DELAY_MS);
            return ConnectDB(retries - 1);
        } else {
            console.error(chalk.red.bold('🚨 MongoDB failed to connect after max retries. Exiting process.'));
            process.exit(1);
        }
    }
};

module.exports = ConnectDB;
