// Next Imports
import Link from 'next/link'

// MUI Imports
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'

const MFASetupPage = () => {
  return (
    <Grid container spacing={6}>
      <Grid size={{ xs: 12 }}>
        <Card>
          <CardHeader
            title="Multi-Factor Authentication Setup"
            subheader="Secure your account with an additional layer of protection"
          />
          <CardContent>
            <Alert severity="info" className="mb-6">
              This is a dummy MFA setup page. Functionality will be implemented later.
            </Alert>

            <div className="space-y-6">
              <div>
                <Typography variant="h6" className="mb-2">
                  Step 1: Choose Authentication Method
                </Typography>
                <Typography variant="body2" color="text.secondary" className="mb-4">
                  Select your preferred method for two-factor authentication
                </Typography>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card variant="outlined" className="p-4 cursor-pointer hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <i className="tabler-device-mobile text-2xl text-primary" />
                      <div>
                        <Typography variant="subtitle1">Authenticator App</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Use Google Authenticator, Authy, or similar apps
                        </Typography>
                      </div>
                    </div>
                  </Card>

                  <Card variant="outlined" className="p-4 cursor-pointer hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <i className="tabler-message text-2xl text-primary" />
                      <div>
                        <Typography variant="subtitle1">SMS Text Message</Typography>
                        <Typography variant="body2" color="text.secondary">
                          Receive codes via text message
                        </Typography>
                      </div>
                    </div>
                  </Card>
                </div>
              </div>

              <div>
                <Typography variant="h6" className="mb-2">
                  Step 2: Scan QR Code
                </Typography>
                <Typography variant="body2" color="text.secondary" className="mb-4">
                  Scan this QR code with your authenticator app
                </Typography>

                <div className="flex justify-center mb-4">
                  <div className="w-48 h-48 bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                    <Typography variant="body2" color="text.secondary">
                      QR Code Placeholder
                    </Typography>
                  </div>
                </div>

                <Typography variant="body2" color="text.secondary" className="text-center">
                  Manual entry key: ABCD-EFGH-IJKL-MNOP
                </Typography>
              </div>

              <div>
                <Typography variant="h6" className="mb-2">
                  Step 3: Verify Setup
                </Typography>
                <Typography variant="body2" color="text.secondary" className="mb-4">
                  Enter the 6-digit code from your authenticator app
                </Typography>

                <div className="flex space-x-2 mb-4">
                  <input
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 text-center border border-gray-300 rounded focus:border-primary focus:outline-none"
                    placeholder="0"
                  />
                  <input
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 text-center border border-gray-300 rounded focus:border-primary focus:outline-none"
                    placeholder="0"
                  />
                  <input
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 text-center border border-gray-300 rounded focus:border-primary focus:outline-none"
                    placeholder="0"
                  />
                  <input
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 text-center border border-gray-300 rounded focus:border-primary focus:outline-none"
                    placeholder="0"
                  />
                  <input
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 text-center border border-gray-300 rounded focus:border-primary focus:outline-none"
                    placeholder="0"
                  />
                  <input
                    type="text"
                    maxLength="1"
                    className="w-12 h-12 text-center border border-gray-300 rounded focus:border-primary focus:outline-none"
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="flex space-x-4">
                <Button variant="contained" color="primary">
                  Enable MFA
                </Button>
                <Button variant="outlined" color="secondary">
                  Cancel
                </Button>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <Typography variant="h6" className="mb-4">
                  Additional Security Settings
                </Typography>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    component={Link}
                    variant="outlined"
                    color="primary"
                    startIcon={<i className="tabler-device-mobile" />}
                    href="/mfa/devices"
                    className="flex-1"
                  >
                    Manage Trusted Devices
                  </Button>
                  <Button
                    component={Link}
                    variant="outlined"
                    color="secondary"
                    startIcon={<i className="tabler-key" />}
                    href="/mfa/backup-codes"
                    className="flex-1"
                  >
                    View Backup Codes
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default MFASetupPage
