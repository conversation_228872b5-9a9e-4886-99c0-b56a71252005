'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Alert,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Button
} from '@mui/material'
import {
  Shield,
  Key,
  CheckCircle,
  Warning,
  Error,
  Info,
  AccessTime
} from '@mui/icons-material'
import { getMFAStatus } from '@/services/mfaApi'
import MFASettings from '@/components/MFA/MFASettings'

const AdminSecurityDashboard = () => {
  const { data: session, status } = useSession()
  const [userId, setUserId] = useState(null)
  const [userEmail, setUserEmail] = useState(null)
  const [mfaStatus, setMfaStatus] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    // Get real user data from NextAuth session
    if (session?.user) {
      setUserEmail(session.user.email)
      setUserId(session.user.id)
    }
  }, [session])

  useEffect(() => {
    if (userId) {
      loadSecurityData()
    }
  }, [userId])

  const loadSecurityData = async () => {
    try {
      setLoading(true)
      setError('')

      const statusResult = await getMFAStatus(userId)
      setMfaStatus(statusResult.data)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const getSecurityScore = () => {
    let score = 0
    let maxScore = 100

    // MFA enabled (60 points)
    if (mfaStatus?.mfaEnabled) score += 60

    // Recent MFA usage (25 points)
    if (mfaStatus?.lastMfaUsed) {
      const daysSinceLastUse = (new Date() - new Date(mfaStatus.lastMfaUsed)) / (1000 * 60 * 60 * 24)
      if (daysSinceLastUse <= 7) score += 25
      else if (daysSinceLastUse <= 30) score += 15
    }

    // Backup codes available (15 points)
    if (mfaStatus?.backupCodesCount > 0) score += 15

    return Math.min(score, maxScore)
  }

  const getSecurityLevel = (score) => {
    if (score >= 80) return { level: 'Excellent', color: 'success', icon: <CheckCircle /> }
    if (score >= 60) return { level: 'Good', color: 'info', icon: <Info /> }
    if (score >= 40) return { level: 'Fair', color: 'warning', icon: <Warning /> }
    return { level: 'Poor', color: 'error', icon: <Error /> }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString()
  }

  if (status === 'loading' || loading || !userId) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
          <LinearProgress sx={{ width: '100%' }} />
        </Box>
      </Container>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          You must be logged in to access the security dashboard.
        </Alert>
      </Container>
    )
  }

  const securityScore = getSecurityScore()
  const securityLevel = getSecurityLevel(securityScore)

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Admin Security Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Monitor and manage your account security settings and MFA devices.
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Security Score Card */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardHeader
              avatar={securityLevel.icon}
              title="Security Score"
              subheader={`${securityScore}/100`}
            />
            <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              <Box sx={{ mb: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={securityScore}
                  color={securityLevel.color}
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>
              <Chip
                label={securityLevel.level}
                color={securityLevel.color}
                variant="outlined"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* MFA Status Card */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardHeader
              avatar={<Shield />}
              title="MFA Status"
              subheader={mfaStatus?.mfaEnabled ? 'Enabled' : 'Disabled'}
            />
            <CardContent sx={{ flexGrow: 1 }}>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <Key />
                  </ListItemIcon>
                  <ListItemText
                    primary="Backup Codes"
                    secondary={`${mfaStatus?.backupCodesCount || 0} available`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <AccessTime />
                  </ListItemIcon>
                  <ListItemText
                    primary="Last Used"
                    secondary={formatDate(mfaStatus?.lastMfaUsed)}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activity */}
        <Grid item xs={12}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardHeader
              avatar={<AccessTime />}
              title="Recent Security Activity"
            />
            <CardContent sx={{ flexGrow: 1 }}>
              <List>
                {mfaStatus?.mfaSetupAt && (
                  <ListItem>
                    <ListItemIcon>
                      <Shield color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="MFA Enabled"
                      secondary={`Setup completed on ${formatDate(mfaStatus.mfaSetupAt)}`}
                    />
                  </ListItem>
                )}
                {!mfaStatus?.mfaEnabled && (
                  <ListItem>
                    <ListItemText
                      primary="No security activity yet"
                      secondary="Enable MFA to start tracking security events"
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* MFA Settings */}
        <Grid item xs={12}>
          <MFASettings userId={userId} userEmail={userEmail} />
        </Grid>
      </Grid>
    </Container>
  )
}

export default AdminSecurityDashboard
