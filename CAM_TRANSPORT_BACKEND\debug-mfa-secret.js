const mongoose = require('mongoose');
const speakeasy = require('speakeasy');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/CAM_TRANSPORT_SYSTEM', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

// Define the Login schema (same as in your app)
const LoginSchema = new mongoose.Schema({
    username: String,
    email: String,
    password: String,
    mfaSecret: String,
    mfaEnabled: Boolean,
    isVerified: <PERSON><PERSON><PERSON>,
    role: String
}, { timestamps: true });

const Login = mongoose.model('Login', LoginSchema);

async function debugMFASecret() {
    console.log('🔍 DEBUGGING MFA SECRET FOR USER');
    console.log('=================================\n');
    
    try {
        // Find the user
        const user = await Login.findOne({
            $or: [
                { username: 'dhruv' },
                { email: '<EMAIL>' }
            ]
        });
        
        if (!user) {
            console.log('❌ User not found!');
            process.exit(1);
        }
        
        console.log('✅ User found:');
        console.log('   ID:', user._id);
        console.log('   Username:', user.username);
        console.log('   Email:', user.email);
        console.log('   MFA Enabled:', user.mfaEnabled);
        console.log('   MFA Secret Length:', user.mfaSecret ? user.mfaSecret.length : 'null');
        console.log('   MFA Secret (first 10 chars):', user.mfaSecret ? user.mfaSecret.substring(0, 10) + '...' : 'null');
        
        if (!user.mfaSecret) {
            console.log('\n❌ CRITICAL ISSUE: No MFA secret found!');
            console.log('   The user needs to set up MFA again.');
            process.exit(1);
        }
        
        // Generate current TOTP token
        console.log('\n🔐 Generating current TOTP tokens:');
        
        const currentTime = Math.floor(Date.now() / 1000);
        const window = 1; // 30-second window
        
        for (let i = -window; i <= window; i++) {
            const time = currentTime + (i * 30);
            const token = speakeasy.totp({
                secret: user.mfaSecret,
                encoding: 'base32',
                time: time
            });
            
            const timeStr = new Date(time * 1000).toLocaleTimeString();
            console.log(`   Time ${i === 0 ? '(current)' : i > 0 ? '(+' + (i*30) + 's)' : '(' + (i*30) + 's)'}: ${token} at ${timeStr}`);
        }
        
        console.log('\n🎯 INSTRUCTIONS:');
        console.log('1. Check your authenticator app RIGHT NOW');
        console.log('2. Compare the code with the "current" token above');
        console.log('3. If they match, the secret is correct');
        console.log('4. If they don\'t match, the secret is wrong and needs to be reset');
        
        console.log('\n⏰ TIMING INFO:');
        console.log('   Current server time:', new Date().toLocaleString());
        console.log('   TOTP codes change every 30 seconds');
        console.log('   Window tolerance: ±30 seconds');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        mongoose.connection.close();
    }
}

debugMFASecret();
