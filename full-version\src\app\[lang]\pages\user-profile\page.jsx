// Next Imports
import dynamic from 'next/dynamic'

// MUI Imports
import Button from '@mui/material/Button'

// Layout Imports
import LayoutWrapper from '@layouts/LayoutWrapper'
import VerticalLayout from '@layouts/VerticalLayout'
import HorizontalLayout from '@layouts/HorizontalLayout'

// Component Imports
import UserProfile from '@views/pages/user-profile'
import Providers from '@components/Providers'
import Navigation from '@components/layout/vertical/Navigation'
import Header from '@components/layout/horizontal/Header'
import Navbar from '@components/layout/vertical/Navbar'
import VerticalFooter from '@components/layout/vertical/Footer'
import HorizontalFooter from '@components/layout/horizontal/Footer'
import ScrollToTop from '@core/components/scroll-to-top'
import AuthGuard from '@/hocs/AuthGuard'
import ClientAuthGuard from '@components/ClientAuthGuard'

// Data Imports
import { getProfileData } from '@/app/server/actions'

// Config Imports
import { i18n } from '@configs/i18n'

// Util Imports
import { getDictionary } from '@/utils/getDictionary'
import { getMode, getSystemMode } from '@core/utils/serverHelpers'

const ProfileTab = dynamic(() => import('@views/pages/user-profile/profile'))

// Vars
const tabContentList = data => ({
    profile: <ProfileTab data={data?.users.profile} />
})

const ProfilePage = async ({ params }) => {
    // Vars
    const data = await getProfileData()
    const lang = params.lang
    const direction = i18n.langDirection[lang]
    const dictionary = await getDictionary(lang)
    const mode = await getMode()
    const systemMode = await getSystemMode()

    return (
        <Providers direction={direction}>
            <AuthGuard locale={lang}>
                <ClientAuthGuard>
                    <LayoutWrapper
                        systemMode={systemMode}
                        verticalLayout={
                            <VerticalLayout
                                navigation={<Navigation dictionary={dictionary} mode={mode} />}
                                navbar={<Navbar />}
                                footer={<VerticalFooter />}
                            >
                                <UserProfile data={data} tabContentList={tabContentList(data)} />
                            </VerticalLayout>
                        }
                        horizontalLayout={
                            <HorizontalLayout header={<Header dictionary={dictionary} />} footer={<HorizontalFooter />}>
                                <UserProfile data={data} tabContentList={tabContentList(data)} />
                            </HorizontalLayout>
                        }
                    />
                    <ScrollToTop className='mui-fixed'>
                        <Button
                            variant='contained'
                            className='is-10 bs-10 rounded-full p-0 min-is-0 flex items-center justify-center'
                        >
                            <i className='tabler-arrow-up' />
                        </Button>
                    </ScrollToTop>
                </ClientAuthGuard>
            </AuthGuard>
        </Providers>
    )
}

export default ProfilePage
