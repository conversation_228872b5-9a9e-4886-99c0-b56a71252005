// MUI Imports
import Grid from '@mui/material/Grid2'

// Component Imports
import CurrentPlan from './CurrentPlan'
import Address from './Address'
import PaymentMethod from './PaymentMethod'
import InvoiceListTable from './InvoiceListTable'

const BillingPlans = async () => {
  return (
    <Grid container spacing={6}>
      <Grid size={{ xs: 12 }}>
        <CurrentPlan />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <PaymentMethod />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <Address />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <InvoiceListTable />
      </Grid>
    </Grid>
  )
}

export default BillingPlans
