'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  Container,
  Typography,
  Box,
  Alert,
  CircularProgress
} from '@mui/material'
import MFASettings from '@/components/MFA/MFASettings'

const MFASettingsPage = () => {
  const { data: session, status } = useSession()
  const [userId, setUserId] = useState(null)
  const [userEmail, setUserEmail] = useState(null)

  useEffect(() => {
    // Get real user data from NextAuth session
    if (session?.user) {
      setUserEmail(session.user.email)
      setUserId(session.user.id) // Use real user ID from session
    }
  }, [session])

  if (status === 'loading') {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
          <CircularProgress />
        </Box>
      </Container>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="error">
          You must be logged in to access MFA settings.
        </Alert>
      </Container>
    )
  }

  if (!userId) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Alert severity="warning">
          Unable to load user information. Please try refreshing the page.
        </Alert>
      </Container>
    )
  }

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Security Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your account security and two-factor authentication settings.
        </Typography>
      </Box>

      <MFASettings userId={userId} userEmail={userEmail} />


    </Container>
  )
}

export default MFASettingsPage
