const mongoose = require('mongoose');
const Login = require('./model/Login');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/CAM_TRANSPORT', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function testUserManagement() {
  try {
    console.log('🔍 Testing User Management API...');
    
    // Check if any users exist
    const totalUsers = await Login.countDocuments();
    console.log(`📊 Total users in database: ${totalUsers}`);
    
    // Check super admin users
    const superAdmins = await Login.find({ role: 'super_admin' });
    console.log(`👑 Super admin users: ${superAdmins.length}`);
    
    if (superAdmins.length > 0) {
      console.log('Super admin users:');
      superAdmins.forEach(admin => {
        console.log(`  - ${admin.username} (${admin.email}) - Active: ${admin.isActive}`);
      });
    }
    
    // Test API endpoint
    const API_BASE_URL = 'http://localhost:8090';
    
    console.log('\n🌐 Testing /user-profile/users endpoint...');
    
    const response = await fetch(`${API_BASE_URL}/user-profile/users?page=1&limit=10`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`📡 Response status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ API Response:', JSON.stringify(result, null, 2));
    } else {
      const errorText = await response.text();
      console.log('❌ API Error:', errorText);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

testUserManagement();
