const axios = require('axios');

async function testLoginNoMFA() {
    console.log('🧪 TESTING LOGIN WITHOUT MFA');
    console.log('============================\n');
    
    try {
        console.log('🔐 Attempting login...');
        const loginResponse = await axios.post('http://localhost:8090/login', {
            username: 'dhruv',
            email: '<EMAIL>',
            password: 'dhruv@123'
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Login response:', JSON.stringify(loginResponse.data, null, 2));
        
        if (loginResponse.data.requiresMFA) {
            console.log('❌ MFA is still required - disable didn\'t work');
        } else if (loginResponse.data.requiresEmailOTP) {
            console.log('✅ Only email OTP required (normal for non-MFA users)');
        } else {
            console.log('✅ Login successful without MFA!');
        }
        
    } catch (error) {
        console.error('❌ Login failed:', error.response?.data || error.message);
    }
}

testLoginNoMFA();
