<!DOCTYPE html>
<html>
<head>
    <title>Backend API Test</title>
</head>
<body>
    <h1>Backend API Test</h1>
    <button onclick="testGetContacts()">Test Get Contacts</button>
    <button onclick="testUpdateStatus()">Test Update Status</button>
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8090';

        async function testGetContacts() {
            try {
                const response = await fetch(`${API_BASE_URL}/contact/get-contacts`);
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h3>Get Contacts Result:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Get Contacts Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testUpdateStatus() {
            try {
                // First get contacts to get a valid ID
                const getResponse = await fetch(`${API_BASE_URL}/contact/get-contacts`);
                const contacts = await getResponse.json();
                
                if (contacts.length === 0) {
                    document.getElementById('results').innerHTML = `
                        <h3>Update Status Error:</h3>
                        <pre>No contacts found to test with</pre>
                    `;
                    return;
                }

                const contactId = contacts[0]._id;
                
                // Test update status
                const updateResponse = await fetch(`${API_BASE_URL}/contact/update-status/${contactId}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ status: 'in-view' })
                });
                
                const result = await updateResponse.json();
                document.getElementById('results').innerHTML = `
                    <h3>Update Status Result:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Update Status Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
