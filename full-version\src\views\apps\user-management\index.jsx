'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Grid from '@mui/material/Grid'

// Component Imports
import UserManagementList from './UserManagementList'

const UserManagement = ({ userData }) => {
  // States
  const [data, setData] = useState(userData)

  return (
    <Grid container spacing={6}>
      <Grid item xs={12}>
        <UserManagementList tableData={data} setData={setData} />
      </Grid>
    </Grid>
  )
}

export default UserManagement
