// Next Imports
import { useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'

// MUI Imports
import { useTheme } from '@mui/material/styles'

// Third-party Imports
import PerfectScrollbar from 'react-perfect-scrollbar'

// Component Imports
import { Menu, MenuItem, MenuSection } from '@menu/vertical-menu'

// Hook Imports
import useVerticalNav from '@menu/hooks/useVerticalNav'

// Utils Imports
// Temporary fallback for roleUtils
const isSuperAdmin = (userRole) => {
  return userRole === 'super_admin'
}
// import { isSuperAdmin } from '@/utils/roleUtils'

// Style Imports
import menuItemStyles from '@core/styles/vertical/menuItemStyles'
import menuSectionStyles from '@core/styles/vertical/menuSectionStyles'

const VerticalMenu = ({ dictionary, scrollMenu }) => {
  // Hooks
  const theme = useTheme()
  const verticalNavOptions = useVerticalNav()
  const params = useParams()
  const { data: session } = useSession()

  // Vars
  const { isBreakpointReached } = verticalNavOptions
  const { lang: locale } = params
  const userRole = session?.user?.role

  console.log('Dictionary in VerticalMenu:', dictionary);
  console.log('🔍 VerticalMenu session data:', {
    hasSession: !!session,
    userRole: userRole,
    userEmail: session?.user?.email,
    isSuperAdmin: isSuperAdmin(userRole)
  });

  const ScrollWrapper = isBreakpointReached ? 'div' : PerfectScrollbar

  return (
    // eslint-disable-next-line lines-around-comment
    /* Custom scrollbar instead of browser scroll, remove if you want browser scroll only */
    <ScrollWrapper
      {...(isBreakpointReached
        ? {
          className: 'bs-full overflow-y-auto overflow-x-hidden',
          onScroll: container => scrollMenu(container, false)
        }
        : {
          options: { wheelPropagation: false, suppressScrollX: true },
          onScrollY: container => scrollMenu(container, true)
        })}
    >
      {/* Incase you also want to scroll NavHeader to scroll with Vertical Menu, remove NavHeader from above and paste it below this comment */}
      {/* Vertical Menu */}
      <Menu
        popoutMenuOffset={{ mainAxis: 23 }}
        menuItemStyles={menuItemStyles(verticalNavOptions, theme)}
        menuSectionStyles={menuSectionStyles(verticalNavOptions, theme)}
      >
        {/* Apps - Direct Menu Items */}
        <MenuSection label="Apps">
          <MenuItem href={`/${locale}/apps/quotes`} icon={<i className='tabler-mail-question' />}>
            Quotes
          </MenuItem>
          <MenuItem href={`/${locale}/apps/user/contact`} icon={<i className='tabler-users' />}>
            Contact
          </MenuItem>
          <MenuItem href={`/${locale}/apps/user/jobs`} icon={<i className='tabler-briefcase' />}>
            Apply Job
          </MenuItem>
          <MenuItem href={`/${locale}/apps/user/urgent`} icon={<i className='tabler-urgent' />}>
            Urgent Inquiry
          </MenuItem>
        </MenuSection>

        {/* Pages - Direct Menu Items */}
        <MenuSection label="Pages">
          <MenuItem href={`/${locale}/pages/user-profile`} icon={<i className='tabler-user-circle' />}>
            User Profile
          </MenuItem>
          {/* User Management - Only for Super Admins */}
          {isSuperAdmin(userRole) && (
            <MenuItem href={`/${locale}/apps/user-management`} icon={<i className='tabler-users-group' />}>
              User Management
            </MenuItem>
          )}
        </MenuSection>

        {/* Authentication - Direct Menu Items */}
        <MenuSection label="Authentication">
          <MenuItem href={`/${locale}/pages/auth/login-v2`} icon={<i className='tabler-login' />} target='_blank'>
            Login
          </MenuItem>
          <MenuItem href={`/${locale}/apps/admin-security`} icon={<i className='tabler-shield' />}>
            Security Dashboard
          </MenuItem>
          <MenuItem href={`/${locale}/apps/mfa-settings`} icon={<i className='tabler-shield-check' />}>
            MFA Settings
          </MenuItem>
          <MenuItem href={`/${locale}/apps/mfa-demo`} icon={<i className='tabler-shield-lock' />}>
            MFA Demo
          </MenuItem>
        </MenuSection>

      </Menu>
    </ScrollWrapper>
  )
}

export default VerticalMenu
