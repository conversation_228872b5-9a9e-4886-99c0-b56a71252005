'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Divider
} from '@mui/material'
import { Security, Shield, Key, Download, Warning } from '@mui/icons-material'
import { getMFAStatus, disableMFA, generateBackupCodes } from '@/services/mfaApi'
import MFASetup from './MFASetup'
import DeviceManagement from './DeviceManagement'

const MFASettings = ({ userId, userEmail }) => {
  const [mfaStatus, setMfaStatus] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [setupOpen, setSetupOpen] = useState(false)
  const [disableDialogOpen, setDisableDialogOpen] = useState(false)
  const [password, setPassword] = useState('')
  const [backupCodes, setBackupCodes] = useState([])
  const [backupCodesDialogOpen, setBackupCodesDialogOpen] = useState(false)

  useEffect(() => {
    loadMFAStatus()
  }, [userId])

  const loadMFAStatus = async () => {
    try {
      setLoading(true)
      setError('')

      const result = await getMFAStatus(userId)
      setMfaStatus(result.data)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleDisableMFA = async () => {
    if (!password) {
      setError('Password is required to disable MFA')
      return
    }

    try {
      setLoading(true)
      setError('')

      await disableMFA(userId, password)
      setMfaStatus({ ...mfaStatus, mfaEnabled: false })
      setDisableDialogOpen(false)
      setPassword('')

      // Show success message
      alert('MFA has been disabled successfully')
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateBackupCodes = async () => {
    try {
      setLoading(true)
      setError('')

      const result = await generateBackupCodes(userId)
      setBackupCodes(result.data.backupCodes)
      setBackupCodesDialogOpen(true)

      // Update backup codes count
      setMfaStatus({ ...mfaStatus, backupCodesCount: result.data.backupCodes.length })
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const downloadBackupCodes = () => {
    const content = `CAM Transport - MFA Backup Codes\n\nGenerated: ${new Date().toLocaleString()}\nUser: ${userEmail}\n\nBackup Codes:\n${backupCodes.join('\n')}\n\nImportant:\n- Keep these codes safe and secure\n- Each code can only be used once\n- Use these codes if you lose access to your authenticator app\n- Generate new codes if you suspect these have been compromised`

    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'cam-transport-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (loading && !mfaStatus) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading MFA settings...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader
          avatar={<Security sx={{ color: 'primary.main' }} />}
          title="Two-Factor Authentication"
          subheader="Add an extra layer of security to your account"
        />
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mb: 3 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={mfaStatus?.mfaEnabled || false}
                  disabled={loading}
                />
              }
              label={
                <Box>
                  <Typography variant="body1">
                    Two-Factor Authentication
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {mfaStatus?.mfaEnabled
                      ? 'Your account is protected with 2FA'
                      : 'Secure your account with an authenticator app'
                    }
                  </Typography>
                </Box>
              }
            />
          </Box>

          {mfaStatus?.mfaEnabled ? (
            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Shield sx={{ color: 'success.main' }} />
                <Typography variant="h6" color="success.main">
                  MFA is Enabled
                </Typography>
              </Box>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Enabled on:
                  </Typography>
                  <Typography variant="body1">
                    {mfaStatus.mfaSetupAt ? new Date(mfaStatus.mfaSetupAt).toLocaleDateString() : 'Unknown'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Last used:
                  </Typography>
                  <Typography variant="body1">
                    {mfaStatus.lastMfaUsed ? new Date(mfaStatus.lastMfaUsed).toLocaleDateString() : 'Never'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    Backup codes remaining:
                  </Typography>
                  <Chip
                    label={`${mfaStatus.backupCodesCount || 0} codes`}
                    color={mfaStatus.backupCodesCount > 3 ? 'success' : 'warning'}
                    size="small"
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="outlined"
                  startIcon={<Key />}
                  onClick={handleGenerateBackupCodes}
                  disabled={loading}
                >
                  Generate New Backup Codes
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<Warning />}
                  onClick={() => setDisableDialogOpen(true)}
                  disabled={loading}
                >
                  Disable MFA
                </Button>
              </Box>
            </Box>
          ) : (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                Two-factor authentication is not enabled. Enable it now to secure your account.
              </Alert>

              <Button
                variant="contained"
                startIcon={<Security />}
                onClick={() => setSetupOpen(true)}
                disabled={loading}
                size="large"
              >
                Enable Two-Factor Authentication
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Device Management - Show only if MFA is enabled */}
      {mfaStatus?.mfaEnabled && (
        <Box sx={{ mt: 3 }}>
          <DeviceManagement
            userId={userId}
            onDeviceChange={loadMFAStatus}
          />
        </Box>
      )}

      {/* MFA Setup Dialog */}
      <MFASetup
        open={setupOpen}
        onClose={() => setSetupOpen(false)}
        userId={userId}
        onSuccess={(message) => {
          alert(message)
          loadMFAStatus()
        }}
      />

      {/* Disable MFA Dialog */}
      <Dialog open={disableDialogOpen} onClose={() => setDisableDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Warning color="error" />
            Disable Two-Factor Authentication
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Disabling two-factor authentication will make your account less secure. Are you sure you want to continue?
          </Alert>
          <TextField
            fullWidth
            type="password"
            label="Enter your password to confirm"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDisableDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            color="error"
            variant="contained"
            onClick={handleDisableMFA}
            disabled={loading || !password}
          >
            {loading ? 'Disabling...' : 'Disable MFA'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Backup Codes Dialog */}
      <Dialog open={backupCodesDialogOpen} onClose={() => setBackupCodesDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Key />
            New Backup Codes Generated
          </Box>
        </DialogTitle>
        <DialogContent>
          <Alert severity="success" sx={{ mb: 2 }}>
            Your new backup codes have been generated. Save them in a secure location.
          </Alert>

          <Grid container spacing={1} sx={{ mb: 2 }}>
            {backupCodes.map((code, index) => (
              <Grid item xs={6} key={index}>
                <Chip
                  label={code}
                  variant="outlined"
                  sx={{ fontFamily: 'monospace', width: '100%' }}
                />
              </Grid>
            ))}
          </Grid>

          <Alert severity="warning">
            These codes replace your previous backup codes. Each code can only be used once.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            startIcon={<Download />}
            onClick={downloadBackupCodes}
          >
            Download Codes
          </Button>
          <Button variant="contained" onClick={() => setBackupCodesDialogOpen(false)}>
            I've Saved Them
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}

export default MFASettings
