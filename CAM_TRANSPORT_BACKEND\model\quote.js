const mongoose = require('mongoose');

const DeckQuoteSchema = new mongoose.Schema({
    quoteType: { type: String, enum: ['deck', 'quote'], required: true },
    contactInfo: {
        fullName: { type: String, required: true },
        companyName: { type: String },
        phoneNumber: { type: String, required: true },
        emailAddress: { type: String, required: true },
        preferredContactMethod: { type: String, enum: ['Phone', 'Email'], required: true }
    },
    shipmentDetails: {
        itemDescription: { type: String, required: true },
        loadType: {
            type: String,
            enum: ['Standard Flatbed', 'Step Deck', 'Lowboy', 'Oversized / Over dimensional'],
            required: true
        },
        loadDimensions: {
            length: { type: Number, required: true },
            width: { type: Number, required: true },
            height: { type: Number, required: true },
            unit: { type: String, enum: ['ft', 'm'], required: true }
        },
        approximateWeight: {
            value: { type: Number, required: true },
            unit: { type: String, enum: ['lbs', 'kgs'], required: true }
        }
    },
    pickupDelivery: {
        pickupLocation: {
            city: { type: String, required: true },
            stateOrProvince: { type: String, required: true }
        },
        deliveryLocation: {
            city: { type: String, required: true },
            stateOrProvince: { type: String, required: true }
        },
        preferredPickupDate: { type: Date, required: true },
        preferredDeliveryDate: { type: Date, required: true },
        deliveryAssistanceRequired: { type: Boolean, default: false }
    },
    specialRequirements: {
        requiresPermitsOrEscorts: { type: Boolean, default: false },
        specialHandlingInstructions: { type: String },
        deliveryType: { type: String, enum: ['Recurring', 'One-time'] }
    }
});

const DeckQuote = mongoose.model('DeckQuote', DeckQuoteSchema);

module.exports = DeckQuote;
