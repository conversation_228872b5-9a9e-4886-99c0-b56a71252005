// API service for job application management
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

// Fetch all job applications from backend
export const fetchJobApplications = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/jobs/get-jobs`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const applications = await response.json()

    // Transform backend data to match frontend structure
    return applications.map(app => ({
      id: app._id,
      fullName: `${app.first_name} ${app.last_name}`,
      firstName: app.first_name,
      lastName: app.last_name,
      email: app.email,
      phone: app.phone_number,
      phoneNumber: app.phone_number,
      dob: app.dob,
      address: app.address,
      position: app.position,
      specificDrivingRole: app.specific_driving_role,
      specificNonDrivingRole: app.specific_non_driving_role,
      commercialLicense: app.commercial_license,
      otherJob: app.other_job,
      employmentType: app.employment_type,
      employment_type: app.employment_type,
      preferredStartDate: app.preferred_start_date,
      relocate: app.relocate,
      experience: app.experience,
      resume: app.resume,
      workReason: app.work_reason,
      reference: app.reference,
      otherReference: app.other_reference,
      ip: app.ip,
      status: 'pending', // Default status for new applications (frontend-only)
      appliedDate: app.createdAt, // Keep full timestamp for dynamic time display
      createdAt: app.createdAt,
      updatedAt: app.updatedAt,
      // Add avatar placeholder
      avatar: null
    }))
  } catch (error) {
    console.error('Error fetching job applications:', error)
    throw error
  }
}

// Update job application status
export const updateJobApplicationStatus = async (applicationId, status) => {
  try {
    console.log('API: Updating job application status', applicationId, 'to', status)
    console.log('API URL:', `${API_BASE_URL}/jobs/update-status/${applicationId}`)

    const response = await fetch(`${API_BASE_URL}/jobs/update-status/${applicationId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status })
    })

    console.log('API Response status:', response.status)
    console.log('API Response ok:', response.ok)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('API Success result:', result)
    return result
  } catch (error) {
    console.error('❌ Error updating job application status:', error)
    throw error
  }
}

// Delete job application from backend
export const deleteJobApplication = async (applicationId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/jobs/delete-job/${applicationId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Error deleting job application:', error)
    throw error
  }
}

// Download resume file
export const downloadResume = async (resumePath, applicantName) => {
  try {
    console.log('=== FRONTEND DOWNLOAD DEBUG ===')
    console.log('Resume path from DB:', resumePath)
    console.log('Applicant name:', applicantName)
    console.log('API Base URL:', API_BASE_URL)

    // Extract just the filename from the path
    // Handle cases where resumePath might be "uploads/resumes/filename.pdf" or just "filename.pdf"
    let fileName = resumePath

    // Remove any path components - split by both forward and back slashes
    if (resumePath.includes('/') || resumePath.includes('\\')) {
      const parts = resumePath.split(/[/\\]/)
      fileName = parts[parts.length - 1] // Get the last part (filename)
    }

    console.log('Original resume path:', resumePath)
    console.log('Extracted filename:', fileName)

    // Double check - if fileName still contains path separators, something is wrong
    if (fileName.includes('/') || fileName.includes('\\')) {
      console.error('❌ Filename still contains path separators:', fileName)
      // Force extract just the actual filename
      fileName = fileName.replace(/.*[/\\]/, '')
      console.log('Force extracted filename:', fileName)
    }

    // Validate the filename before making the request
    if (!fileName || fileName.trim() === '') {
      throw new Error('Invalid filename extracted from resume path')
    }

    // Use the dedicated download route
    const downloadUrl = `${API_BASE_URL}/jobs/download-resume/${fileName}`
    console.log('Download URL:', downloadUrl)

    // Final validation - make sure URL doesn't have double paths
    if (downloadUrl.includes('uploads/resumes') && downloadUrl.split('uploads/resumes').length > 2) {
      console.error('❌ URL contains duplicate path components:', downloadUrl)
      throw new Error('Invalid download URL generated')
    }

    const response = await fetch(downloadUrl, {
      method: 'GET',
      mode: 'cors',
      credentials: 'omit',
      headers: {
        'Accept': '*/*'
      }
    })

    console.log('Response status:', response.status)
    console.log('Response ok:', response.ok)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Response error:', errorText)
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const blob = await response.blob()
    console.log('✅ Download successful! Blob size:', blob.size, 'Type:', blob.type)

    // Create download link
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // Get file extension and create clean filename
    const fileExtension = fileName.split('.').pop() || 'pdf'
    const cleanApplicantName = applicantName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_')
    const downloadFileName = `${cleanApplicantName}_Resume.${fileExtension}`

    link.download = downloadFileName
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()

    // Cleanup after a short delay
    setTimeout(() => {
      window.URL.revokeObjectURL(url)
      document.body.removeChild(link)
    }, 100)

    console.log('✅ Resume downloaded successfully:', downloadFileName)
    return { success: true, fileName: downloadFileName }

  } catch (error) {
    console.error('❌ Error downloading resume:', error)

    // Try fallback method with direct static file access
    try {
      console.log('Trying fallback method...')
      const fileName = resumePath.split('/').pop() || resumePath
      const fallbackUrl = `${API_BASE_URL}/uploads/resumes/${fileName}`
      console.log('Fallback URL:', fallbackUrl)

      const fallbackResponse = await fetch(fallbackUrl, {
        method: 'GET',
        mode: 'cors'
      })

      if (fallbackResponse.ok) {
        const blob = await fallbackResponse.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        const fileExtension = fileName.split('.').pop() || 'pdf'
        const cleanApplicantName = applicantName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_')
        const downloadFileName = `${cleanApplicantName}_Resume.${fileExtension}`

        link.download = downloadFileName
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()

        setTimeout(() => {
          window.URL.revokeObjectURL(url)
          document.body.removeChild(link)
        }, 100)

        console.log('✅ Fallback download successful:', downloadFileName)
        return { success: true, fileName: downloadFileName }
      }
    } catch (fallbackError) {
      console.error('❌ Fallback method also failed:', fallbackError)
    }

    throw error
  }
}

// Get job application by ID
export const getJobApplicationById = async (applicationId) => {
  try {
    const applications = await fetchJobApplications()
    return applications.find(app => app.id === applicationId)
  } catch (error) {
    console.error('Error fetching job application by ID:', error)
    throw error
  }
}

// Test function to debug filename extraction
export const testFilenameExtraction = (resumePath) => {
  console.log('=== FILENAME EXTRACTION TEST ===')
  console.log('Input:', resumePath)

  let fileName = resumePath

  if (resumePath.includes('/') || resumePath.includes('\\')) {
    const parts = resumePath.split(/[/\\]/)
    fileName = parts[parts.length - 1]
  }

  console.log('Output:', fileName)
  console.log('Expected URL:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'}/jobs/download-resume/${fileName}`)

  return fileName
}
