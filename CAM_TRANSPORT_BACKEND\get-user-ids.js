const mongoose = require('mongoose');
const Login = require('./model/Login');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cam_transport');

async function getUserIds() {
  try {
    console.log('🔍 Getting user ObjectIds...');
    
    // Get all users with their ObjectIds
    const allUsers = await Login.find({}).select('_id username email role adminId');
    
    console.log(`📊 Total users: ${allUsers.length}`);
    
    allUsers.forEach((user, index) => {
      console.log(`\n${index + 1}. MongoDB ObjectId: ${user._id}`);
      console.log(`   Username: ${user.username}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   Admin ID: ${user.adminId}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

getUserIds();
