const Joi = require("joi");
const { isValidPhoneNumber } = require("libphonenumber-js");

const noMongoKeys = (value, helpers) => {
    if (value.includes("$")) {
        return helpers.error("any.invalid");
    }
    return value;
};

const contactValidationSchema = Joi.object({
    name: Joi.string()
        .trim()
        .min(3)
        .max(100)
        .custom(noMongoKeys)
        .required()
        .messages({
            "string.empty": "Name is required",
            "string.min": "Name must be at least 3 characters",
            "string.max": "Name must be less than 100 characters",
            "any.invalid": "Name contains invalid characters"
        }),

    email: Joi.string()
        .trim()
        .email({ tlds: false })
        .pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
        .required()
        .messages({
            "string.empty": "Email is required",
            "string.pattern.base": "Email format is invalid. Please remove any spaces.",
            "string.email": "Email must be a valid format"
        }),

    contact: Joi.string()
        .trim()
        .required()
        .custom((value, helpers) => {
            const cleaned = value.replace(/\s+/g, "");
            if (!isValidPhoneNumber(cleaned)) {
                return helpers.error("any.invalid");
            }
            return value;
        })
        .messages({
            "any.invalid": "Please enter a valid phone number (e.g. +91 9574736637)",
            "string.empty": "Phone number is required"
        }),

    company: Joi.string()
        .trim()
        .min(5)
        .max(200)
        .allow("")
        .custom(noMongoKeys)
        .messages({
            "string.min": "Company name must be at least 5 characters",
            "string.max": "Company name must be less than 200 characters",
            "any.invalid": "Company name contains invalid characters"
        }),

    inquiryType: Joi.string()
        .valid("General Inquiry", "Book a Shipment", "Track a Shipment", "Other")
        .required()
        .messages({
            "any.only": "Please select a valid inquiry type",
            "string.empty": "Inquiry type is required"
        }),

    otherInquiry: Joi.string()
        .trim()
        .when("inquiryType", {
            is: "Other",
            then: Joi.string().min(1).required().messages({
                "string.empty": "Other inquiry must be provided when 'Other' is selected",
                "any.required": "Other inquiry is required when 'Other' is selected"
            }),
            otherwise: Joi.optional()
        })
        .max(150)
        .allow("")
        .custom(noMongoKeys)
        .messages({
            "string.max": "Other inquiry must be less than 150 characters",
            "any.invalid": "Other inquiry contains invalid characters"
        }),

    message: Joi.string()
        .trim()
        .max(300)
        .required()
        .custom(noMongoKeys)
        .messages({
            "string.empty": "Message is required",
            "string.max": "Message must be less than 300 characters",
            "any.invalid": "Message contains invalid characters"
        }),

    ip: Joi.string()
        .ip({ version: ["ipv4", "ipv6"], cidr: "optional" })
        .optional()
        .messages({
            "string.ip": "Invalid IP address format"
        }),

    _honeypot: Joi.string().allow("")
});

module.exports = { contactValidationSchema };
