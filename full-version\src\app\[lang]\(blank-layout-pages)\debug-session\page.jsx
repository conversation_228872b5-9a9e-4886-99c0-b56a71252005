'use client'

// React Imports
import { useState, useEffect } from 'react'
import { useSession, signIn, signOut } from 'next-auth/react'

// MUI Imports
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import <PERSON>ert from '@mui/material/Alert'
import Box from '@mui/material/Box'
import TextField from '@mui/material/TextField'

const DebugSession = () => {
  const { data: session, status } = useSession()
  const [loginData, setLoginData] = useState({
    username: 'dhruv',
    email: '<EMAIL>',
    password: 'dhruv@123'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [loginResult, setLoginResult] = useState(null)

  const handleLogin = async () => {
    setIsLoading(true)
    setLoginResult(null)

    try {
      console.log('🔍 Attempting login with NextAuth...')
      
      const result = await signIn('credentials', {
        username: loginData.username,
        email: loginData.email,
        password: loginData.password,
        redirect: false
      })

      console.log('🔍 NextAuth signIn result:', result)
      setLoginResult(result)

    } catch (error) {
      console.error('Login error:', error)
      setLoginResult({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut({ redirect: false })
    setLoginResult(null)
  }

  return (
    <Box sx={{ padding: 4, maxWidth: 800, margin: '0 auto' }}>
      <Typography variant='h4' gutterBottom>
        NextAuth Session Debug 🔍
      </Typography>

      <Alert severity='info' sx={{ mb: 3 }}>
        <Typography variant='body2'>
          This page helps debug the NextAuth session and login flow.
        </Typography>
      </Alert>

      {/* Session Status */}
      <Alert severity={status === 'authenticated' ? 'success' : 'warning'} sx={{ mb: 3 }}>
        <Typography variant='h6'>Session Status: {status}</Typography>
        {session && (
          <Typography variant='body2' component='pre' sx={{ whiteSpace: 'pre-wrap', mt: 1 }}>
            {JSON.stringify(session, null, 2)}
          </Typography>
        )}
      </Alert>

      {/* Login Form */}
      <Box sx={{ mb: 3, p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
        <Typography variant='h6' gutterBottom>Test Login</Typography>
        
        <TextField
          fullWidth
          label='Username'
          value={loginData.username}
          onChange={(e) => setLoginData(prev => ({ ...prev, username: e.target.value }))}
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          label='Email'
          value={loginData.email}
          onChange={(e) => setLoginData(prev => ({ ...prev, email: e.target.value }))}
          sx={{ mb: 2 }}
        />
        
        <TextField
          fullWidth
          label='Password'
          type='password'
          value={loginData.password}
          onChange={(e) => setLoginData(prev => ({ ...prev, password: e.target.value }))}
          sx={{ mb: 2 }}
        />

        <Button
          variant='contained'
          onClick={handleLogin}
          disabled={isLoading}
          sx={{ mr: 2 }}
        >
          {isLoading ? 'Logging in...' : 'Test Login'}
        </Button>

        <Button
          variant='outlined'
          onClick={handleLogout}
          disabled={isLoading}
        >
          Logout
        </Button>
      </Box>

      {/* Login Result */}
      {loginResult && (
        <Alert severity={loginResult.ok ? 'success' : 'error'} sx={{ mb: 3 }}>
          <Typography variant='h6'>Login Result:</Typography>
          <Typography variant='body2' component='pre' sx={{ whiteSpace: 'pre-wrap', mt: 1 }}>
            {JSON.stringify(loginResult, null, 2)}
          </Typography>
        </Alert>
      )}

      {/* Session Analysis */}
      {session?.user && (
        <Alert severity='info' sx={{ mb: 3 }}>
          <Typography variant='h6'>Session Analysis:</Typography>
          <Typography variant='body2'>
            • otpPending: {session.user.otpPending ? '✅ TRUE' : '❌ FALSE/UNDEFINED'}<br/>
            • otpType: {session.user.otpType || 'UNDEFINED'}<br/>
            • requiresEmailOTP: {session.user.requiresEmailOTP ? '✅ TRUE' : '❌ FALSE/UNDEFINED'}<br/>
            • requiresMFA: {session.user.requiresMFA ? '✅ TRUE' : '❌ FALSE/UNDEFINED'}<br/>
            • verificationPending: {session.user.verificationPending ? '✅ TRUE' : '❌ FALSE/UNDEFINED'}<br/>
            • verificationType: {session.user.verificationType || 'UNDEFINED'}<br/>
          </Typography>
        </Alert>
      )}

      {/* Expected Behavior */}
      <Alert severity='warning'>
        <Typography variant='h6'>Expected Behavior:</Typography>
        <Typography variant='body2'>
          When logging in with a user that requires Email OTP:<br/>
          • otpPending should be TRUE<br/>
          • otpType should be "email"<br/>
          • requiresEmailOTP should be TRUE<br/>
          • The login component should redirect to OTP verification page
        </Typography>
      </Alert>
    </Box>
  )
}

export default DebugSession
