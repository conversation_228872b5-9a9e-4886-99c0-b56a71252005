const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function designateSuperAdmin() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cam_transport', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('🔗 Connected to MongoDB');

        // Find the user <NAME_EMAIL>
        const adminUser = await Login.findOne({ email: '<EMAIL>' });

        if (!adminUser) {
            console.log('❌ User <NAME_EMAIL> not found');
            console.log('📝 Creating super admin user...');
            
            // Create the super admin user if it doesn't exist
            const bcrypt = require('bcrypt');
            const hashedPassword = await bcrypt.hash('admin', 10);
            
            const newSuperAdmin = new Login({
                username: 'admin',
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'super_admin',
                isVerified: true,
                isActive: true,
                adminId: 'super_admin_001',
                company: 'CAM Transport ltd.',
                mfaEnabled: false // Will be set up later
            });

            await newSuperAdmin.save();
            console.log('✅ Super admin user created successfully');
        } else {
            // Update existing user to super_admin role
            console.log('👤 Found existing user:', adminUser.email);
            console.log('🔄 Current role:', adminUser.role);

            if (adminUser.role === 'super_admin') {
                console.log('✅ User is already a super_admin');
            } else {
                adminUser.role = 'super_admin';
                await adminUser.save();
                console.log('✅ User role updated to super_admin');
            }
        }

        // Verify the change
        const updatedUser = await Login.findOne({ email: '<EMAIL>' });
        console.log('🔍 Verification - User role:', updatedUser.role);
        console.log('📧 Email:', updatedUser.email);
        console.log('👤 Username:', updatedUser.username);

        console.log('🎉 Super admin designation completed successfully!');

    } catch (error) {
        console.error('❌ Error designating super admin:', error);
    } finally {
        // Close the database connection
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

// Run the script
designateSuperAdmin();
