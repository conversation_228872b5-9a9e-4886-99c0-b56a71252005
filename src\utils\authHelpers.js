/**
 * Authentication helper functions
 */

import { signIn } from 'next-auth/react'

/**
 * Custom login function that handles email OTP redirection
 * @param {Object} credentials - Login credentials
 * @param {string} credentials.username - Username
 * @param {string} credentials.email - Email
 * @param {string} credentials.password - Password
 * @returns {Object} Login result
 */
export const customLogin = async (credentials) => {
  try {
    console.log('🔐 Custom login attempt:', { 
      email: credentials.email, 
      username: credentials.username 
    })

    // Attempt to sign in with NextAuth
    const result = await signIn('credentials', {
      ...credentials,
      redirect: false
    })

    console.log('📡 NextAuth sign in result:', result)

    // Check if login was successful
    if (result?.ok) {
      return {
        success: true,
        message: 'Login successful'
      }
    }

    // Check if this is an email OTP case
    if (result?.error) {
      // Try to parse the error to see if it contains redirect info
      try {
        const errorData = JSON.parse(result.error)
        if (errorData.needsEmailOTP && errorData.redirectTo) {
          return {
            success: false,
            needsEmailOTP: true,
            redirectTo: errorData.redirectTo,
            message: 'Email verification required'
          }
        }
      } catch (parseError) {
        console.log('Error is not JSON, treating as regular error')
      }
    }

    return {
      success: false,
      message: result?.error || 'Login failed'
    }

  } catch (error) {
    console.error('❌ Custom login error:', error)
    return {
      success: false,
      message: 'Login failed. Please try again.'
    }
  }
}

/**
 * Handle MFA verification
 * @param {Object} credentials - MFA credentials
 * @returns {Object} Verification result
 */
export const verifyMFA = async (credentials) => {
  try {
    console.log('🔐 MFA verification attempt')

    const result = await signIn('credentials', {
      ...credentials,
      step: 'mfa',
      redirect: false
    })

    if (result?.ok) {
      return {
        success: true,
        message: 'MFA verification successful'
      }
    }

    return {
      success: false,
      message: result?.error || 'MFA verification failed'
    }

  } catch (error) {
    console.error('❌ MFA verification error:', error)
    return {
      success: false,
      message: 'MFA verification failed. Please try again.'
    }
  }
}

/**
 * Check if user needs email OTP verification
 * @param {Object} user - User object
 * @returns {boolean} Whether user needs email OTP
 */
export const needsEmailOTP = (user) => {
  return user && !user.isVerified && !user.mfaEnabled
}

/**
 * Generate email OTP for user
 * @param {string} email - User email
 * @param {string} username - Username
 * @returns {Object} Generation result
 */
export const generateEmailOTP = async (email, username) => {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
    
    const response = await fetch(`${API_BASE_URL}/user-profile/email-otp/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, username })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      return {
        success: true,
        message: 'OTP sent to your email',
        userId: result.userId
      }
    }

    return {
      success: false,
      message: result.message || 'Failed to send OTP'
    }

  } catch (error) {
    console.error('❌ Error generating email OTP:', error)
    return {
      success: false,
      message: 'Failed to send OTP. Please try again.'
    }
  }
}

/**
 * Verify email OTP
 * @param {string} userId - User ID
 * @param {string} otp - OTP code
 * @returns {Object} Verification result
 */
export const verifyEmailOTP = async (userId, otp) => {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
    
    const response = await fetch(`${API_BASE_URL}/user-profile/email-otp/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, otp: parseInt(otp) })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      return {
        success: true,
        message: 'Email verified successfully',
        user: result.user
      }
    }

    return {
      success: false,
      message: result.message || 'OTP verification failed'
    }

  } catch (error) {
    console.error('❌ Error verifying email OTP:', error)
    return {
      success: false,
      message: 'Verification failed. Please try again.'
    }
  }
}
