{
  // JavaScript Snippets
  "exportDefault": {
    "prefix": "exp",
    "body": [
      "export default ${1:moduleName}",
      ""
    ]
  },
  "exportDestructing": {
    "prefix": "exd",
    "body": [
      "export { ${2:destructuredModule} } from '${1:module}'",
      ""
    ]
  },
  "exportAs": {
    "prefix": "exa",
    "body": [
      "export { ${2:originalName} as ${3:aliasName}} from '${1:module}'",
      ""
    ]
  },
  "exportDefaultFunction": {
    "prefix": "edf",
    "body": [
      "export default (${1:params}) => {",
      "  $0",
      "}",
      ""
    ]
  },
  "exportDefaultNamedFunction": {
    "prefix": "ednf",
    "body": [
      "export default function ${1:functionName}(${2:params}) {",
      "  $0",
      "}",
      ""
    ]
  },
  // React Snippets
  "reactArrowFunctionExportComponent": {
    "prefix": "rafce",
    "body": [
      "const ${1:ComponentName} = () => {",
      "  return (",
      "    $0",
      "  )",
      "}",
      "",
      "export default ${1:ComponentName}",
      ""
    ]
  },
  "reactArrowFunctionComponent": {
    "prefix": "rafc",
    "body": [
      "const ${1:ComponentName} = () => {",
      "  return (",
      "    $0",
      "  )",
      "}",
      ""
    ]
  },
  "reactUseState": {
    "prefix": "useState",
    "body": [
      "const [${1:state}, set${1/(.*)/${1:/capitalize}/}] = useState(${2:initialState})"
    ]
  },
  "reactUseEffect": {
    "prefix": "useEffect",
    "body": [
      "useEffect(() => {",
      "  $0",
      "}, [])"
    ]
  },
  // Type Snippets
  "exportType": {
    "prefix": "exptp",
    "body": [
      "export type ${1:Props} = {",
      "  ${2:name}: ${3:string}",
      "}",
      ""
    ]
  },
  "type": {
    "prefix": "tp",
    "body": [
      "type ${1:Props} = {",
      "  ${2:name}: ${3:string}",
      "}",
      ""
    ]
  },
  // Next Snippets
  "nextImport": {
    "prefix": "impn",
    "body": [
      "// Next Import",
      "import ${2:second} from '${1:first}'",
      ""
    ]
  }
}
