'use client'

// React Imports
import React, { useState, useMemo, useEffect } from 'react'

// Next Imports
import Link from 'next/link'
import { useParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Checkbox from '@mui/material/Checkbox'
import IconButton from '@mui/material/IconButton'
import { styled } from '@mui/material/styles'
import TablePagination from '@mui/material/TablePagination'
import MenuItem from '@mui/material/MenuItem'
import Chip from '@mui/material/Chip'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'
import CardContent from '@mui/material/CardContent'
import Select from '@mui/material/Select'
import FormControl from '@mui/material/FormControl'
import ListItemIcon from '@mui/material/ListItemIcon'
import Menu from '@mui/material/Menu'

// Third-party Imports
import classnames from 'classnames'
import { rankItem } from '@tanstack/match-sorter-utils'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

// Component Imports
import TableFilters from './TableFilters'
import ApplicantDetailsModal from './ApplicantDetailsModal'
import ResumeViewerModal from './ResumeViewerModal'
import CustomTextField from '@core/components/mui/TextField'
import CustomAvatar from '@core/components/mui/Avatar'
import TablePaginationComponent from '@components/TablePaginationComponent'

// PDF Export
import jsPDF from 'jspdf'
// Import autotable plugin
import 'jspdf-autotable'

// API Imports
import { fetchJobApplications, deleteJobApplication, downloadResume, updateJobApplicationStatus } from '@/services/jobApi'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

const fuzzyFilter = (row, columnId, value, addMeta) => {
  // Rank the item
  const itemRank = rankItem(row.getValue(columnId), value)

  // Store the itemRank info
  addMeta({
    itemRank
  })

  // Return if the item should be filtered in/out
  return itemRank.passed
}

const DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {
  // States
  const [value, setValue] = useState(initialValue)

  useEffect(() => {
    setValue(initialValue)
  }, [initialValue])
  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value)
    }, debounce)

    return () => clearTimeout(timeout)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value])

  return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />
}

// Column Definitions
// Status configuration
const statusConfig = {
  pending: {
    label: 'Pending',
    color: 'warning',
    icon: 'tabler-clock',
    description: 'Waiting for review'
  },
  'in-view': {
    label: 'In View',
    color: 'info',
    icon: 'tabler-eye',
    description: 'Being reviewed'
  },
  completed: {
    label: 'Completed',
    color: 'success',
    icon: 'tabler-check',
    description: 'Review completed'
  }
}

// Status options for dropdown
const statusOptions = [
  { value: 'pending', label: 'Pending', color: 'warning', icon: 'tabler-clock' },
  { value: 'in-view', label: 'In View', color: 'info', icon: 'tabler-eye' },
  { value: 'completed', label: 'Completed', color: 'success', icon: 'tabler-check' }
]

// Status Dropdown Component
const StatusDropdown = ({ currentStatus, onStatusChange, applicationId }) => {
  const [isOpen, setIsOpen] = useState(false)
  const config = statusConfig[currentStatus] || statusConfig.pending

  const handleStatusSelect = (newStatus) => {
    console.log('StatusDropdown: Selecting status', newStatus, 'for application', applicationId)
    onStatusChange(applicationId, newStatus)
    setIsOpen(false)
  }

  if (!isOpen) {
    // Show as button/chip when closed
    return (
      <Chip
        icon={<i className={`${config.icon} text-xs sm:text-sm`} />}
        label={config.label}
        color={config.color}
        variant='filled'
        size='small'
        className='text-xs sm:text-sm cursor-pointer'
        onClick={() => setIsOpen(true)}
        title="Click to change status"
        sx={{
          height: { xs: '28px', sm: '32px' },
          width: { xs: '100px', sm: '110px' },
          minWidth: { xs: '100px', sm: '110px' },
          maxWidth: { xs: '100px', sm: '110px' },
          fontSize: { xs: '0.75rem', sm: '0.8rem' },
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '& .MuiChip-label': {
            padding: { xs: '0 6px', sm: '0 8px' },
            fontSize: { xs: '0.7rem', sm: '0.75rem' },
            fontWeight: 500,
            whiteSpace: 'nowrap',
            overflow: 'visible',
            textOverflow: 'unset'
          },
          '& .MuiChip-icon': {
            fontSize: { xs: '14px', sm: '16px' },
            marginLeft: { xs: '6px', sm: '8px' },
            marginRight: { xs: '0px', sm: '2px' }
          },
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: 2
          }
        }}
      />
    )
  }

  // Show as dropdown when open
  return (
    <FormControl size="small" sx={{ minWidth: 120 }}>
      <Select
        value={currentStatus}
        onChange={(e) => handleStatusSelect(e.target.value)}
        onClose={() => setIsOpen(false)}
        open={isOpen}
        size="small"
        autoFocus
        sx={{
          height: '32px',
          fontSize: '0.75rem',
          '& .MuiSelect-select': {
            padding: '4px 8px',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }
        }}
      >
        {statusOptions.map((option) => (
          <MenuItem
            key={option.value}
            value={option.value}
            onClick={() => handleStatusSelect(option.value)}
          >
            <ListItemIcon sx={{ minWidth: '20px !important' }}>
              <i className={`${option.icon} text-sm`} />
            </ListItemIcon>
            <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
              {option.label}
            </Typography>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

const columnHelper = createColumnHelper()

const ApplyJobTable = () => {
  // States
  const [rowSelection, setRowSelection] = useState({})
  const [data, setData] = useState([])
  const [filteredData, setFilteredData] = useState([])
  const [globalFilter, setGlobalFilter] = useState('')
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [resumeModalOpen, setResumeModalOpen] = useState(false)
  const [selectedApplicant, setSelectedApplicant] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [actionMenuAnchor, setActionMenuAnchor] = useState(null)
  const [selectedApplicationId, setSelectedApplicationId] = useState(null)

  // Hooks
  const { lang: locale } = useParams()

  // Load job applications from backend
  const loadJobApplications = async () => {
    try {
      console.log('🔄 Loading job applications from backend...')
      setLoading(true)
      setError(null)

      const applications = await fetchJobApplications()
      console.log('✅ Fetched applications:', applications.length, 'items')
      console.log('📋 Sample application:', applications[0])

      // Restore saved statuses from localStorage
      const savedStatuses = JSON.parse(localStorage.getItem('jobApplicationStatuses') || '{}')
      const applicationsWithSavedStatuses = applications.map(application => ({
        ...application,
        status: savedStatuses[application.id] || application.status || 'pending'
      }))

      console.log('📦 Restored job application statuses from localStorage:', Object.keys(savedStatuses).length, 'items')

      setData(applicationsWithSavedStatuses)
      setFilteredData(applicationsWithSavedStatuses)
    } catch (err) {
      console.error('❌ Error loading job applications:', err)
      setError('Failed to load job applications. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Load job applications on component mount
  useEffect(() => {
    loadJobApplications()
  }, [])

  const handleViewDetails = (applicantData) => {
    setSelectedApplicant(applicantData)
    setDetailsModalOpen(true)
  }

  const handleViewResume = (applicantData) => {
    setSelectedApplicant(applicantData)
    setResumeModalOpen(true)
  }

  const handleStatusChange = async (applicationId, newStatus) => {
    console.log('Updating status for application:', applicationId, 'to:', newStatus)

    try {
      // Try to call API to update status in backend
      await updateJobApplicationStatus(applicationId, newStatus)
      console.log('Job application status updated in backend successfully')
    } catch (error) {
      console.error('Backend API error, using localStorage fallback:', error)

      // Fallback: Save status to localStorage for persistence
      const savedStatuses = JSON.parse(localStorage.getItem('jobApplicationStatuses') || '{}')
      savedStatuses[applicationId] = newStatus
      localStorage.setItem('jobApplicationStatuses', JSON.stringify(savedStatuses))
      console.log('Job application status saved to localStorage as fallback')
    }

    // Update local state regardless of backend success/failure
    setData(prevData =>
      prevData.map(item =>
        item.id === applicationId
          ? { ...item, status: newStatus }
          : item
      )
    )
    setFilteredData(prevData =>
      prevData.map(item =>
        item.id === applicationId
          ? { ...item, status: newStatus }
          : item
      )
    )

    console.log('Job application status updated in frontend successfully')
  }

  // Optional: Clear all saved statuses (for debugging)
  const clearSavedJobStatuses = () => {
    localStorage.removeItem('jobApplicationStatuses')
    console.log('Cleared all saved job application statuses')
  }

  const handleDeleteApplication = async (applicationId) => {
    try {
      // Find the application to get their name for confirmation
      const application = data.find(item => item.id === applicationId)
      const applicantName = application?.fullName || 'this application'

      // Show confirmation dialog
      const confirmed = window.confirm(
        `Are you sure you want to delete ${applicantName}'s application?\n\nThis action cannot be undone and will permanently remove the application from the database.`
      )

      if (!confirmed) {
        return
      }

      // Call backend API to delete application
      await deleteJobApplication(applicationId)

      // Remove application from local state
      setData(prevData => prevData.filter(item => item.id !== applicationId))
      setFilteredData(prevData => prevData.filter(item => item.id !== applicationId))

      // Clear selection if deleted application was selected
      setRowSelection(prevSelection => {
        const newSelection = { ...prevSelection }
        delete newSelection[applicationId]
        return newSelection
      })

      // Show success message
      alert(`${applicantName}'s application has been deleted successfully!`)

    } catch (error) {
      console.error('Error deleting application:', error)
      alert('Failed to delete application. Please try again.')
    }
  }

  const handleDownloadResume = async (applicantData) => {
    try {
      if (!applicantData.resume) {
        alert('No resume file found for this applicant.')
        return
      }

      console.log('Downloading resume for:', applicantData.fullName)
      console.log('Resume path:', applicantData.resume)

      await downloadResume(applicantData.resume, applicantData.fullName)
      alert('Resume downloaded successfully!')
    } catch (error) {
      console.error('Error downloading resume:', error)

      let errorMessage = 'Failed to download resume. '
      if (error.message.includes('404') || error.message.includes('not found')) {
        errorMessage += 'The resume file was not found on the server.'
      } else if (error.message.includes('network')) {
        errorMessage += 'Please check your internet connection.'
      } else {
        errorMessage += 'Please try again or contact support.'
      }

      alert(errorMessage)
    }
  }

  const handleActionMenuOpen = (event, applicationId) => {
    setActionMenuAnchor(event.currentTarget)
    setSelectedApplicationId(applicationId)
  }

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null)
    setSelectedApplicationId(null)
  }

  const columns = useMemo(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      columnHelper.accessor('fullName', {
        header: 'Applicant',
        cell: ({ row }) => (
          <div className='flex items-center gap-4'>
            <CustomAvatar
              variant='rounded'
              color='primary'
              skin='light'
              size={34}
            >
              {row.original.fullName?.charAt(0)?.toUpperCase()}
            </CustomAvatar>
            <div className='flex flex-col'>
              <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                {row.original.fullName}
              </Typography>
              <Typography variant='body1' color='text.primary' className='font-medium' style={{ fontSize: '1rem', letterSpacing: '1px' }}>
                {row.original.email}
              </Typography>
            </div>
          </div>
        )
      }),
      columnHelper.accessor('position', {
        header: 'Position Applied',
        cell: ({ row }) => (
          <Typography className='capitalize' color='text.primary'>
            {row.original.position || 'Not specified'}
          </Typography>
        )
      }),
      columnHelper.accessor('experience', {
        header: 'Experience',
        cell: ({ row }) => (
          <Typography color='text.primary'>
            {row.original.experience || 'Not specified'}
          </Typography>
        )
      }),
      columnHelper.accessor('appliedDate', {
        header: 'Applied Date',
        cell: ({ row }) => {
          const formatDateTime = (dateString) => {
            if (!dateString) return 'Not available'

            try {
              // Handle both ISO string and already formatted dates
              const date = new Date(dateString)
              if (isNaN(date.getTime())) return dateString // Return original if invalid date

              // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)
              const dateOptions = {
                month: '2-digit',
                day: '2-digit',
                year: 'numeric'
              }
              const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              }

              const formattedDate = date.toLocaleDateString('en-US', dateOptions)
              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

              return `${formattedDate} ${formattedTime}`
            } catch (error) {
              return dateString // Return original if formatting fails
            }
          }

          return (
            <div className='flex flex-col'>
              <Typography color='text.primary' style={{ fontSize: '0.95rem', lineHeight: '1.2' }}>
                {formatDateTime(row.original.appliedDate || row.original.createdAt)}
              </Typography>
            </div>
          )
        }
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: ({ row }) => {
          const status = row.original.status || 'pending'

          return (
            <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>
              <StatusDropdown
                currentStatus={status}
                onStatusChange={handleStatusChange}
                applicationId={row.original.id}
              />
            </div>
          )
        },
        enableSorting: false
      }),
      columnHelper.accessor('action', {
        header: 'Action',
        cell: ({ row }) => (
          <div className='flex items-center gap-1'>
            {/* Delete */}
            <IconButton
              onClick={() => handleDeleteApplication(row.original.id)}
              title="Delete Application"
              size='small'
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'error.main',
                  backgroundColor: 'error.light',
                  transform: 'scale(1.1)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className='tabler-trash' />
            </IconButton>

            {/* View Details */}
            <IconButton
              onClick={() => handleViewDetails(row.original)}
              title="View Details"
              size='small'
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'info.main',
                  backgroundColor: 'info.light',
                  transform: 'scale(1.1)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className='tabler-eye' />
            </IconButton>

            {/* Three Dots Menu */}
            <IconButton
              onClick={(e) => handleActionMenuOpen(e, row.original.id)}
              title="More Actions"
              size='small'
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  backgroundColor: 'primary.light',
                  transform: 'scale(1.1)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className='tabler-dots-vertical' />
            </IconButton>
          </div>
        ),
        enableSorting: false
      })
    ],
    [data, filteredData]
  )

  const table = useReactTable({
    data: filteredData,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter
    },
    state: {
      rowSelection,
      globalFilter
    },
    initialState: {
      pagination: {
        pageSize: 10
      }
    },
    enableRowSelection: true,
    globalFilterFn: fuzzyFilter,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  // Get selected rows data
  const getSelectedRowsData = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows
    return selectedRows.map(row => row.original)
  }

  // PDF Export function with detailed user information
  const exportSelectedToPDF = () => {
    const selectedData = getSelectedRowsData()

    console.log('=== PDF EXPORT DEBUG ===')
    console.log('Selected applications count:', selectedData.length)
    console.log('Selected data:', selectedData)
    console.log('Sample application data:', selectedData[0])

    if (selectedData.length === 0) {
      alert('Please select at least one application to export.')
      return
    }

    try {
      const doc = new jsPDF()
      let yPosition = 20

      // Add title
      doc.setFontSize(20)
      doc.setTextColor(40, 40, 40)
      doc.text('CAM Transport - Job Applications Export', 20, yPosition)
      yPosition += 15

      // Add export info
      doc.setFontSize(12)
      doc.setTextColor(100, 100, 100)
      doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 20, yPosition)
      yPosition += 8
      doc.text(`Selected Applications: ${selectedData.length}`, 20, yPosition)
      yPosition += 20

      // Process each selected application
      selectedData.forEach((app, index) => {
        // Check if we need a new page
        if (yPosition > 250) {
          doc.addPage()
          yPosition = 20
        }

        // Application header
        doc.setFontSize(16)
        doc.setTextColor(41, 128, 185)
        doc.setFont(undefined, 'bold')
        doc.text(`${index + 1}. ${app.fullName || 'Unknown Applicant'}`, 20, yPosition)
        yPosition += 12

        // Draw a line under the name
        doc.setDrawColor(41, 128, 185)
        doc.line(20, yPosition - 2, 190, yPosition - 2)
        yPosition += 8

        // Personal Information Section
        doc.setFontSize(12)
        doc.setTextColor(0, 0, 0)
        doc.setFont(undefined, 'bold')
        doc.text('Personal Information:', 20, yPosition)
        yPosition += 8

        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')

        const personalInfo = [
          `Full Name: ${app.fullName || 'Not provided'}`,
          `Email: ${app.email || 'Not provided'}`,
          `Phone: ${app.phone || app.phoneNumber || 'Not provided'}`,
          `Date of Birth: ${app.dob || 'Not provided'}`,
          `Address: ${app.address || 'Not provided'}`
        ]

        personalInfo.forEach(info => {
          doc.text(info, 25, yPosition)
          yPosition += 6
        })

        yPosition += 5

        // Job Information Section
        doc.setFontSize(12)
        doc.setFont(undefined, 'bold')
        doc.text('Job Information:', 20, yPosition)
        yPosition += 8

        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')

        const jobInfo = [
          `Position Applied: ${app.position || 'Not specified'}`,
          `Specific Driving Role: ${app.specificDrivingRole || 'Not specified'}`,
          `Specific Non-Driving Role: ${app.specificNonDrivingRole || 'Not specified'}`,
          `Employment Type: ${app.employmentType || app.employment_type || 'Not specified'}`,
          `Experience: ${app.experience || 'Not specified'}`,
          `Preferred Start Date: ${app.preferredStartDate || 'Not specified'}`,
          `Willing to Relocate: ${app.relocate || 'Not specified'}`,
          `Commercial License: ${app.commercialLicense || 'Not specified'}`,
          `Other Job Details: ${app.otherJob || 'Not provided'}`
        ]

        jobInfo.forEach(info => {
          doc.text(info, 25, yPosition)
          yPosition += 6
        })

        yPosition += 5

        // Application Details Section
        doc.setFontSize(12)
        doc.setFont(undefined, 'bold')
        doc.text('Application Details:', 20, yPosition)
        yPosition += 8

        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')

        // Format date with time for PDF (using actual submission time from database)
        const formatDateTimeForPDF = (dateString) => {
          if (!dateString) return 'Not available'
          try {
            // Parse the actual timestamp from database (createdAt field)
            const date = new Date(dateString)
            if (isNaN(date.getTime())) return dateString

            const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
            const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }

            const formattedDate = date.toLocaleDateString('en-US', dateOptions)
            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

            return `${formattedDate} ${formattedTime}`
          } catch (error) {
            return dateString
          }
        }

        const applicationDetails = [
          `Applied Date: ${formatDateTimeForPDF(app.appliedDate || app.createdAt)}`,
          `Current Status: ${statusConfig[app.status || 'pending']?.label || 'Pending'}`,
          `Work Reason: ${app.workReason || 'Not provided'}`,
          `Reference: ${app.reference || 'Not provided'}`,
          `Other Reference: ${app.otherReference || 'Not provided'}`,
          `Resume: ${app.resume ? 'Uploaded' : 'Not uploaded'}`
        ]

        applicationDetails.forEach(info => {
          doc.text(info, 25, yPosition)
          yPosition += 6
        })

        // Add separator line between applications
        yPosition += 10
        doc.setDrawColor(200, 200, 200)
        doc.line(20, yPosition, 190, yPosition)
        yPosition += 15
      })

      // Save the PDF
      const fileName = `CAM_Transport_Job_Applications_${new Date().toISOString().split('T')[0]}.pdf`
      doc.save(fileName)

      alert(`Successfully exported ${selectedData.length} application(s) with full details to ${fileName}`)

    } catch (error) {
      console.error('PDF Export Error:', error)
      alert('Error generating PDF. Please try again.')
    }
  }



  // Show loading state
  if (loading) {
    return (
      <Card>
        <CardHeader title='Job Applications' className='pbe-4' />
        <CardContent className='flex justify-center items-center py-8'>
          <div className='flex flex-col items-center gap-4'>
            <CircularProgress />
            <Typography variant='body2' color='text.secondary'>
              Loading job applications...
            </Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show error state
  if (error) {
    return (
      <Card>
        <CardHeader title='Job Applications' className='pbe-4' />
        <CardContent>
          <Alert severity='error' className='mb-4'>
            {error}
          </Alert>
          <div className='flex gap-2'>
            <Button
              variant='contained'
              onClick={loadJobApplications}
              startIcon={<i className='tabler-refresh' />}
            >
              Retry
            </Button>
            <Button
              variant='outlined'
              onClick={() => window.location.reload()}
              startIcon={<i className='tabler-reload' />}
            >
              Reload Page
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show empty state
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader title='Job Applications' className='pbe-4' />
        <CardContent className='flex justify-center items-center py-8'>
          <div className='flex flex-col items-center gap-4 text-center'>
            <i className='tabler-briefcase text-6xl text-textSecondary' />
            <Typography variant='h6' color='text.secondary'>
              No job applications found
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              Job applications submitted through the website will appear here.
            </Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader
          title={
            <div className='flex items-center gap-3'>
              <span>Job Applications</span>
              {data.length > 0 && (
                <Chip
                  label={`${data.length} application${data.length !== 1 ? 's' : ''}`}
                  color='primary'
                  variant='tonal'
                  size='small'
                />
              )}
              {Object.keys(rowSelection).length > 0 && (
                <Chip
                  label={`${Object.keys(rowSelection).length} selected`}
                  color='secondary'
                  variant='filled'
                  size='small'
                  icon={<i className='tabler-check' />}
                />
              )}
            </div>
          }
          className='pbe-4'
        />
        <TableFilters setData={setFilteredData} tableData={data} />
        <div className='flex justify-between flex-col items-start md:flex-row md:items-center p-6 border-bs gap-4'>
          <CustomTextField
            select
            value={table.getState().pagination.pageSize}
            onChange={e => table.setPageSize(Number(e.target.value))}
            className='max-sm:is-full sm:is-[70px]'
          >
            <MenuItem value='10'>10</MenuItem>
            <MenuItem value='25'>25</MenuItem>
            <MenuItem value='50'>50</MenuItem>
          </CustomTextField>
          <div className='flex flex-col sm:flex-row max-sm:is-full items-start sm:items-center gap-4'>
            <DebouncedInput
              value={globalFilter ?? ''}
              onChange={value => setGlobalFilter(String(value))}
              placeholder='Search Job Applications'
              className='max-sm:is-full'
            />

            {Object.keys(rowSelection).length > 0 ? (
              <Button
                color='warning'
                variant='outlined'
                size='small'
                startIcon={<i className='tabler-x' />}
                onClick={() => setRowSelection({})}
                className='max-sm:is-full'
              >
                Clear Selection
              </Button>
            ) : (
              <Button
                color='info'
                variant='outlined'
                size='small'
                startIcon={<i className='tabler-check-all' />}
                onClick={() => {
                  const allRowIds = {}
                  table.getRowModel().rows.forEach(row => {
                    allRowIds[row.original.id] = true
                  })
                  setRowSelection(allRowIds)
                }}
                className='max-sm:is-full'
              >
                Select All
              </Button>
            )}

            <Button
              color='secondary'
              variant='tonal'
              startIcon={<i className='tabler-file-type-pdf' />}
              onClick={exportSelectedToPDF}
              disabled={Object.keys(rowSelection).length === 0}
              className='max-sm:is-full'
              sx={{
                '&:disabled': {
                  opacity: 0.5,
                  cursor: 'not-allowed'
                }
              }}
            >
              Export PDF ({Object.keys(rowSelection).length})
            </Button>
            <IconButton
              color='primary'
              onClick={loadJobApplications}
              disabled={loading}
              title={loading ? 'Loading...' : 'Refresh Data'}
              sx={{
                border: '1px solid',
                borderColor: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  transform: 'scale(1.05)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />
            </IconButton>
          </div>
        </div>
        <div className='overflow-x-auto'>
          <table className={tableStyles.table}>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th key={header.id}>
                      {header.isPlaceholder ? null : (
                        <>
                          <div
                            className={classnames({
                              'flex items-center': header.column.getIsSorted(),
                              'cursor-pointer select-none': header.column.getCanSort()
                            })}
                            onClick={header.column.getToggleSortingHandler()}
                          >
                            {flexRender(header.column.columnDef.header, header.getContext())}
                            {{
                              asc: <i className='tabler-chevron-up text-xl' />,
                              desc: <i className='tabler-chevron-down text-xl' />
                            }[header.column.getIsSorted()] ?? null}
                          </div>
                        </>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            {table.getFilteredRowModel().rows.length === 0 ? (
              <tbody>
                <tr>
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>
                    No data available
                  </td>
                </tr>
              </tbody>
            ) : (
              <tbody>
                {table
                  .getRowModel()
                  .rows.slice(0, table.getState().pagination.pageSize)
                  .map(row => {
                    return (
                      <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>
                        {row.getVisibleCells().map(cell => (
                          <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
                        ))}
                      </tr>
                    )
                  })}
              </tbody>
            )}
          </table>
        </div>
        <TablePagination
          component={() => <TablePaginationComponent table={table} />}
          count={table.getFilteredRowModel().rows.length}
          rowsPerPage={table.getState().pagination.pageSize}
          page={table.getState().pagination.pageIndex}
          onPageChange={(_, page) => {
            table.setPageIndex(page)
          }}
        />
      </Card>

      <ApplicantDetailsModal
        open={detailsModalOpen}
        onClose={() => setDetailsModalOpen(false)}
        applicantData={selectedApplicant}
      />

      <ResumeViewerModal
        open={resumeModalOpen}
        onClose={() => setResumeModalOpen(false)}
        applicantData={selectedApplicant}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem
          onClick={() => {
            const selectedApp = data.find(app => app.id === selectedApplicationId)
            if (selectedApp) {
              handleDownloadResume(selectedApp)
            }
            handleActionMenuClose()
          }}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            '&:hover': {
              backgroundColor: 'primary.light',
              color: 'primary.main'
            }
          }}
        >
          <i className='tabler-download text-lg' />
          <Typography variant='body2'>Download Resume</Typography>
        </MenuItem>
      </Menu>
    </>
  )
}

export default ApplyJobTable
