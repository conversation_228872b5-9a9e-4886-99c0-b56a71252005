const mongoose = require('mongoose');
require('dotenv').config();

const quickCleanup = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/test');
        console.log('🔗 Connected to MongoDB');
        
        // Get the collection directly
        const db = mongoose.connection.db;
        const collection = db.collection('logins');
        
        // Delete all users with adminId "1"
        const result = await collection.deleteMany({ adminId: "1" });
        console.log(`🗑️ Deleted ${result.deletedCount} users with adminId "1"`);
        
        await mongoose.connection.close();
        console.log('✅ Cleanup completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
};

quickCleanup();
