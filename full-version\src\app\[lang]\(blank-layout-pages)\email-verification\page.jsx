'use client'

// React Imports
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'
import Alert from '@mui/material/Alert'
// Using Tabler icons instead of MUI icons to avoid dependency issues

// Component Imports
import Logo from '@components/layout/shared/Logo'

// Auth Imports
import { setCurrentUser } from '@/middleware/authCheck'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'

const EmailVerificationPage = () => {
  // States
  const [verificationStatus, setVerificationStatus] = useState('loading') // loading, success, error
  const [errorMessage, setErrorMessage] = useState('')
  const [userEmail, setUserEmail] = useState('')

  // Hooks
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Vars
  const darkImg = '/images/pages/auth-v2-mask-dark.png'
  const lightImg = '/images/pages/auth-v2-mask-light.png'
  const authBackground = useImageVariant(lightImg, darkImg)

  useEffect(() => {
    const userId = searchParams.get('userId')
    const otp = searchParams.get('otp')

    if (!userId || !otp) {
      setVerificationStatus('error')
      setErrorMessage('Invalid verification link. Please try logging in again.')
      return
    }

    // Verify the email with backend
    const verifyEmail = async () => {
      try {
        console.log('🔄 Verifying email with backend...')
        console.log('User ID:', userId)
        console.log('OTP:', otp)

        const response = await fetch(`http://localhost:8090/login/verify-otp/${userId}/${otp}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })

        console.log('Response status:', response.status)

        if (response.ok) {
          // Check if response is JSON or redirect
          const contentType = response.headers.get('content-type')
          
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json()
            console.log('✅ Verification response:', data)

            // Save user data to localStorage for authentication
            if (data.user) {
              setCurrentUser(data.user)
              console.log('✅ User data saved to localStorage')
            }

            setVerificationStatus('success')
            setUserEmail(data.user?.email || 'your email')
          } else {
            // Backend is redirecting, which means verification was successful
            console.log('✅ Verification successful (redirect response)')
            setVerificationStatus('success')
          }
        } else {
          const errorData = await response.json()
          console.error('❌ Verification failed:', errorData)
          setVerificationStatus('error')
          setErrorMessage(errorData.message || 'Email verification failed. Please try again.')
        }
      } catch (error) {
        console.error('❌ Error verifying email:', error)
        setVerificationStatus('error')
        setErrorMessage('An error occurred during verification. Please try again.')
      }
    }

    verifyEmail()
  }, [searchParams])

  const handleGoToProfile = () => {
    router.push('/en/pages/user-profile')
  }

  const handleBackToLogin = () => {
    router.push('/en/login')
  }

  return (
    <div className='flex bs-full justify-center'>
      <div
        className={`flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden`}
        style={{
          backgroundImage: `url(${authBackground})`,
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundSize: 'cover'
        }}
      >
        <div className='absolute inset-0 bg-black/20' />
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <div className='absolute block-start-5 sm:block-start-[33px] inline-start-6 sm:inline-start-[38px]'>
          <Logo />
        </div>
        <div className='flex flex-col gap-6 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset] mx-auto'>
          <div className='flex flex-col gap-1'>
            <Typography variant='h4' className='font-medium'>
              Email Verification
            </Typography>
            <Typography>Verifying your email address...</Typography>
          </div>

          <Card className='flex flex-col gap-6'>
            <CardContent className='!p-8'>
              {verificationStatus === 'loading' && (
                <Box className='flex flex-col items-center gap-4'>
                  <CircularProgress size={60} />
                  <Typography variant='h6'>Verifying your email...</Typography>
                  <Typography color='text.secondary' className='text-center'>
                    Please wait while we verify your email address.
                  </Typography>
                </Box>
              )}

              {verificationStatus === 'success' && (
                <Box className='flex flex-col items-center gap-4'>
                  <div className='flex items-center justify-center w-20 h-20 rounded-full bg-green-100'>
                    <i className='tabler-check text-5xl text-green-600' />
                  </div>
                  <Typography variant='h5' className='font-medium text-center'>
                    Email Verified Successfully!
                  </Typography>
                  <Typography color='text.secondary' className='text-center'>
                    Your email has been verified. You can now access your profile and all features.
                  </Typography>
                  {userEmail && (
                    <Alert severity='success' className='w-full'>
                      Welcome! Your account ({userEmail}) is now verified and ready to use.
                    </Alert>
                  )}
                  <Button
                    fullWidth
                    variant='contained'
                    size='large'
                    onClick={handleGoToProfile}
                    className='mt-4'
                  >
                    Go to Profile
                  </Button>
                </Box>
              )}

              {verificationStatus === 'error' && (
                <Box className='flex flex-col items-center gap-4'>
                  <div className='flex items-center justify-center w-20 h-20 rounded-full bg-red-100'>
                    <i className='tabler-x text-5xl text-red-600' />
                  </div>
                  <Typography variant='h5' className='font-medium text-center'>
                    Verification Failed
                  </Typography>
                  <Alert severity='error' className='w-full'>
                    {errorMessage}
                  </Alert>
                  <div className='flex flex-col gap-2 w-full'>
                    <Button
                      fullWidth
                      variant='contained'
                      onClick={handleBackToLogin}
                    >
                      Back to Login
                    </Button>
                    <Button
                      fullWidth
                      variant='outlined'
                      onClick={() => window.location.reload()}
                    >
                      Try Again
                    </Button>
                  </div>
                </Box>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default EmailVerificationPage
