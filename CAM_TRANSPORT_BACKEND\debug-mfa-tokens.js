const speakeasy = require('speakeasy');

// Test the fixed secret that should be used for admin user
const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';

console.log('🔍 Debugging MFA Token Generation');
console.log('=================================');
console.log(`Master Secret: ${ADMIN_SECRET}`);
console.log('');

// Generate current TOTP token with different window settings
console.log('📱 Current Token Generation:');
console.log('============================');

const currentTime = Math.floor(Date.now() / 1000);
const timeWindow = Math.floor(currentTime / 30);

console.log(`Current Unix Time: ${currentTime}`);
console.log(`Current Time Window: ${timeWindow}`);
console.log(`Current Date/Time: ${new Date().toLocaleString()}`);
console.log('');

// Generate tokens for current and adjacent time windows
for (let i = -2; i <= 2; i++) {
    const windowTime = (timeWindow + i) * 30;
    const token = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        time: windowTime
    });
    
    const windowDate = new Date(windowTime * 1000);
    const status = i === 0 ? ' ← CURRENT' : '';
    console.log(`Window ${i}: ${token} (${windowDate.toLocaleTimeString()})${status}`);
}

console.log('');

// Test verification with different window settings
console.log('🔐 Token Verification Tests:');
console.log('============================');

const currentToken = speakeasy.totp({
    secret: ADMIN_SECRET,
    encoding: 'base32'
});

console.log(`Current Token: ${currentToken}`);
console.log('');

// Test with different window tolerances
for (let window = 0; window <= 3; window++) {
    const verified = speakeasy.totp.verify({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        token: currentToken,
        window: window
    });
    
    console.log(`Window ${window}: ${verified ? '✅ PASS' : '❌ FAIL'}`);
}

console.log('');

// Test what happens with slightly different times
console.log('⏰ Time Drift Simulation:');
console.log('=========================');

// Simulate tokens from devices with slight time differences
for (let timeDrift = -60; timeDrift <= 60; timeDrift += 15) {
    const driftTime = currentTime + timeDrift;
    const driftToken = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        time: driftTime
    });
    
    const verified = speakeasy.totp.verify({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        token: driftToken,
        window: 1
    });
    
    const status = verified ? '✅' : '❌';
    console.log(`${timeDrift}s drift: ${driftToken} ${status}`);
}

console.log('');

// Manual token input test
console.log('🧪 Manual Token Test:');
console.log('=====================');
console.log('Enter a token from your Microsoft Authenticator and we\'ll test it:');
console.log('');

// Function to test a manual token
function testManualToken(inputToken) {
    console.log(`Testing token: ${inputToken}`);
    
    const verified = speakeasy.totp.verify({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        token: inputToken,
        window: 2 // More generous window for testing
    });
    
    console.log(`Verification result: ${verified ? '✅ VALID' : '❌ INVALID'}`);
    
    if (!verified) {
        // Show what the expected tokens are
        console.log('Expected tokens:');
        const currentExpected = speakeasy.totp({
            secret: ADMIN_SECRET,
            encoding: 'base32'
        });
        console.log(`  Current: ${currentExpected}`);
        
        // Show previous and next tokens
        const prevToken = speakeasy.totp({
            secret: ADMIN_SECRET,
            encoding: 'base32',
            time: Math.floor(Date.now() / 1000) - 30
        });
        const nextToken = speakeasy.totp({
            secret: ADMIN_SECRET,
            encoding: 'base32',
            time: Math.floor(Date.now() / 1000) + 30
        });
        
        console.log(`  Previous: ${prevToken}`);
        console.log(`  Next: ${nextToken}`);
    }
}

// If running interactively, you can call testManualToken with your Microsoft Authenticator token
// Example: testManualToken('123456');

console.log('');
console.log('🔧 Troubleshooting Tips:');
console.log('========================');
console.log('1. Make sure both apps are using the EXACT same secret: JBSWY3DPEHPK3PXP');
console.log('2. Check that device clocks are synchronized (within 30 seconds)');
console.log('3. Verify the account name and issuer are set correctly');
console.log('4. Try tokens from both apps at the same time to compare');
console.log('');
console.log('📱 QR Code URL for testing:');
const qrUrl = speakeasy.otpauthURL({
    secret: ADMIN_SECRET,
    label: 'CAM Transport (<EMAIL>)',
    issuer: 'CAM Transport',
    encoding: 'base32'
});
console.log(qrUrl);

// Export the test function for manual use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testManualToken };
}
