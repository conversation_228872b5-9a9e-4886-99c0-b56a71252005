// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Vars
const data = [
  {
    description: 'Create & validate your referral link and get',
    value: '$50',
    icon: (
      <svg xmlns='http://www.w3.org/2000/svg' width='42' height='42' viewBox='0 0 43 42' fill='none'>
        <path
          opacity='0.2'
          fillRule='evenodd'
          clipRule='evenodd'
          d='M35.5943 24.3473L30.4428 18.1621C30.6396 21.952 29.7045 26.3652 26.817 31.4019L31.7389 35.3394C31.9139 35.4784 32.1215 35.5704 32.342 35.6067C32.5625 35.643 32.7887 35.6224 32.999 35.5468C33.2093 35.4712 33.3968 35.3432 33.5438 35.1748C33.6908 35.0065 33.7923 34.8034 33.8389 34.5848L35.8568 25.4629C35.9055 25.2695 35.907 25.0672 35.8614 24.8731C35.8157 24.679 35.7241 24.4987 35.5943 24.3473ZM7.63806 24.4457L12.7896 18.277C12.5927 22.0668 13.5279 26.4801 16.4154 31.5004L11.4935 35.4379C11.3196 35.5769 11.1132 35.6693 10.8937 35.7065C10.6743 35.7437 10.4489 35.7245 10.2389 35.6507C10.0289 35.5769 9.84115 35.4509 9.69326 35.2845C9.54537 35.1181 9.44223 34.9168 9.39353 34.6996L7.37556 25.5613C7.32691 25.3679 7.32536 25.1657 7.37104 24.9716C7.41671 24.7775 7.50828 24.5971 7.63806 24.4457Z'
          fill='currentColor'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M20.2132 2.47353C20.6217 2.13596 21.1351 1.95117 21.6653 1.95117C22.1971 1.95117 22.7121 2.13707 23.1212 2.47657C24.7301 3.7867 28.2128 7.0357 30.1373 12.0381C30.798 13.7554 31.2701 15.6673 31.4253 17.7625L36.4572 23.8008C36.6883 24.0724 36.8515 24.3951 36.9332 24.7424C37.0142 25.0868 37.0128 25.4453 36.9291 25.7889L34.9138 34.9151L34.9131 34.9182C34.8273 35.3009 34.6455 35.6555 34.385 35.9487C34.1244 36.2418 33.7936 36.4639 33.4236 36.5939C33.0535 36.724 32.6565 36.7579 32.2698 36.6923C31.8831 36.6267 31.5195 36.4638 31.2131 36.219L31.2126 36.2186L26.5646 32.5002H16.7662L12.1182 36.2186L12.1177 36.219C11.8113 36.4638 11.4477 36.6267 11.061 36.6923C10.6743 36.7579 10.2773 36.724 9.90727 36.5939C9.53726 36.4639 9.20641 36.2418 8.94584 35.9487C8.68527 35.6555 8.50355 35.3009 8.41775 34.9182L8.41706 34.9151L6.40177 25.7889C6.31804 25.4453 6.31658 25.0868 6.39762 24.7424C6.47936 24.395 6.64268 24.072 6.87401 23.8004L11.8088 17.8912C11.9513 15.7424 12.4328 13.7842 13.1145 12.029C15.058 7.02537 18.5854 3.77698 20.2132 2.47353ZM29.4558 18.3076C29.4446 18.2387 29.4406 18.169 29.4438 18.0996C29.3181 16.1202 28.879 14.3374 28.2707 12.7563C26.5219 8.21065 23.3318 5.22661 21.8544 4.02428L21.8446 4.01626L21.8446 4.01619C21.7943 3.97418 21.7309 3.95117 21.6653 3.95117C21.5998 3.95117 21.5363 3.97418 21.486 4.01619L21.4697 4.02954C19.9771 5.22365 16.7444 8.20744 14.9788 12.7531C14.3468 14.3804 13.8969 16.2219 13.7896 18.2718C13.7898 18.3084 13.788 18.345 13.7842 18.3815C13.6245 21.8411 14.4416 25.8898 16.9985 30.5002H26.3263C28.8486 25.8569 29.6366 21.7836 29.4558 18.3076ZM34.9245 25.0857L31.4177 20.8775C31.1755 24.0045 30.2142 27.4702 28.197 31.2448L32.4615 34.6565C32.5029 34.6896 32.5521 34.7116 32.6043 34.7204C32.6566 34.7293 32.7102 34.7247 32.7602 34.7071C32.8102 34.6896 32.8549 34.6596 32.8901 34.6199C32.9251 34.5806 32.9496 34.533 32.9613 34.4817L32.9615 34.4807L34.9788 25.3455C34.9809 25.3361 34.9831 25.3266 34.9855 25.3172C34.9951 25.2789 34.9954 25.2389 34.9864 25.2004C34.9773 25.162 34.9592 25.1263 34.9335 25.0963L34.9245 25.0858L34.9245 25.0857ZM11.8405 20.9734L8.40561 25.0865L8.39739 25.0964L8.39732 25.0963C8.37163 25.1263 8.3535 25.162 8.34445 25.2004C8.33541 25.2389 8.33572 25.2789 8.34535 25.3172C8.34772 25.3266 8.34995 25.3361 8.35204 25.3455L10.3693 34.4807L10.3695 34.4817C10.3812 34.533 10.4057 34.5806 10.4407 34.6199C10.4759 34.6596 10.5206 34.6896 10.5706 34.7071C10.6206 34.7247 10.6743 34.7293 10.7265 34.7204C10.7788 34.7116 10.8279 34.6896 10.8693 34.6565L15.1281 31.2495C13.0909 27.5131 12.1056 24.0779 11.8405 20.9734ZM18.0404 36.7502C18.0404 36.1979 18.4881 35.7502 19.0404 35.7502H24.2904C24.8427 35.7502 25.2904 36.1979 25.2904 36.7502C25.2904 37.3025 24.8427 37.7502 24.2904 37.7502H19.0404C18.4881 37.7502 18.0404 37.3025 18.0404 36.7502ZM23.6342 15.7502C23.6342 16.8375 22.7527 17.719 21.6654 17.719C20.5781 17.719 19.6967 16.8375 19.6967 15.7502C19.6967 14.6629 20.5781 13.7815 21.6654 13.7815C22.7527 13.7815 23.6342 14.6629 23.6342 15.7502Z'
          fill='currentColor'
        />
      </svg>
    )
  },
  {
    description: 'For every new signup you get',
    value: '10%',
    icon: (
      <svg xmlns='http://www.w3.org/2000/svg' width='42' height='42' viewBox='0 0 42 42' fill='none'>
        <path
          opacity='0.2'
          d='M9.1875 6.25H32.8125C32.8954 6.25 32.9749 6.28292 33.0335 6.34153L33.739 5.63603L33.0335 6.34153C33.0921 6.40013 33.125 6.47962 33.125 6.5625V35.4375C33.125 35.5204 33.0921 35.5999 33.0335 35.6585L33.7406 36.3656L33.0335 35.6585C32.9749 35.7171 32.8954 35.75 32.8125 35.75H9.1875C9.10462 35.75 9.02513 35.7171 8.96653 35.6585L8.25942 36.3656L8.96653 35.6585C8.90792 35.5999 8.875 35.5204 8.875 35.4375V6.5625C8.875 6.47962 8.90792 6.40014 8.96653 6.34153C9.02514 6.28292 9.10462 6.25 9.1875 6.25ZM17.5277 27.5092C18.5555 28.1959 19.7639 28.5625 21 28.5625C22.6576 28.5625 24.2473 27.904 25.4194 26.7319C26.5915 25.5598 27.25 23.9701 27.25 22.3125C27.25 21.0764 26.8834 19.868 26.1967 18.8402C25.5099 17.8124 24.5338 17.0113 23.3918 16.5383C22.2497 16.0652 20.9931 15.9414 19.7807 16.1826C18.5683 16.4237 17.4547 17.019 16.5806 17.8931C15.7065 18.7672 15.1112 19.8808 14.8701 21.0932C14.6289 22.3056 14.7527 23.5622 15.2258 24.7043C15.6988 25.8463 16.4999 26.8224 17.5277 27.5092Z'
          fill='currentColor'
          stroke='currentColor'
          strokeWidth='2'
        />
        <path
          d='M21 27.5625C23.8995 27.5625 26.25 25.212 26.25 22.3125C26.25 19.413 23.8995 17.0625 21 17.0625C18.1005 17.0625 15.75 19.413 15.75 22.3125C15.75 25.212 18.1005 27.5625 21 27.5625ZM21 27.5625C19.4718 27.5625 17.9646 27.9183 16.5977 28.6017C15.2309 29.2852 14.0419 30.2774 13.125 31.5M21 27.5625C22.5282 27.5625 24.0354 27.9183 25.4023 28.6017C26.7691 29.2852 27.9581 30.2774 28.875 31.5M15.75 10.5H26.25M34.125 6.5625V35.4375C34.125 36.1624 33.5374 36.75 32.8125 36.75H9.1875C8.46263 36.75 7.875 36.1624 7.875 35.4375V6.5625C7.875 5.83763 8.46263 5.25 9.1875 5.25H32.8125C33.5374 5.25 34.125 5.83763 34.125 6.5625Z'
          stroke='currentColor'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </svg>
    )
  },
  {
    description: 'Get other friends to generate link and get',
    value: '$100',
    icon: (
      <svg xmlns='http://www.w3.org/2000/svg' width='42' height='42' viewBox='0 0 43 42' fill='none'>
        <path
          opacity='0.2'
          d='M34.8347 5.89001L4.25347 14.5033C3.99315 14.5745 3.76109 14.7242 3.58892 14.932C3.41674 15.1398 3.31281 15.3956 3.29129 15.6647C3.26977 15.9337 3.3317 16.2028 3.46865 16.4353C3.60559 16.6679 3.8109 16.8526 4.0566 16.9642L18.1003 23.6088C18.3754 23.7362 18.5964 23.9571 18.7238 24.2322L25.3683 38.2759C25.48 38.5216 25.6647 38.7269 25.8972 38.8639C26.1298 39.0008 26.3989 39.0628 26.6679 39.0412C26.9369 39.0197 27.1927 38.9158 27.4006 38.7436C27.6084 38.5714 27.7581 38.3394 27.8293 38.0791L36.4425 7.49782C36.5078 7.27466 36.5118 7.03804 36.4542 6.81279C36.3966 6.58753 36.2794 6.38192 36.115 6.21751C35.9506 6.0531 35.745 5.93594 35.5198 5.87832C35.2945 5.8207 35.0579 5.82474 34.8347 5.89001Z'
          fill='currentColor'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M35.7676 4.90951C35.3704 4.80791 34.9532 4.81452 34.5595 4.92862L3.98975 13.5387L3.98553 13.5398C3.52858 13.6657 3.12129 13.929 2.81886 14.294C2.5155 14.6602 2.33239 15.1109 2.29448 15.5849C2.25656 16.0589 2.36567 16.533 2.60696 16.9428C2.84676 17.35 3.20553 17.6739 3.63489 17.871L17.6727 24.5127L17.6727 24.5127L17.6801 24.5162C17.7402 24.544 17.7885 24.5923 17.8164 24.6524L17.8163 24.6524L17.8199 24.6599L24.4616 38.6978C24.6587 39.1271 24.9826 39.4858 25.3898 39.7256C25.7995 39.9669 26.2736 40.076 26.7476 40.0381C27.2216 40.0001 27.6724 39.817 28.0385 39.5137C28.4036 39.2113 28.6668 38.804 28.7927 38.347L28.7939 38.3428L37.4023 7.77853L37.4039 7.77315C37.518 7.37938 37.5246 6.96221 37.423 6.56497C37.3209 6.16591 37.1134 5.80166 36.8221 5.5104C36.5309 5.21914 36.1666 5.01159 35.7676 4.90951ZM35.1058 6.85256L34.8347 5.89001L35.1154 6.8498C35.1664 6.83489 35.2205 6.83396 35.2719 6.84713C35.3234 6.86029 35.3704 6.88705 35.4079 6.92461C35.4455 6.96218 35.4723 7.00915 35.4854 7.06061C35.4986 7.11207 35.4977 7.16612 35.4827 7.2171L35.4827 7.21709L35.48 7.22671L26.8667 37.808L26.8667 37.808L26.8647 37.8153C26.8477 37.8773 26.8121 37.9326 26.7626 37.9736C26.7131 38.0146 26.6522 38.0393 26.5881 38.0444C26.5241 38.0496 26.46 38.0348 26.4046 38.0022C26.3493 37.9696 26.3053 37.9207 26.2787 37.8622L26.2722 37.8483L19.7287 24.0181L26.6496 17.0971C27.0402 16.7066 27.0402 16.0734 26.6496 15.6829C26.2591 15.2924 25.626 15.2924 25.2354 15.6829L18.3145 22.6038L4.48428 16.0603L4.47032 16.0538C4.41182 16.0272 4.36294 15.9833 4.33033 15.9279C4.29773 15.8725 4.28298 15.8085 4.28811 15.7444C4.29323 15.6803 4.31798 15.6194 4.35897 15.57C4.39996 15.5205 4.45521 15.4848 4.5172 15.4679L4.5172 15.4679L4.52458 15.4658L35.1058 6.85256Z'
          fill='currentColor'
        />
      </svg>
    )
  }
]

const IconStepsCard = () => {
  return (
    <Card>
      <CardHeader title='How to use' subheader='Integrate your referral code in 3 easy steps.' className='pbe-6' />
      <CardContent className='flex flex-col sm:flex-row items-center justify-around gap-6'>
        {data.map((item, index) => (
          <div key={index} className='flex flex-col items-center gap-2 max-is-[185px]'>
            <div className='flex border border-dashed border-primary rounded-full p-3.5 text-primary'>{item.icon}</div>
            <Typography className='text-wrap text-center'>{item.description}</Typography>
            <Typography variant='h6' color='primary.main'>
              {item.value}
            </Typography>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export default IconStepsCard
