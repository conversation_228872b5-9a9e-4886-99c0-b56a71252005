const { Router } = require('express');
const {
    generateMFASetup,
    verifyMFASetup,
    verifyMFAToken,
    disableMFA,
    getMFAStatus,
    generateBackupCodes,
    getMFADevices,
    updateDeviceName,
    removeDevice
} = require('../controller/MFA');

const MFARoute = Router();

// Generate MFA setup (secret and QR code)
MFARoute.post('/setup/:userId', generateMFASetup);

// Verify MFA setup and enable MFA
MFARoute.post('/setup/verify/:userId', verifyMFASetup);

// Verify MFA token during login
MFARoute.post('/verify/:userId', verifyMFAToken);

// Get MFA status
MFARoute.get('/status/:userId', getMFAStatus);

// Disable MFA
MFARoute.post('/disable/:userId', disableMFA);

// Generate new backup codes
MFARoute.post('/backup-codes/:userId', generateBackupCodes);

// Device management routes
// Get all devices for a user
MFARoute.get('/devices/:userId', getMFADevices);

// Update device name
MFARoute.put('/devices/:userId/:deviceId/name', updateDeviceName);

// Remove/deactivate a device
MFARoute.delete('/devices/:userId/:deviceId', removeDevice);

// Cleanup endpoint for testing (removes all MFA data for a user)
MFARoute.delete('/cleanup/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const Login = require('../model/Login');

        // Delete the user completely to start fresh
        const result = await Login.deleteOne({ adminId: userId.toString() });

        res.json({
            success: true,
            message: `Cleaned up user ${userId}`,
            deletedCount: result.deletedCount
        });
    } catch (error) {
        console.error('Cleanup error:', error);
        res.status(500).json({
            success: false,
            message: 'Cleanup failed'
        });
    }
});

/**
 * @swagger
 * components:
 *   schemas:
 *     MFASetupResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             secret:
 *               type: string
 *             qrCode:
 *               type: string
 *             manualEntryKey:
 *               type: string
 *             issuer:
 *               type: string
 *             accountName:
 *               type: string
 */

/**
 * @swagger
 * /mfa/setup/{userId}:
 *   get:
 *     summary: Generate MFA setup
 *     description: Generate MFA secret and QR code for user to set up authenticator app
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     responses:
 *       200:
 *         description: MFA setup generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MFASetupResponse'
 *       400:
 *         description: MFA already enabled
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/setup/verify/{userId}:
 *   post:
 *     summary: Verify MFA setup
 *     description: Verify TOTP token and enable MFA for the user
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: 6-digit TOTP token from authenticator app
 *     responses:
 *       200:
 *         description: MFA enabled successfully
 *       400:
 *         description: Invalid token or setup not initiated
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/verify/{userId}:
 *   post:
 *     summary: Verify MFA token
 *     description: Verify TOTP token or backup code during login
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: 6-digit TOTP token or backup code
 *               isBackupCode:
 *                 type: boolean
 *                 description: Whether the token is a backup code
 *     responses:
 *       200:
 *         description: MFA verification successful
 *       400:
 *         description: Invalid token or MFA not enabled
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/status/{userId}:
 *   get:
 *     summary: Get MFA status
 *     description: Get MFA status and information for a user
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     responses:
 *       200:
 *         description: MFA status retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/disable/{userId}:
 *   post:
 *     summary: Disable MFA
 *     description: Disable MFA for a user (requires password confirmation)
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - password
 *             properties:
 *               password:
 *                 type: string
 *                 description: User's current password
 *     responses:
 *       200:
 *         description: MFA disabled successfully
 *       400:
 *         description: Invalid password
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/backup-codes/{userId}:
 *   post:
 *     summary: Generate backup codes
 *     description: Generate new backup codes for MFA
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     responses:
 *       200:
 *         description: Backup codes generated successfully
 *       400:
 *         description: MFA not enabled
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/devices/{userId}:
 *   get:
 *     summary: Get MFA devices
 *     description: Get all active MFA devices for a user
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     responses:
 *       200:
 *         description: Devices retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/devices/{userId}/{deviceId}/name:
 *   put:
 *     summary: Update device name
 *     description: Update the name of an MFA device
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The device ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - deviceName
 *             properties:
 *               deviceName:
 *                 type: string
 *                 description: New device name
 *     responses:
 *       200:
 *         description: Device name updated successfully
 *       400:
 *         description: Invalid device name
 *       404:
 *         description: User or device not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mfa/devices/{userId}/{deviceId}:
 *   delete:
 *     summary: Remove MFA device
 *     description: Remove/deactivate an MFA device
 *     tags:
 *       - MFA
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The device ID
 *     responses:
 *       200:
 *         description: Device removed successfully
 *       400:
 *         description: Cannot remove last device
 *       404:
 *         description: User or device not found
 *       500:
 *         description: Internal server error
 */

module.exports = MFARoute;
