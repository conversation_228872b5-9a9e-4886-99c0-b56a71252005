'use client'

// React Imports
import { useState, useEffect, useMemo } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Chip from '@mui/material/Chip'
import Box from '@mui/material/Box'
import Alert from '@mui/material/Alert'

// Third-party Imports
import classnames from 'classnames'
import { rankItem } from '@tanstack/match-sorter-utils'
import {
    createColumnHelper,
    flexRender,
    getCoreRowModel,
    useReactTable,
    getFilteredRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
    getFacetedMinMaxValues,
    getPaginationRowModel,
    getSortedRowModel
} from '@tanstack/react-table'

// Component Imports
import QuoteDetailsModal from './QuoteDetailsModal'
import StatusDropdown from './StatusDropdown'
import CustomTextField from '@core/components/mui/TextField'
import CustomAvatar from '@core/components/mui/Avatar'
import TablePaginationComponent from '@components/TablePaginationComponent'

// API Imports
import { fetchQuotes, deleteQuote, updateQuoteStatus } from '@/services/quoteApi'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

// Fuzzy Filter function
const fuzzyFilter = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value)
    addMeta({
        itemRank
    })
    return itemRank.passed
}

// DebouncedInput component
const DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />
}

// Column Definitions
const columnHelper = createColumnHelper()

const QuotesTable = () => {
    // States
    const [data, setData] = useState([])
    const [filteredData, setFilteredData] = useState([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)
    const [selectedQuote, setSelectedQuote] = useState(null)
    const [showModal, setShowModal] = useState(false)

    // Load quotes data
    useEffect(() => {
        const loadQuotes = async () => {
            try {
                setLoading(true)
                setError(null)
                const response = await fetchQuotes()
                if (response.success) {
                    // Load saved statuses from localStorage
                    const savedStatuses = JSON.parse(localStorage.getItem('quoteStatuses') || '{}')

                    // Add default status to quotes that don't have one, prioritizing localStorage
                    const quotesWithStatus = response.quotes.map(quote => ({
                        ...quote,
                        status: savedStatuses[quote._id] || quote.status || 'pending'
                    }))
                    setData(quotesWithStatus)
                    setFilteredData(quotesWithStatus)
                } else {
                    setError('Failed to load quotes')
                }
            } catch (err) {
                console.error('Error loading quotes:', err)
                setError(err.message || 'Failed to load quotes. Please try again.')
                setData([])
                setFilteredData([])
            } finally {
                setLoading(false)
            }
        }

        loadQuotes()
    }, [])

    // Handlers
    const handleViewDetails = (quote) => {
        setSelectedQuote(quote)
        setShowModal(true)
    }

    const handleCloseModal = () => {
        setShowModal(false)
        setSelectedQuote(null)
    }

    const handleDeleteQuote = async (quoteId) => {
        const quote = data.find(q => q._id === quoteId)
        const contactName = quote?.contactInfo?.fullName || 'Unknown'

        if (window.confirm(`Are you sure you want to delete the quote for ${contactName}? This action cannot be undone.`)) {
            try {
                await deleteQuote(quoteId)
                const updatedQuotes = data.filter(quote => quote._id !== quoteId)
                setData(updatedQuotes)
                setFilteredData(updatedQuotes)
                alert('Quote deleted successfully!')
            } catch (error) {
                console.error('Error deleting quote:', error)
                alert(`Failed to delete quote: ${error.message}`)
            }
        }
    }

    // Handle status change
    const handleStatusChange = async (quoteId, newStatus) => {
        console.log('Updating status for quote:', quoteId, 'to:', newStatus)

        try {
            // Try to call API to update status in backend
            await updateQuoteStatus(quoteId, newStatus)
            console.log('Quote status updated in backend successfully')
        } catch (error) {
            console.error('Backend API error, using localStorage fallback:', error)

            // Fallback: Save status to localStorage for persistence (like other sections)
            const savedStatuses = JSON.parse(localStorage.getItem('quoteStatuses') || '{}')
            savedStatuses[quoteId] = newStatus
            localStorage.setItem('quoteStatuses', JSON.stringify(savedStatuses))
            console.log('Quote status saved to localStorage as fallback')
        }

        // Update local state regardless of backend success/failure
        const updateQuoteStatus = (quotes) =>
            quotes.map(quote =>
                quote._id === quoteId
                    ? { ...quote, status: newStatus }
                    : quote
            )

        setData(updateQuoteStatus)
        setFilteredData(updateQuoteStatus)

        console.log('Quote status updated in frontend successfully')
    }

    // Action button styles
    const actionButtonStyles = {
        color: 'text.secondary',
        '&:hover': {
            backgroundColor: 'action.hover',
            transform: 'scale(1.1)'
        },
        transition: 'all 0.2s ease-in-out',
        minWidth: '32px',
        width: '32px',
        height: '32px',
        padding: '4px'
    }

    // Column definitions
    const columns = useMemo(
        () => [
            columnHelper.accessor('contactInfo.fullName', {
                header: 'Contact Person',
                cell: ({ row }) => (
                    <div className='flex items-center gap-4'>
                        <CustomAvatar
                            variant='rounded'
                            color='primary'
                            skin='light'
                            size={34}
                        >
                            {row.original.contactInfo?.fullName?.charAt(0)?.toUpperCase() || 'Q'}
                        </CustomAvatar>
                        <div className='flex flex-col'>
                            <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                                {row.original.contactInfo?.fullName || 'Unknown'}
                            </Typography>
                            <Typography variant='body1' color='text.primary' className='font-medium' style={{ fontSize: '1rem', letterSpacing: '1px' }}>
                                {row.original.contactInfo?.emailAddress || 'No email'}
                            </Typography>
                        </div>
                    </div>
                )
            }),
            columnHelper.accessor('contactInfo.phoneNumber', {
                header: 'Contact',
                cell: ({ row }) => (
                    <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                        {row.original.contactInfo?.phoneNumber || 'Not provided'}
                    </Typography>
                )
            }),
            columnHelper.accessor('shipmentDetails.loadType', {
                header: 'Freight Type',
                cell: ({ row }) => (
                    <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                        {row.original.shipmentDetails?.loadType || 'Not specified'}
                    </Typography>
                )
            }),
            columnHelper.accessor('pickupDelivery', {
                header: 'Pickup Location',
                cell: ({ row }) => {
                    const pickup = row.original.pickupDelivery?.pickupLocation
                    return (
                        <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                            {pickup ? `${pickup.city}, ${pickup.stateOrProvince}` : 'Not specified'}
                        </Typography>
                    )
                }
            }),
            columnHelper.accessor('deliveryLocation', {
                header: 'Delivery Location',
                cell: ({ row }) => {
                    const delivery = row.original.pickupDelivery?.deliveryLocation
                    return (
                        <Typography color='text.primary' className='font-medium' style={{ fontSize: '1.1rem' }}>
                            {delivery ? `${delivery.city}, ${delivery.stateOrProvince}` : 'Not specified'}
                        </Typography>
                    )
                }
            }),
            columnHelper.accessor('createdAt', {
                header: 'Date Created',
                cell: ({ row }) => {
                    const formatDateTime = (dateString) => {
                        if (!dateString) return 'Not available'

                        try {
                            // Handle both ISO string and already formatted dates
                            const date = new Date(dateString)
                            if (isNaN(date.getTime())) return dateString // Return original if invalid date

                            // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)
                            const dateOptions = {
                                month: '2-digit',
                                day: '2-digit',
                                year: 'numeric'
                            }
                            const timeOptions = {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true
                            }

                            const formattedDate = date.toLocaleDateString('en-US', dateOptions)
                            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

                            return `${formattedDate} ${formattedTime}`
                        } catch (error) {
                            return dateString // Return original if formatting fails
                        }
                    }

                    const date = row.original.createdAt || row.original.pickupDelivery?.preferredPickupDate
                    return (
                        <div className='flex flex-col'>
                            <Typography color='text.primary' style={{ fontSize: '0.95rem', lineHeight: '1.2' }}>
                                {formatDateTime(date)}
                            </Typography>
                        </div>
                    )
                }
            }),
            columnHelper.accessor('status', {
                header: 'Status',
                cell: ({ row }) => {
                    const status = row.original.status || 'pending'

                    return (
                        <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>
                            <StatusDropdown
                                currentStatus={status}
                                onStatusChange={handleStatusChange}
                                quoteId={row.original._id}
                            />
                        </div>
                    )
                },
                enableSorting: false
            }),
            columnHelper.accessor('action', {
                header: 'Action',
                cell: ({ row }) => (
                    <div className='flex items-center gap-1'>
                        {/* Delete */}
                        <IconButton
                            onClick={() => handleDeleteQuote(row.original._id)}
                            title="Delete Quote"
                            size='small'
                            sx={{
                                color: 'text.secondary',
                                '&:hover': {
                                    color: 'error.main',
                                    backgroundColor: 'error.light',
                                    transform: 'scale(1.1)'
                                },
                                transition: 'all 0.2s ease-in-out'
                            }}
                        >
                            <i className='tabler-trash' />
                        </IconButton>

                        {/* View Details */}
                        <IconButton
                            onClick={() => handleViewDetails(row.original)}
                            title="View Details"
                            size='small'
                            sx={{
                                color: 'text.secondary',
                                '&:hover': {
                                    color: 'info.main',
                                    backgroundColor: 'info.light',
                                    transform: 'scale(1.1)'
                                },
                                transition: 'all 0.2s ease-in-out'
                            }}
                        >
                            <i className='tabler-info-circle' />
                        </IconButton>
                    </div>
                ),
                enableSorting: false
            })
        ],
        [data, filteredData]
    )

    // React Table
    const table = useReactTable({
        data: filteredData,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter
        },
        state: {
            globalFilter
        },
        initialState: {
            pagination: {
                pageSize: 10
            }
        },
        globalFilterFn: fuzzyFilter,
        onGlobalFilterChange: setGlobalFilter,
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
        getFacetedMinMaxValues: getFacetedMinMaxValues()
    })

    // Loading state
    if (loading) {
        return (
            <Card>
                <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
                        <Typography>Loading quotes...</Typography>
                    </Box>
                </CardContent>
            </Card>
        )
    }

    // Error state
    if (error) {
        return (
            <Card>
                <CardContent>
                    <Alert severity="error" sx={{ mb: 4 }}>
                        {error}
                    </Alert>
                    <Box sx={{ textAlign: 'center' }}>
                        <Button
                            variant="contained"
                            onClick={() => window.location.reload()}
                            startIcon={<i className='tabler-refresh' />}
                        >
                            Retry
                        </Button>
                    </Box>
                </CardContent>
            </Card>
        )
    }

    return (
        <>
            <Card>
                <CardHeader
                    title='Quotes Management'
                    subheader='View and manage quote requests'
                    className='pb-2'
                />
                <CardContent>
                    <div className='flex justify-between gap-4 p-6 border-be'>
                        <div className='flex items-center gap-2'>
                            <Typography className='text-xl font-bold'>
                                Quotes
                            </Typography>
                        </div>
                        <div className='flex items-center gap-4'>
                            <DebouncedInput
                                value={globalFilter ?? ''}
                                onChange={value => setGlobalFilter(String(value))}
                                placeholder='Search quotes...'
                                className='max-sm:is-full'
                            />
                            <IconButton
                                variant='outlined'
                                color='text.primary'
                                onClick={() => window.location.reload()}
                                className='max-sm:is-full'
                            >
                                <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />
                            </IconButton>
                        </div>
                    </div>
                    <div className='overflow-x-auto'>
                        <table className={tableStyles.table}>
                            <thead>
                                {table.getHeaderGroups().map(headerGroup => (
                                    <tr key={headerGroup.id}>
                                        {headerGroup.headers.map(header => (
                                            <th key={header.id}>
                                                {header.isPlaceholder ? null : (
                                                    <>
                                                        <div
                                                            className={classnames({
                                                                'flex items-center': header.column.getIsSorted(),
                                                                'cursor-pointer select-none': header.column.getCanSort()
                                                            })}
                                                            onClick={header.column.getToggleSortingHandler()}
                                                        >
                                                            {flexRender(header.column.columnDef.header, header.getContext())}
                                                            {{
                                                                asc: <i className='tabler-chevron-up text-xl' />,
                                                                desc: <i className='tabler-chevron-down text-xl' />
                                                            }[header.column.getIsSorted()] ?? null}
                                                        </div>
                                                    </>
                                                )}
                                            </th>
                                        ))}
                                    </tr>
                                ))}
                            </thead>
                            {table.getFilteredRowModel().rows.length === 0 ? (
                                <tbody>
                                    <tr>
                                        <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>
                                            No quotes available
                                        </td>
                                    </tr>
                                </tbody>
                            ) : (
                                <tbody>
                                    {table
                                        .getRowModel()
                                        .rows.slice(0, table.getState().pagination.pageSize)
                                        .map(row => {
                                            return (
                                                <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>
                                                    {row.getVisibleCells().map(cell => (
                                                        <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
                                                    ))}
                                                </tr>
                                            )
                                        })}
                                </tbody>
                            )}
                        </table>
                    </div>
                    <TablePaginationComponent table={table} />
                </CardContent>
            </Card>

            {/* Quote Details Modal */}
            {selectedQuote && (
                <QuoteDetailsModal
                    open={showModal}
                    onClose={handleCloseModal}
                    quoteData={selectedQuote}
                />
            )}
        </>
    )
}

export default QuotesTable
