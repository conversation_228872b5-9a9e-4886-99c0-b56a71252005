const Contact = require("./model/contact");
const Jobs = require("./model/jobs");
const Urgent = require("./model/urgent_Inquiry");
const cron = require('node-cron');
const mongoose = require('mongoose');
require('dotenv').config();

mongoose.connect(process.env.MONGO_URI, {
    // useNewUrlParser: true, // Deprecated in recent versions of Mongoose
    // useUnifiedTopology: true, // Deprecated in recent versions of Mongoose
}).then(() => {
    console.log('MongoDB connected for check_remaining.js');
}).catch(err => {
    console.error('MongoDB connection error in check_remaining.js:', err);
    process.exit(1);
}); 

async function checkRemaining() { 
    const now = new Date(); 
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);

    const contactCount = await Contact.countDocuments({ createdAt: { $gte: fifteenMinutesAgo } });
    const jobsCount = await Jobs.countDocuments({ createdAt: { $gte: fifteenMinutesAgo } });
    const urgentCount = await Urgent.countDocuments({ createdAt: { $gte: fifteenMinutesAgo } });

    console.log('Contact remaining (global 15 min):', 15 - contactCount);
    console.log('Jobs remaining (global 15 min):', 15 - jobsCount);
    console.log('Urgent Inquiry remaining (global 15 min):', 15 - urgentCount);
}

checkRemaining();

cron.schedule('0 * * * *', async () => {
    console.log('Running scheduled cleanup job...');
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours for new limits

    // Fetch all relevant contacts once within the last 24 hours
    const recentContactsIn24h = await Contact.find({ createdAt: { $gte: oneDayAgo } }).sort({ createdAt: 1 });
    const recentJobsIn24h = await Jobs.find({ createdAt: { $gte: oneDayAgo } }).sort({ createdAt: 1 });
    const recentUrgentsIn24h = await Urgent.find({ createdAt: { $gte: oneDayAgo } }).sort({ createdAt: 1 });

    // --- Cleanup for Contact Per-Email 24-hour Limit (15 submissions) ---
    const contactEmailMap = new Map();
    for (const contact of recentContactsIn24h) {
        if (!contactEmailMap.has(contact.email)) {
            contactEmailMap.set(contact.email, []);
        }
        contactEmailMap.get(contact.email).push(contact);
    }

    for (const [email, contacts] of contactEmailMap.entries()) {
        if (contacts.length >= 15) { // Check for 15 submissions in 24 hours
            const oldestContactIn24h = contacts[0].createdAt; // The earliest contact that contributed to this 24h window limit
            const unblockDate = new Date(oldestContactIn24h.getTime() + 24 * 60 * 60 * 1000);

            if (now > unblockDate) {
                await Contact.deleteMany({
                    email,
                    createdAt: { $lte: oldestContactIn24h } // Delete contacts up to the oldest one that contributed to the block
                });
                console.log(`Cleaned up expired 24h email block for Contact: ${email}`);
            }
        }
    }

    // --- Cleanup for Contact IP-based 24-hour Limit (5 submissions) ---
    const contactIpMap = new Map();
    for (const contact of recentContactsIn24h) {
        if (!contactIpMap.has(contact.ip)) {
            contactIpMap.set(contact.ip, []);
        }
        contactIpMap.get(contact.ip).push(contact);
    }

    for (const [ip, contacts] of contactIpMap.entries()) {
        if (contacts.length >= 5) { // Check for 5 submissions in 24 hours
            const oldestContactIn24h = contacts[0].createdAt; // The earliest contact that contributed to this 24h window limit
            const unblockDate = new Date(oldestContactIn24h.getTime() + 24 * 60 * 60 * 1000);

            if (now > unblockDate) {
                await Contact.deleteMany({
                    ip,
                    createdAt: { $lte: oldestContactIn24h }
                });
                console.log(`Cleaned up expired 24h IP block for Contact: ${ip}`);
            }
        }
    }

    // --- Cleanup for Jobs Per-Email 24-hour Limit (3 submissions) ---
    const jobsEmailMap = new Map();
    for (const job of recentJobsIn24h) {
        if (!jobsEmailMap.has(job.email)) {
            jobsEmailMap.set(job.email, []);
        }
        jobsEmailMap.get(job.email).push(job);
    }

    for (const [email, jobs] of jobsEmailMap.entries()) {
        if (jobs.length >= 3) { // Check for 3 submissions in 24 hours
            const oldestJobIn24h = jobs[0].createdAt;
            const unblockDate = new Date(oldestJobIn24h.getTime() + 24 * 60 * 60 * 1000);

            if (now > unblockDate) {
                await Jobs.deleteMany({
                    email,
                    createdAt: { $lte: oldestJobIn24h }
                });
                console.log(`Cleaned up expired 24h email block for Jobs: ${email}`);
            }
        }
    }

    // --- Cleanup for Jobs IP-based 24-hour Limit (3 submissions) ---
    const jobsIpMap = new Map();
    for (const job of recentJobsIn24h) {
        if (!jobsIpMap.has(job.ip)) {
            jobsIpMap.set(job.ip, []);
        }
        jobsIpMap.get(job.ip).push(job);
    }

    for (const [ip, jobs] of jobsIpMap.entries()) {
        if (jobs.length >= 3) { // Check for 3 submissions in 24 hours
            const oldestJobIn24h = jobs[0].createdAt;
            const unblockDate = new Date(oldestJobIn24h.getTime() + 24 * 60 * 60 * 1000);

            if (now > unblockDate) {
                await Jobs.deleteMany({
                    ip,
                    createdAt: { $lte: oldestJobIn24h }
                });
                console.log(`Cleaned up expired 24h IP block for Jobs: ${ip}`);
            }
        }
    }

    // --- Cleanup for Urgent Inquiry Per-Email 24-hour Limit (15 submissions) ---
    const urgentEmailMap = new Map();
    for (const urgent of recentUrgentsIn24h) {
        if (!urgentEmailMap.has(urgent.email)) {
            urgentEmailMap.set(urgent.email, []);
        }
        urgentEmailMap.get(urgent.email).push(urgent);
    }

    for (const [email, urgents] of urgentEmailMap.entries()) {
        if (urgents.length >= 15) { // Check for 15 submissions in 24 hours
            const oldestUrgentIn24h = urgents[0].createdAt;
            const unblockDate = new Date(oldestUrgentIn24h.getTime() + 24 * 60 * 60 * 1000);

            if (now > unblockDate) {
                await Urgent.deleteMany({
                    email,
                    createdAt: { $lte: oldestUrgentIn24h }
                });
                console.log(`Cleaned up expired 24h email block for Urgent Inquiry: ${email}`);
            }
        }
    }

    // --- Cleanup for Urgent Inquiry IP-based 24-hour Limit (5 submissions) ---
    const urgentIpMap = new Map();
    for (const urgent of recentUrgentsIn24h) {
        if (!urgentIpMap.has(urgent.ip)) {
            urgentIpMap.set(urgent.ip, []);
        }
        urgentIpMap.get(urgent.ip).push(urgent);
    }

    for (const [ip, urgents] of urgentIpMap.entries()) {
        if (urgents.length >= 5) { // Check for 5 submissions in 24 hours
            const oldestUrgentIn24h = urgents[0].createdAt;
            const unblockDate = new Date(oldestUrgentIn24h.getTime() + 24 * 60 * 60 * 1000);

            if (now > unblockDate) {
                await Urgent.deleteMany({
                    ip,
                    createdAt: { $lte: oldestUrgentIn24h }
                });
                console.log(`Cleaned up expired 24h IP block for Urgent Inquiry: ${ip}`);
            }
        }
    }
}); 