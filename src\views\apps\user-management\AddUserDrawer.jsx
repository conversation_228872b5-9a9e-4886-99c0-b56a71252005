'use client'

// React Imports
import { useState } from 'react'

// Next Imports
import { useSession } from 'next-auth/react'

// MUI Imports
import Drawer from '@mui/material/Drawer'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import Box from '@mui/material/Box'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import IconButton from '@mui/material/IconButton'
import Divider from '@mui/material/Divider'

// Third-party Imports
import { useForm, Controller } from 'react-hook-form'

// Utils Imports
// Temporary fallback for roleUtils
const getAvailableRoles = (currentUserRole) => {
  const roles = [
    { value: 'normal_user', label: 'Normal User' },
    { value: 'admin', label: 'Admin' }
  ]

  // Only super admin can assign super admin role
  if (currentUserRole === 'super_admin') {
    roles.push({ value: 'super_admin', label: 'Super Admin' })
  }

  return roles
}
// import { getAvailableRoles } from '@/utils/roleUtils'

const AddUserDrawer = ({ open, onClose, onUserAdded }) => {
  // Hooks
  const { data: session } = useSession()
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    defaultValues: {
      username: '',
      email: '',
      password: '',
      role: 'normal_user'
    }
  })

  // States
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)

  // Handlers
  const onSubmit = async (data) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      if (!session?.user?.id) {
        setError('Authentication required. Please login.')
        setLoading(false)
        return
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

      const response = await fetch(`${API_BASE_URL}/user-profile/users/create?userId=${session.user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess(true)
        reset()
        setTimeout(() => {
          onUserAdded()
          handleClose()
        }, 1500)
      } else {
        setError(result.message || 'Failed to create user')
      }

    } catch (error) {
      console.error('Error creating user:', error)
      setError('Failed to create user. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    reset()
    setError(null)
    setSuccess(false)
    onClose()
  }

  const availableRoles = getAvailableRoles(session?.user?.role)

  return (
    <Drawer
      open={open}
      anchor="right"
      variant="temporary"
      onClose={handleClose}
      ModalProps={{ keepMounted: true }}
      sx={{ '& .MuiDrawer-paper': { width: { xs: 300, sm: 400 } } }}
    >
      <Box sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
          <Typography variant="h5">Add New User</Typography>
          <IconButton onClick={handleClose}>
            <i className="tabler-x" />
          </IconButton>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {/* Success Message */}
        {success && (
          <Alert severity="success" sx={{ mb: 4 }}>
            User created successfully! They will receive login instructions via email.
          </Alert>
        )}

        {/* Error Message */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            {/* Username */}
            <Controller
              name="username"
              control={control}
              rules={{
                required: 'Username is required',
                minLength: { value: 3, message: 'Username must be at least 3 characters' }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Username"
                  placeholder="Enter username"
                  error={!!errors.username}
                  helperText={errors.username?.message}
                />
              )}
            />

            {/* Email */}
            <Controller
              name="email"
              control={control}
              rules={{
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Email"
                  placeholder="Enter email address"
                  type="email"
                  error={!!errors.email}
                  helperText={errors.email?.message}
                />
              )}
            />

            {/* Password */}
            <Controller
              name="password"
              control={control}
              rules={{
                required: 'Password is required',
                minLength: { value: 6, message: 'Password must be at least 6 characters' }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label="Password"
                  placeholder="Enter password"
                  type="password"
                  error={!!errors.password}
                  helperText={errors.password?.message}
                />
              )}
            />

            {/* Role */}
            <Controller
              name="role"
              control={control}
              rules={{ required: 'Role is required' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.role}>
                  <InputLabel>Role</InputLabel>
                  <Select {...field} label="Role">
                    {availableRoles.map((role) => (
                      <MenuItem key={role.value} value={role.value}>
                        {role.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.role && (
                    <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                      {errors.role.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />

            {/* Info Box */}
            <Alert severity="info">
              <Typography variant="body2">
                <strong>Note:</strong> The new user will receive an email with login instructions.
                They will need to verify their email address on first login before setting up MFA.
              </Typography>
            </Alert>

            {/* Action Buttons */}
            <Box sx={{ display: 'flex', gap: 2, mt: 4 }}>
              <Button
                fullWidth
                variant="contained"
                type="submit"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <i className="tabler-plus" />}
              >
                {loading ? 'Creating...' : 'Create User'}
              </Button>
              <Button
                fullWidth
                variant="outlined"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
            </Box>
          </Box>
        </form>
      </Box>
    </Drawer>
  )
}

export default AddUserDrawer
