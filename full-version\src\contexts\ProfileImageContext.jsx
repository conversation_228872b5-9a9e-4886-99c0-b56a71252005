'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

const ProfileImageContext = createContext()

export const useProfileImage = () => {
  const context = useContext(ProfileImageContext)
  if (!context) {
    throw new Error('useProfileImage must be used within a ProfileImageProvider')
  }
  return context
}

export const ProfileImageProvider = ({ children }) => {
  const { data: session } = useSession()
  const [profileImage, setProfileImage] = useState(null)

  // Get first letter of user name
  const getFirstLetter = () => {
    const name = session?.user?.name || 'A'
    return name.charAt(0).toUpperCase()
  }

  // Load profile image from localStorage on mount
  useEffect(() => {
    if (session?.user?.email) {
      const savedImage = localStorage.getItem(`profileImage_${session.user.email}`)
      if (savedImage) {
        setProfileImage(savedImage)
      }
    }
  }, [session?.user?.email])

  // Update profile image and save to localStorage
  const updateProfileImage = (imageData) => {
    setProfileImage(imageData)
    if (session?.user?.email) {
      if (imageData) {
        localStorage.setItem(`profileImage_${session.user.email}`, imageData)
      } else {
        localStorage.removeItem(`profileImage_${session.user.email}`)
      }
    }
  }

  // Get current profile image or first letter
  const getCurrentProfileDisplay = () => {
    return {
      hasImage: !!profileImage,
      imageUrl: profileImage,
      firstLetter: getFirstLetter(),
      userName: session?.user?.name || 'User'
    }
  }

  const value = {
    profileImage,
    updateProfileImage,
    getCurrentProfileDisplay,
    getFirstLetter
  }

  return (
    <ProfileImageContext.Provider value={value}>
      {children}
    </ProfileImageContext.Provider>
  )
}
