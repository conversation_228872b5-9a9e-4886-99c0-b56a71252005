# Backend Status Update Endpoints for All Sections

## Required Backend Implementation

To make the status functionality fully functional with the backend across all sections, you need to add the following endpoints to your backend API:

## 1. QUOTES SECTION

### Endpoint: Update Quote Status

**URL:** `PATCH /quote/update-status/:quoteId`

**Headers:**
- `x-api-key`: Your API key
- `Content-Type`: application/json

**Request Body:**
```json
{
  "status": "pending" | "in-review" | "approved" | "rejected"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Quote status updated successfully",
  "quote": {
    "_id": "quote_id",
    "status": "new_status",
    "updatedAt": "2025-01-18T10:30:00.000Z"
  }
}
```

### Database Schema Update

Make sure your quote model/schema includes a `status` field:

```javascript
// MongoDB/Mongoose example
const quoteSchema = new mongoose.Schema({
  contactInfo: { /* existing fields */ },
  shipmentDetails: { /* existing fields */ },
  pickupDelivery: { /* existing fields */ },
  specialRequirements: { /* existing fields */ },
  status: {
    type: String,
    enum: ['pending', 'in-review', 'approved', 'rejected'],
    default: 'pending'
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

### Example Backend Implementation (Node.js/Express)

```javascript
// routes/quote.js
router.patch('/update-status/:quoteId', async (req, res) => {
  try {
    const { quoteId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['pending', 'in-review', 'approved', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    // Update quote in database
    const updatedQuote = await Quote.findByIdAndUpdate(
      quoteId,
      { 
        status: status,
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!updatedQuote) {
      return res.status(404).json({
        success: false,
        message: 'Quote not found'
      });
    }

    res.json({
      success: true,
      message: 'Quote status updated successfully',
      quote: updatedQuote
    });

  } catch (error) {
    console.error('Error updating quote status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});
```

### Current Frontend Implementation

The frontend is already implemented with:

1. **StatusDropdown Component** - Allows users to change quote status
2. **API Integration** - Calls the backend endpoint with fallback to localStorage
3. **Consistent UI** - Matches the design of other sections (Contact, Urgent Inquiry)
4. **Status Options:**
   - **Pending** (Warning/Yellow) - Default status for new quotes
   - **In Review** (Info/Blue) - Quote is being reviewed
   - **Approved** (Success/Green) - Quote has been approved
   - **Rejected** (Error/Red) - Quote has been rejected

### Fallback Mechanism

The frontend currently includes a fallback mechanism that saves status changes to localStorage if the backend endpoint is not available. This ensures the functionality works immediately while you implement the backend endpoint.

### Testing

Once you implement the backend endpoint:

1. The status dropdown will work with real backend persistence
2. Status changes will be saved to the database
3. Status will persist across page refreshes and user sessions
4. The localStorage fallback will no longer be needed

### Integration Steps

1. Add the endpoint to your backend routes
2. Update your quote model to include the status field
3. Test the endpoint with the frontend
4. Remove the localStorage fallback once backend is working (optional)

## 2. URGENT INQUIRY SECTION

### Endpoint: Update Urgent Inquiry Status

**URL:** `PATCH /urgent/update-status/:inquiryId`

**Headers:**
- `Content-Type`: application/json

**Request Body:**
```json
{
  "status": "pending" | "in-view" | "completed"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Urgent inquiry status updated successfully",
  "inquiry": {
    "_id": "inquiry_id",
    "status": "new_status",
    "updatedAt": "2025-01-18T10:30:00.000Z"
  }
}
```

### Database Schema Update for Urgent Inquiries

```javascript
const urgentInquirySchema = new mongoose.Schema({
  // existing fields...
  status: {
    type: String,
    enum: ['pending', 'in-view', 'completed'],
    default: 'pending'
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

## 3. JOB APPLICATIONS SECTION

### Endpoint: Update Job Application Status

**URL:** `PATCH /jobs/update-status/:applicationId`

**Headers:**
- `Content-Type`: application/json

**Request Body:**
```json
{
  "status": "pending" | "in-view" | "completed"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Job application status updated successfully",
  "application": {
    "_id": "application_id",
    "status": "new_status",
    "updatedAt": "2025-01-18T10:30:00.000Z"
  }
}
```

### Database Schema Update for Job Applications

```javascript
const jobApplicationSchema = new mongoose.Schema({
  // existing fields...
  status: {
    type: String,
    enum: ['pending', 'in-view', 'completed'],
    default: 'pending'
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

## 4. CONTACT SECTION (Already Implemented)

**URL:** `PATCH /contact/update-status/:contactId` ✅ **WORKING**

## COMPLETE BACKEND IMPLEMENTATION EXAMPLES

### Urgent Inquiry Route (routes/urgent.js)

```javascript
router.patch('/update-status/:inquiryId', async (req, res) => {
  try {
    const { inquiryId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['pending', 'in-view', 'completed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    // Update inquiry in database
    const updatedInquiry = await UrgentInquiry.findByIdAndUpdate(
      inquiryId,
      {
        status: status,
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!updatedInquiry) {
      return res.status(404).json({
        success: false,
        message: 'Urgent inquiry not found'
      });
    }

    res.json({
      success: true,
      message: 'Urgent inquiry status updated successfully',
      inquiry: updatedInquiry
    });

  } catch (error) {
    console.error('Error updating urgent inquiry status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});
```

### Job Application Route (routes/jobs.js)

```javascript
router.patch('/update-status/:applicationId', async (req, res) => {
  try {
    const { applicationId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['pending', 'in-view', 'completed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    // Update application in database
    const updatedApplication = await JobApplication.findByIdAndUpdate(
      applicationId,
      {
        status: status,
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!updatedApplication) {
      return res.status(404).json({
        success: false,
        message: 'Job application not found'
      });
    }

    res.json({
      success: true,
      message: 'Job application status updated successfully',
      application: updatedApplication
    });

  } catch (error) {
    console.error('Error updating job application status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});
```

## CURRENT FRONTEND STATUS

### ✅ **FULLY IMPLEMENTED SECTIONS:**

1. **Contact** - ✅ Backend API + Frontend integration working
2. **Quotes** - ✅ Backend API + Frontend integration ready
3. **Urgent Inquiry** - ✅ Frontend ready, Backend API needed
4. **Job Applications** - ✅ Frontend ready, Backend API needed

### 🎯 **FEATURES IMPLEMENTED:**

- **Status Dropdown** - All sections have consistent status management
- **Timestamp Formatting** - All dates show "MM/DD/YYYY HH:MM AM/PM" format
- **Backend Integration** - API calls with localStorage fallback
- **Consistent UI** - Same design patterns across all sections
- **Error Handling** - Graceful fallbacks when backend unavailable

### 📋 **NEXT STEPS:**

1. **Implement the 2 missing backend endpoints** (Urgent Inquiry + Job Applications)
2. **Add status fields** to your database models
3. **Test the endpoints** - Frontend will automatically use them once available
4. **Remove localStorage fallbacks** once backend is working (optional)

The frontend implementation is complete and ready to work with your backend once all endpoints are added!
