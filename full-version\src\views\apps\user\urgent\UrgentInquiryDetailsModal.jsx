'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Avatar from '@mui/material/Avatar'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import Chip from '@mui/material/Chip'

// API Imports
// import { downloadUrgentDocument } from '@/services/urgentApi'

const UrgentInquiryDetailsModal = ({ open, onClose, inquiryData }) => {
  if (!inquiryData) return null

  // Handle document download
  // const handleDownloadDocument = async () => {
  //   try {
  //     if (!inquiryData.documents) {
  //       alert('No document available for this inquiry.')
  //       return
  //     }

  //     await downloadUrgentDocument(inquiryData.documents, inquiryData.fullName)
  //   } catch (error) {
  //     console.error('Error downloading document:', error)
  //     alert('Failed to download document. Please try again.')
  //   }
  // }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h4" style={{ fontSize: '1.8rem' }}>Urgent Inquiry Details</Typography>
        <IconButton onClick={onClose}>
          <i className="tabler-x" />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={6}>
          {/* Contact Profile Section */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <div className="flex items-center gap-6 mb-6">
                  <Avatar
                    sx={{ width: 120, height: 120, fontSize: '3.5rem', fontWeight: 'bold', backgroundColor: '#f44336' }}
                  >
                    {inquiryData.fullName?.charAt(0)?.toUpperCase()}
                  </Avatar>
                  <div>
                    <Typography variant="h3" className="mb-3 font-bold" style={{ fontSize: '2.2rem' }}>
                      {inquiryData.fullName}
                    </Typography>
                    <Typography variant="h5" color="text.secondary" className="font-medium" style={{ fontSize: '1.4rem' }}>
                      {inquiryData.email}
                    </Typography>
                  </div>
                </div>

                <Divider className="mb-6" />

                {/* Contact Information */}
                <Typography variant="h5" className="mb-4 font-bold" style={{ fontSize: '1.6rem' }}>
                  Contact Information
                </Typography>
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Full Name
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {inquiryData.full_name || inquiryData.fullName || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Email
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {inquiryData.email || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Phone Number
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {inquiryData.phone_number || inquiryData.phone || '+****************'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Reference Number
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {inquiryData.ref_number || inquiryData.referenceNumber || 'URG-2025-001'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Urgency Information */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <Typography variant="h5" className="mb-4 font-bold" style={{ fontSize: '1.6rem' }}>
                  Urgency Information
                </Typography>
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Urgency Type
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {inquiryData.urgency_type || inquiryData.urgencyType || 'Emergency'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Other Urgency
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {inquiryData.other_urgency || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Documents
                    </Typography>
                    <div className="flex items-center gap-3">
                      <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                        {inquiryData.documents ? 'Documents attached' : 'No documents attached'}
                      </Typography>
                      {/* {inquiryData.documents && (
                        <Button
                          variant="contained"
                          color="primary"
                          size="small"
                          startIcon={<i className="tabler-download" />}
                          onClick={handleDownloadDocument}
                          sx={{ ml: 2 }}
                        >
                          Download
                        </Button>
                      )} */}
                    </div>
                  </Grid>

                  <Grid size={{ xs: 12 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Brief Description
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem', lineHeight: '1.4' }}>
                      {inquiryData.brief_description || inquiryData.description || 'This is an urgent matter that requires immediate attention. Please contact me as soon as possible to discuss the details and next steps.'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Additional Information */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <Typography variant="h5" className="mb-4 font-bold" style={{ fontSize: '1.6rem' }}>
                  Additional Information
                </Typography>
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Submitted Date
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {(() => {
                        // Use actual submission time from database (createdAt or submittedDate)
                        const dateString = inquiryData.submittedDate || inquiryData.createdAt
                        if (!dateString) {
                          const today = new Date()
                          const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
                          const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }
                          const formattedDate = today.toLocaleDateString('en-US', dateOptions)
                          const formattedTime = today.toLocaleTimeString('en-US', timeOptions)
                          return `${formattedDate} ${formattedTime}`
                        }

                        try {
                          // Parse the actual timestamp from database
                          const date = new Date(dateString)
                          if (isNaN(date.getTime())) return dateString

                          const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
                          const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }

                          const formattedDate = date.toLocaleDateString('en-US', dateOptions)
                          const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

                          return `${formattedDate} ${formattedTime}`
                        } catch (error) {
                          return dateString
                        }
                      })()}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Current Status
                    </Typography>
                    <Chip
                      label={inquiryData.status === 'pending' ? 'Pending' :
                        inquiryData.status === 'in-view' ? 'In View' :
                          inquiryData.status === 'completed' ? 'Completed' : 'Pending'}
                      color={inquiryData.status === 'pending' ? 'warning' :
                        inquiryData.status === 'in-view' ? 'info' :
                          inquiryData.status === 'completed' ? 'success' : 'warning'}
                      variant="tonal"
                      size="medium"
                      sx={{ fontSize: '1rem', fontWeight: 'bold' }}
                    />
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Priority Level
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem', color: '#f44336' }}>
                      HIGH PRIORITY
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default UrgentInquiryDetailsModal
