{"name": "vuexy-mui-nextjs-admin-template", "version": "4.0.0", "license": "Commercial", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "build:icons": "tsx src/assets/iconify-icons/bundle-icons-css.mjs", "migrate": "dotenv -e .env -- npx prisma migrate dev", "postinstall": "prisma generate && npm run build:icons", "removeI18n": "tsx src/remove-translation-scripts/index.js"}, "dependencies": {"@auth/prisma-adapter": "2.7.4", "@emoji-mart/data": "1.2.1", "@emoji-mart/react": "1.1.1", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@floating-ui/react": "0.27.2", "@formatjs/intl-localematcher": "0.5.9", "@formkit/drag-and-drop": "0.2.6", "@fullcalendar/common": "5.11.5", "@fullcalendar/core": "6.1.15", "@fullcalendar/daygrid": "6.1.15", "@fullcalendar/interaction": "6.1.15", "@fullcalendar/list": "6.1.15", "@fullcalendar/react": "6.1.15", "@fullcalendar/timegrid": "6.1.15", "@hookform/resolvers": "3.9.1", "@iconify/react": "^6.0.0", "@mui/icons-material": "^6.2.1", "@mui/lab": "6.0.0-beta.19", "@mui/material": "6.2.1", "@mui/material-nextjs": "6.2.1", "@mui/x-data-grid": "^6.2.1", "@mui/x-date-pickers": "^8.5.2", "@prisma/client": "5.22.0", "@reduxjs/toolkit": "2.5.0", "@tanstack/match-sorter-utils": "8.19.4", "@tanstack/react-table": "8.20.6", "@tiptap/extension-color": "^2.10.4", "@tiptap/extension-list-item": "^2.10.4", "@tiptap/extension-placeholder": "^2.10.4", "@tiptap/extension-text-align": "^2.10.4", "@tiptap/extension-text-style": "^2.10.4", "@tiptap/extension-underline": "^2.10.4", "@tiptap/pm": "^2.10.4", "@tiptap/react": "^2.10.4", "@tiptap/starter-kit": "^2.10.4", "apexcharts": "3.49.0", "bootstrap-icons": "1.11.3", "classnames": "2.5.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "emoji-mart": "5.6.0", "fs-extra": "11.2.0", "input-otp": "1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "keen-slider": "6.8.6", "mapbox-gl": "3.9.0", "negotiator": "1.0.0", "next": "^15.3.3", "next-auth": "^4.24.7", "react": "18.3.1", "react-apexcharts": "1.4.1", "react-colorful": "5.6.1", "react-datepicker": "7.3.0", "react-dom": "18.3.1", "react-dropzone": "14.3.5", "react-hook-form": "7.54.1", "react-map-gl": "7.1.8", "react-perfect-scrollbar": "1.5.8", "react-player": "2.16.0", "react-redux": "9.2.0", "react-toastify": "10.0.6", "react-use": "17.6.0", "recharts": "2.15.0", "server-only": "0.0.1", "valibot": "0.42.1"}, "devDependencies": {"@iconify/json": "2.2.286", "@iconify/tools": "4.1.1", "@iconify/utils": "2.2.1", "autoprefixer": "10.4.20", "consola": "3.3.0", "dotenv-cli": "7.4.4", "eslint": "8.57.1", "eslint-config-next": "15.1.2", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "globby": "14.0.2", "postcss": "8.4.49", "postcss-styled-syntax": "0.7.0", "prettier": "3.4.2", "prisma": "5.22.0", "stylelint": "16.12.0", "stylelint-use-logical-spec": "5.0.1", "stylis": "4.3.4", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "3.4.17", "tailwindcss-logical": "3.0.1", "tsx": "^4.20.3"}, "resolutions": {"rimraf": "^5.0.7", "@tiptap/core": "^2.0.0"}, "overrides": {"rimraf": "^5.0.7"}, "prisma": {"schema": "./src/prisma/schema.prisma"}}