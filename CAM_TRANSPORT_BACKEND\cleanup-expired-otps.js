const mongoose = require('mongoose');
const { CleanupExpiredOTPs } = require('./controller/Login');
require('dotenv').config();

async function runCleanup() {
    try {
        console.log('🧹 Starting OTP cleanup process...');
        
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log('🔗 Connected to MongoDB');
        
        // Run the cleanup
        const result = await CleanupExpiredOTPs();
        
        if (result && result.modifiedCount > 0) {
            console.log(`✅ Successfully cleaned up ${result.modifiedCount} expired OTPs`);
        } else {
            console.log('✅ No expired OTPs found to clean up');
        }
        
    } catch (error) {
        console.error('❌ Cleanup failed:', error);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

// Run cleanup if this script is executed directly
if (require.main === module) {
    runCleanup();
}

module.exports = { runCleanup };
