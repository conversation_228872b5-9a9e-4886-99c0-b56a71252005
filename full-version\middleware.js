// Third-party Imports
import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl
    const token = req.nextauth.token

    console.log('Middleware - Path:', pathname, 'Token exists:', !!token, 'Is verified:', token?.isVerified, 'Requires MFA:', token?.requiresMFA, 'MFA verified:', token?.mfaVerified)

    // Check if this is a login page
    const isLoginPage = pathname.includes('/login') && !pathname.includes('/mfa-verify')
    const isMfaVerifyPage = pathname.includes('/mfa-verify')
    const isEmailOtpPage = pathname.includes('/email-otp-verify')

    // CRITICAL SECURITY FIX: Never redirect users away from login pages
    // This prevents the bypass where users can go back and reload to skip MFA
    if (isLoginPage || isEmailOtpPage) {
      console.log('User on login/email-otp page - allowing access to ensure fresh authentication')
      return NextResponse.next()
    }

    // For MFA verification page, allow access if user has a token
    if (isMfaVerifyPage && token) {
      return NextResponse.next()
    }

    // Check MFA requirements for authenticated users accessing protected pages
    if (token && token.isVerified && token.requiresMFA && !token.mfaVerified) {
      // User needs MFA verification for protected pages
      if (!isMfaVerifyPage) {
        console.log('Redirecting to MFA verification - user requires MFA for protected access')
        const params = new URLSearchParams({
          email: token.email || '',
          username: token.name || ''
        })
        return NextResponse.redirect(new URL(`/en/pages/auth/mfa-verify?${params.toString()}`, req.url))
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow access to public pages and API routes
        if (
          pathname.includes('/login') ||
          pathname.includes('/mfa-verify') ||
          pathname.includes('/email-otp-verify') ||
          pathname.includes('/api/') ||
          pathname.includes('/_next') ||
          pathname.includes('/favicon.ico') ||
          pathname.startsWith('/images/') ||
          pathname.startsWith('/icons/')
        ) {
          return true
        }

        // For protected pages (like user-profile), require authentication
        if (pathname.includes('/pages/user-profile')) {
          return !!token
        }

        // Allow all other pages for now
        return true
      }
    }
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)'
  ]
}
