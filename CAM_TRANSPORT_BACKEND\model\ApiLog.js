const mongoose = require('mongoose');

const ApiLogSchema = new mongoose.Schema({
    method: {
        type: String,
        required: true
    },
    originalUrl: {
        type: String,
        required: true
    },
    ipAddress: {
        type: String,
        required: true
    },
    hitTime: {
        type: Date,
        default: Date.now
    },
    leaveTime: {
        type: Date
    },
    statusCode: {
        type: Number
    },
    duration: {
        type: Number // in milliseconds
    }
}, { timestamps: true });

module.exports = mongoose.model('ApiLog', ApiLogSchema); 