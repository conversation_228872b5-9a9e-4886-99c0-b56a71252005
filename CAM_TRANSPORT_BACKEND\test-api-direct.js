const fetch = require('node-fetch');

async function testAPI() {
  try {
    console.log('🌐 Testing User Management API directly...');
    
    const API_BASE_URL = 'http://localhost:8090';
    
    // Test 1: Basic API call without authentication
    console.log('\n1️⃣ Testing without authentication (should use development fallback)...');
    const response1 = await fetch(`${API_BASE_URL}/user-profile/users?page=1&limit=10`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`📡 Response status: ${response1.status}`);
    
    if (response1.ok) {
      const result1 = await response1.json();
      console.log('✅ Success:', JSON.stringify(result1, null, 2));
    } else {
      const errorText1 = await response1.text();
      console.log('❌ Error:', errorText1);
    }
    
    // Test 2: With userId parameter
    console.log('\n2️⃣ Testing with userId parameter...');
    const response2 = await fetch(`${API_BASE_URL}/user-profile/users?page=1&limit=10&userId=675e8b8b8b8b8b8b8b8b8b8b`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`📡 Response status: ${response2.status}`);
    
    if (response2.ok) {
      const result2 = await response2.json();
      console.log('✅ Success:', JSON.stringify(result2, null, 2));
    } else {
      const errorText2 = await response2.text();
      console.log('❌ Error:', errorText2);
    }
    
    // Test 3: Check if server is running
    console.log('\n3️⃣ Testing basic server health...');
    const response3 = await fetch(`${API_BASE_URL}/`, {
      method: 'GET',
    });
    
    console.log(`📡 Server response status: ${response3.status}`);
    
  } catch (error) {
    console.error('❌ Network Error:', error.message);
    console.log('💡 Make sure the backend server is running on port 8090');
  }
}

testAPI();
