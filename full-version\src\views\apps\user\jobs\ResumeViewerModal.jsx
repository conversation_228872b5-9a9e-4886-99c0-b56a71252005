'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Box from '@mui/material/Box'

const ResumeViewerModal = ({ open, onClose, applicantData }) => {
  const handleDownloadResume = () => {
    // Create a sample PDF download functionality
    const element = document.createElement('a')
    const today = new Date()
    const formattedDate = `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`
    const file = new Blob([`Resume for ${applicantData?.fullName}\n\nPersonal Information:\nName: ${applicantData?.fullName}\nEmail: ${applicantData?.email}\nPhone: ${applicantData?.phone_number || applicantData?.phone || 'N/A'}\n\nPosition Applied: ${applicantData?.position || 'Software Developer'}\nExperience: ${applicantData?.experience || '2-3 years'}\nEmployment Type: ${applicantData?.employment_type || applicantData?.employmentType || 'Full-time'}\n\nGenerated on: ${formattedDate}`], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `${applicantData?.fullName || 'applicant'}_resume.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  if (!applicantData) return null

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h5" style={{ fontSize: '1.5rem' }}>
          Resume - {applicantData.fullName}
        </Typography>
        <div className="flex items-center gap-2">
          <Button
            variant="outlined"
            color="primary"
            size="large"
            startIcon={<i className="tabler-download" />}
            onClick={handleDownloadResume}
            className="font-bold"
            sx={{ fontSize: '1rem', padding: '8px 16px' }}
          >
            Download Resume
          </Button>
          <IconButton onClick={onClose}>
            <i className="tabler-x" />
          </IconButton>
        </div>
      </DialogTitle>

      <DialogContent>
        <Box sx={{
          minHeight: '600px',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          padding: '20px',
          backgroundColor: '#fafafa'
        }}>
          {/* Resume Content */}
          <div className="resume-content">
            <div className="text-center mb-6">
              <Typography variant="h4" className="font-bold mb-2" style={{ fontSize: '2rem' }}>
                {applicantData.fullName}
              </Typography>
              <Typography variant="h6" color="text.secondary" style={{ fontSize: '1.2rem' }}>
                {applicantData.email} | {applicantData.phone_number || applicantData.phone || '+****************'}
              </Typography>
              <Typography variant="body1" color="text.secondary" style={{ fontSize: '1rem' }}>
                {applicantData.address || 'Address not provided'}
              </Typography>
            </div>

            <div className="mb-6">
              <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                OBJECTIVE
              </Typography>
              <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                {applicantData.work_reason || 'Seeking a challenging position where I can utilize my skills and experience to contribute to the company\'s success while growing professionally.'}
              </Typography>
            </div>

            <div className="mb-6">
              <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                EXPERIENCE
              </Typography>
              <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                <strong>Experience Level:</strong> {applicantData.experience || '2-3 years'}<br />
                <strong>Position Applied:</strong> {applicantData.position || 'Software Developer'}<br />
                <strong>Employment Type Preference:</strong> {applicantData.employment_type || applicantData.employmentType || 'Full-time'}
              </Typography>
            </div>

            <div className="mb-6">
              <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                SKILLS & QUALIFICATIONS
              </Typography>
              <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                {applicantData.specific_driving_role && (
                  <>
                    <strong>Driving Role:</strong> {applicantData.specific_driving_role}<br />
                  </>
                )}
                {applicantData.specific_non_driving_role && (
                  <>
                    <strong>Non-Driving Role:</strong> {applicantData.specific_non_driving_role}<br />
                  </>
                )}
                {applicantData.commercial_license && (
                  <>
                    <strong>Commercial License:</strong> {applicantData.commercial_license}<br />
                  </>
                )}
                {applicantData.other_job && (
                  <>
                    <strong>Other Experience:</strong> {applicantData.other_job}<br />
                  </>
                )}
              </Typography>
            </div>

            <div className="mb-6">
              <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                AVAILABILITY
              </Typography>
              <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                <strong>Preferred Start Date:</strong> {applicantData.preferred_start_date || 'Flexible'}<br />
                <strong>Willing to Relocate:</strong> {applicantData.relocate || 'Not specified'}
              </Typography>
            </div>

            <div className="mb-6">
              <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                REFERENCES
              </Typography>
              <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                <strong>Primary Reference:</strong> {applicantData.reference || 'Available upon request'}<br />
                {applicantData.other_reference && (
                  <>
                    <strong>Additional Reference:</strong> {applicantData.other_reference}<br />
                  </>
                )}
              </Typography>
            </div>

            <div className="text-center mt-8">
              <Typography variant="body2" color="text.secondary" style={{ fontSize: '0.9rem' }}>
                Resume generated on {(() => {
                  const today = new Date()
                  return `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`
                })()}
              </Typography>
            </div>
          </div>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default ResumeViewerModal
