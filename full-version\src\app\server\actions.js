/**
 * ! The server actions below are used to fetch the static data from the fake-db. If you're using an ORM
 * ! (Object-Relational Mapping) or a database, you can swap the code below with your own database queries.
 */
'use server'

// Third-party Imports
import { getServerSession } from 'next-auth'

// Data Imports
import { db as eCommerceData } from '@/fake-db/apps/ecommerce'
import { db as userData } from '@/fake-db/apps/userList'
import { db as permissionData } from '@/fake-db/apps/permissions'
import { db as profileData } from '@/fake-db/pages/userProfile'

// Auth Imports
import { authOptions } from '@/libs/auth'

export const getEcommerceData = async () => {
  return eCommerceData
}

export const getUserData = async () => {
  return userData
}

export const getPermissionsData = async () => {
  return permissionData
}

export const getProfileData = async () => {
  const session = await getServerSession(authOptions)

  if (!session) {
    throw new Error('Unauthorized: No session found');
  }

  const userProfileData = {
    profileHeader: {
      fullName: session.user.name || 'Admin User',
      designation: 'Administrator',
      designationIcon: 'tabler-crown',
      location: 'Regina, SK, Canada',
      joiningDate: 'Joined Today',
      profileImg: '/images/avatars/1.png',
      coverImg: '/images/pages/profile-banner.png'
    },
    users: {
      profile: {
        about: [
          { property: 'Full Name', value: session.user.name || 'Admin User', icon: 'tabler-user' },
          { property: 'Status', value: session.user.isVerified ? 'Verified' : 'Pending', icon: 'tabler-check' },
          { property: 'Role', value: 'Administrator', icon: 'tabler-crown' },
          { property: 'Country', value: 'Canada', icon: 'tabler-flag' },
          { property: 'Language', value: 'English', icon: 'tabler-language' }
        ],
        contacts: [
          { property: 'Contact', value: '+****************', icon: 'tabler-phone-call' },
          { property: 'Email', value: session.user.email || '<EMAIL>', icon: 'tabler-mail' },
          { property: 'Company', value: 'CAM Transport Ltd.', icon: 'tabler-building' }
        ],
        teams: [
          { property: 'Transport Management', value: '(Admin Access)' },
          { property: 'System Administration', value: '(Full Access)' }
        ],
        overview: [
          { property: 'Task Compiled', value: '13.5k', icon: 'tabler-check' },
          { property: 'Connections', value: '897', icon: 'tabler-users' },
          { property: 'Projects Compiled', value: '146', icon: 'tabler-layout-grid' }
        ],
        connections: [
          {
            isFriend: false,
            connections: '45',
            name: 'Cecilia Payne',
            avatar: '/images/avatars/2.png'
          }
        ],
        teamsTech: [
          {
            members: 72,
            ChipColor: 'error',
            chipText: 'Developer',
            title: 'React Developers',
            avatar: '/images/icons/project-icons/react-label.png'
          }
        ],
        projectTable: [
          {
            id: 1,
            title: 'CAM Transport System',
            subtitle: 'Transport Management',
            leader: 'Admin',
            avatar: '/images/avatars/1.png',
            avatarGroup: ['/images/avatars/1.png'],
            status: 'Active',
            chipColor: 'success'
          }
        ]
      }
    }
  }

  return userProfileData
}

export const checkUsersExistInDB = async () => {
  // We are checking user data from the fake-db.
  // If you are using a real database, you would query it here.
  const users = userData || []

  return users.length > 0
}

export const fetchUrgentInquiriesFromServer = async () => {
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8090';
  try {
    const response = await fetch(`${API_BASE_URL}/urgent/get-urgents`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Backend API returned an error:', {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
        responseBody: errorText,
      });
      throw new Error(`Failed to fetch urgent inquiries: Backend responded with status ${response.status}`);
    }

    const responseText = await response.text();
    try {
      return JSON.parse(responseText);
    } catch (e) {
      console.error('❌ Failed to parse JSON response from backend. Raw response:', responseText);
      throw new Error('Backend did not return valid JSON.');
    }
  } catch (error) {
    console.error('❌ Error fetching urgent inquiries from server:', error);
    throw error;
  }
}
