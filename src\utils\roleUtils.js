/**
 * Role-based access control utilities for frontend
 */

// Define role hierarchy
const ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin'
};

const ROLE_HIERARCHY = {
  [ROLES.SUPER_ADMIN]: 2,
  [ROLES.ADMIN]: 1
};

/**
 * Check if user has required role
 * @param {string} userRole - Current user's role
 * @param {string|array} requiredRoles - Required role(s)
 * @returns {boolean}
 */
export const hasRole = (userRole, requiredRoles) => {
  if (!userRole) return false;
  
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  return roles.includes(userRole);
};

/**
 * Check if user has minimum role level
 * @param {string} userRole - Current user's role
 * @param {string} minimumRole - Minimum required role
 * @returns {boolean}
 */
export const hasMinimumRole = (userRole, minimumRole) => {
  if (!userRole || !minimumRole) return false;
  
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[minimumRole] || 0;
  
  return userLevel >= requiredLevel;
};

/**
 * Check if user is super admin
 * @param {string} userRole - Current user's role
 * @returns {boolean}
 */
export const isSuperAdmin = (userRole) => {
  return userRole === ROLES.SUPER_ADMIN;
};

/**
 * Check if user is admin (any level)
 * @param {string} userRole - Current user's role
 * @returns {boolean}
 */
export const isAdmin = (userRole) => {
  return hasRole(userRole, [ROLES.ADMIN, ROLES.SUPER_ADMIN]);
};

/**
 * Get user role display name
 * @param {string} role - User role
 * @returns {string}
 */
export const getRoleDisplayName = (role) => {
  switch (role) {
    case ROLES.SUPER_ADMIN:
      return 'Super Admin';
    case ROLES.ADMIN:
      return 'Admin';
    default:
      return 'Unknown';
  }
};

/**
 * Get available roles for user management
 * @param {string} currentUserRole - Current user's role
 * @returns {array}
 */
export const getAvailableRoles = (currentUserRole) => {
  const roles = [
    { value: ROLES.ADMIN, label: 'Admin' }
  ];
  
  // Only super admin can assign super admin role
  if (isSuperAdmin(currentUserRole)) {
    roles.push({ value: ROLES.SUPER_ADMIN, label: 'Super Admin' });
  }
  
  return roles;
};

/**
 * Check if current user can manage target user
 * @param {string} currentUserRole - Current user's role
 * @param {string} targetUserRole - Target user's role
 * @returns {boolean}
 */
export const canManageUser = (currentUserRole, targetUserRole) => {
  // Super admin can manage anyone
  if (isSuperAdmin(currentUserRole)) {
    return true;
  }
  
  // Admin cannot manage super admin
  if (targetUserRole === ROLES.SUPER_ADMIN) {
    return false;
  }
  
  // Admin can manage other admins
  return currentUserRole === ROLES.ADMIN && targetUserRole === ROLES.ADMIN;
};

/**
 * Get role-based navigation items
 * @param {string} userRole - Current user's role
 * @returns {array}
 */
export const getRoleBasedNavigation = (userRole) => {
  const navigation = [];
  
  // All authenticated users get basic navigation
  navigation.push(
    { label: 'Dashboard', href: '/', icon: 'tabler-smart-home' },
    { label: 'Security', href: '/pages/user-profile/security', icon: 'tabler-lock' }
  );
  
  // Super admin gets user management
  if (isSuperAdmin(userRole)) {
    navigation.push({
      label: 'User Management',
      href: '/pages/user-management',
      icon: 'tabler-users'
    });
  }
  
  return navigation;
};

export { ROLES };
