const mongoose = require('mongoose');
const Login = require('./model/Login');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function testUserRoles() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cam_transport');
        console.log('✅ Connected to MongoDB');

        // Check existing users
        const existingUsers = await Login.find({}).select('username email role isActive');
        console.log('\n📋 Existing users:');
        existingUsers.forEach(user => {
            console.log(`- ${user.username} (${user.email}) - Role: ${user.role} - Active: ${user.isActive}`);
        });

        // Create test super admin if none exists
        const superAdminCount = await Login.countDocuments({ role: 'super_admin', isActive: true });
        if (superAdminCount === 0) {
            console.log('\n🔧 Creating test super admin...');
            
            const hashedPassword = await bcrypt.hash('admin123', 10);
            const superAdmin = new Login({
                username: 'superadmin',
                email: '<EMAIL>',
                password: hashedPassword,
                adminId: 'SUPER_001',
                role: 'super_admin',
                isActive: true,
                isVerified: true,
                company: 'CAM Transport ltd.'
            });

            await superAdmin.save();
            console.log('✅ Super admin created: <EMAIL> / admin123');
        }

        // Create test normal user if none exists
        const normalUserCount = await Login.countDocuments({ role: 'normal_user', isActive: true });
        if (normalUserCount === 0) {
            console.log('\n🔧 Creating test normal user...');
            
            const hashedPassword = await bcrypt.hash('user123', 10);
            const normalUser = new Login({
                username: 'normaluser',
                email: '<EMAIL>',
                password: hashedPassword,
                adminId: 'USER_001',
                role: 'normal_user',
                isActive: true,
                isVerified: true,
                company: 'CAM Transport ltd.'
            });

            await normalUser.save();
            console.log('✅ Normal user created: <EMAIL> / user123');
        }

        // Test permissions
        console.log('\n🧪 Testing permissions...');
        
        const superAdmin = await Login.findOne({ role: 'super_admin' });
        const normalUser = await Login.findOne({ role: 'normal_user' });

        if (superAdmin) {
            console.log(`\n👑 Super Admin (${superAdmin.username}) permissions:`);
            console.log(`- Can manage users: ${superAdmin.canManageUsers()}`);
            console.log(`- Can view users: ${superAdmin.hasPermission('canViewUsers')}`);
            console.log(`- Can create users: ${superAdmin.hasPermission('canCreateUsers')}`);
            console.log(`- Can edit users: ${superAdmin.hasPermission('canEditUsers')}`);
            console.log(`- Can delete users: ${superAdmin.hasPermission('canDeleteUsers')}`);
            console.log(`- Can manage roles: ${superAdmin.hasPermission('canManageRoles')}`);
        }

        if (normalUser) {
            console.log(`\n👤 Normal User (${normalUser.username}) permissions:`);
            console.log(`- Can manage users: ${normalUser.canManageUsers()}`);
            console.log(`- Can view users: ${normalUser.hasPermission('canViewUsers')}`);
            console.log(`- Can create users: ${normalUser.hasPermission('canCreateUsers')}`);
            console.log(`- Can edit users: ${normalUser.hasPermission('canEditUsers')}`);
            console.log(`- Can delete users: ${normalUser.hasPermission('canDeleteUsers')}`);
            console.log(`- Can manage roles: ${normalUser.hasPermission('canManageRoles')}`);
        }

        console.log('\n✅ User role testing completed!');
        console.log('\n📝 Test accounts:');
        console.log('Super Admin: <EMAIL> / admin123');
        console.log('Normal User: <EMAIL> / user123');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from MongoDB');
    }
}

testUserRoles();
