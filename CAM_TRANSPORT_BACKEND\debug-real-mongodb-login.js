const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config();

async function debugRealMongoLogin() {
    try {
        // Connect to the real MongoDB database
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log('🔗 Connected to real MongoDB database');
        
        // Get all collections in the database
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('\n📊 Collections in database:');
        collections.forEach(col => console.log(`- ${col.name}`));
        
        // Check the users collection (might be named differently)
        const possibleUserCollections = ['users', 'logins', 'admins', 'accounts'];
        
        for (const collectionName of possibleUserCollections) {
            try {
                const collection = mongoose.connection.db.collection(collectionName);
                const count = await collection.countDocuments();
                
                if (count > 0) {
                    console.log(`\n📋 Found ${count} documents in '${collectionName}' collection`);
                    
                    // Get all users from this collection
                    const users = await collection.find({}).toArray();
                    
                    console.log('\n👥 Users in collection:');
                    users.forEach((user, index) => {
                        console.log(`${index + 1}. ID: ${user._id}`);
                        console.log(`   Username: ${user.username || 'N/A'}`);
                        console.log(`   Email: ${user.email || 'N/A'}`);
                        console.log(`   Password: ${user.password ? (user.password.substring(0, 20) + '...') : 'N/A'}`);
                        console.log(`   Role: ${user.role || 'N/A'}`);
                        console.log(`   Active: ${user.isActive !== undefined ? user.isActive : 'N/A'}`);
                        console.log(`   Verified: ${user.isVerified !== undefined ? user.isVerified : 'N/A'}`);
                        console.log('   ---');
                    });
                    
                    // Look for the specific user
                    const targetUser = users.find(user => 
                        user.email === '<EMAIL>' || 
                        user.username === 'dhruv'
                    );
                    
                    if (targetUser) {
                        console.log('\n🎯 Found target user:');
                        console.log('User data:', JSON.stringify(targetUser, null, 2));
                        
                        // Test password verification
                        console.log('\n🔍 Testing password verification...');
                        const testPassword = 'dhruv@123';
                        
                        let isPasswordValid = false;
                        
                        if (targetUser.password) {
                            if (targetUser.password.startsWith('$2b$')) {
                                // Hashed password
                                isPasswordValid = await bcrypt.compare(testPassword, targetUser.password);
                                console.log(`🔍 bcrypt.compare('${testPassword}', hashed) = ${isPasswordValid}`);
                            } else {
                                // Plain text password
                                isPasswordValid = targetUser.password === testPassword;
                                console.log(`🔍 Plain text comparison: ${isPasswordValid}`);
                            }
                        }
                        
                        if (!isPasswordValid) {
                            console.log('\n🔧 Password does not match. Updating user...');
                            
                            // Hash the correct password
                            const hashedPassword = await bcrypt.hash(testPassword, 10);
                            
                            // Update the user in the database
                            await collection.updateOne(
                                { _id: targetUser._id },
                                {
                                    $set: {
                                        password: hashedPassword,
                                        email: '<EMAIL>',
                                        username: 'dhruv',
                                        isActive: true,
                                        isVerified: true,
                                        mfaEnabled: false
                                    }
                                }
                            );
                            
                            console.log('✅ User updated with correct credentials');
                        } else {
                            console.log('✅ Password already matches!');
                        }
                        
                    } else {
                        console.log('\n❌ Target user not found. Creating new user...');
                        
                        // Create the user with correct credentials
                        const hashedPassword = await bcrypt.hash('dhruv@123', 10);
                        
                        const newUser = {
                            username: 'dhruv',
                            email: '<EMAIL>',
                            password: hashedPassword,
                            role: 'admin',
                            isActive: true,
                            isVerified: true,
                            mfaEnabled: false,
                            ipAddress: '127.0.0.1',
                            location: 'Local',
                            lastLoginDate: new Date(),
                            adminId: `ADMIN_${Date.now()}`,
                            createdAt: new Date(),
                            updatedAt: new Date()
                        };
                        
                        await collection.insertOne(newUser);
                        console.log('✅ New user created successfully');
                    }
                }
            } catch (error) {
                // Collection doesn't exist, continue
                continue;
            }
        }
        
        // Test the login API
        console.log('\n🌐 Testing login API with correct credentials...');
        
        const axios = require('axios');
        
        try {
            const response = await axios.post('http://localhost:8090/login', {
                username: 'dhruv',
                email: '<EMAIL>',
                password: 'dhruv@123'
            }, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });
            
            console.log('✅ Login API Success!');
            console.log('Response:', JSON.stringify(response.data, null, 2));
            
        } catch (error) {
            console.log('❌ Login API Error:');
            if (error.response) {
                console.log('Status:', error.response.status);
                console.log('Data:', JSON.stringify(error.response.data, null, 2));
            } else {
                console.log('Error:', error.message);
            }
        }
        
    } catch (error) {
        console.error('❌ Script error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n🔌 Database connection closed');
    }
}

// Run the script
if (require.main === module) {
    debugRealMongoLogin().then(() => {
        console.log('\n✅ Debug completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Debug failed:', error);
        process.exit(1);
    });
}

module.exports = { debugRealMongoLogin };
