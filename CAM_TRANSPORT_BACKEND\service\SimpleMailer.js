const nodemailer = require('nodemailer');
const ejs = require('ejs');
const path = require('path');
require('dotenv').config();

// Create transporter with simple SMTP authentication
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
        user: process.env.USER || process.env.SMTP_FROM,
        pass: process.env.PASS // App password for Gmail
    }
});

const importanceHeaders = {
    high: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High',
        'Importance': 'high'
    },
    normal: {
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal',
        'Importance': 'normal'
    },
    low: {
        'X-Priority': '5',
        'X-MSMail-Priority': 'Low',
        'Importance': 'low'
    }
};

/**
 * Send an email using simple SMTP authentication
 * @param {Object} options
 * @param {string} options.to - Recipient email address
 * @param {string} options.subject - Email subject
 * @param {string} [options.text] - Plain text body
 * @param {string} [options.html] - HTML body (used if no template)
 * @param {string} [options.template] - EJS template filename (in /email_templates)
 * @param {Object} [options.templateData] - Data for EJS template
 * @param {Array} [options.attachments] - Attachments
 * @param {string} [options.importance] - Email importance
 */
async function sendEmail({ to, subject, text, html, template, templateData = {}, attachments = [], importance = 'normal', headers = {} }) {
    let finalHtml = html;

    if (template) {
        const templatePath = path.join(__dirname, '../email_templates', template);
        try {
            finalHtml = await ejs.renderFile(templatePath, templateData);
        } catch (templateError) {
            console.error('❌ Template rendering error:', templateError);
            // Fallback to simple HTML
            finalHtml = `
                <h2>CAM Transport - ${subject}</h2>
                <p>Your verification code is: <strong>${templateData.otp}</strong></p>
                <p>This code will expire in 10 minutes.</p>
                <p>If you didn't request this, please ignore this email.</p>
            `;
        }
    }

    try {
        const mailOptions = {
            from: `CAM Transport <${process.env.USER || process.env.SMTP_FROM}>`,
            to,
            subject,
            text,
            html: finalHtml,
            replyTo: process.env.USER || process.env.SMTP_FROM,
            headers: {
                ...(importanceHeaders[importance] || importanceHeaders.normal),
                ...headers
            },
            ...(attachments?.length > 0 ? { attachments } : {})
        };

        console.log('📧 Sending email to:', to);
        console.log('📧 Using SMTP:', process.env.SMTP_HOST);
        console.log('📧 From:', mailOptions.from);

        const result = await transporter.sendMail(mailOptions);

        console.log('✅ Email sent successfully to:', to);
        console.log('📧 Message ID:', result.messageId);
        return result;
    } catch (error) {
        console.error('❌ Error sending email:', error);
        
        // Provide specific error analysis
        if (error.code === 'EAUTH') {
            console.error('💡 Authentication failed. Check your email and app password.');
        } else if (error.code === 'ECONNECTION') {
            console.error('💡 Connection failed. Check your SMTP settings.');
        } else if (error.responseCode === 535) {
            console.error('💡 Invalid credentials. Make sure you\'re using an app password for Gmail.');
        }
        
        throw error;
    }
}

// Test the email service
async function testEmailService() {
    try {
        console.log('🧪 Testing Simple Email Service...');
        
        const result = await sendEmail({
            to: '<EMAIL>',
            subject: 'CAM Transport - Email Service Test',
            html: `
                <h2>Email Service Test</h2>
                <p>This is a test email from the CAM Transport system.</p>
                <p>If you receive this, the email service is working correctly!</p>
                <p>Time: ${new Date().toISOString()}</p>
            `,
            importance: 'high'
        });
        
        console.log('✅ Test email sent successfully!');
        return true;
    } catch (error) {
        console.error('❌ Test email failed:', error.message);
        return false;
    }
}

module.exports = { sendEmail, testEmailService };
