// MUI Imports
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'

const TrustedDevicesPage = () => {
  // Dummy trusted devices data
  const trustedDevices = [
    {
      id: 1,
      name: 'MacBook Pro',
      type: 'Desktop',
      browser: 'Chrome 118.0',
      location: 'New York, NY',
      lastUsed: '2 hours ago',
      current: true,
      icon: 'tabler-device-laptop'
    },
    {
      id: 2,
      name: 'iPhone 14 Pro',
      type: 'Mobile',
      browser: 'Safari 16.6',
      location: 'New York, NY',
      lastUsed: '1 day ago',
      current: false,
      icon: 'tabler-device-mobile'
    },
    {
      id: 3,
      name: 'Windows PC',
      type: 'Desktop',
      browser: 'Edge 118.0',
      location: 'Los Angeles, CA',
      lastUsed: '3 days ago',
      current: false,
      icon: 'tabler-device-desktop'
    },
    {
      id: 4,
      name: 'iPad Air',
      type: 'Tablet',
      browser: 'Safari 16.6',
      location: 'San Francisco, CA',
      lastUsed: '1 week ago',
      current: false,
      icon: 'tabler-device-tablet'
    }
  ]

  return (
    <Grid container spacing={6}>
      <Grid size={{ xs: 12 }}>
        <Card>
          <CardHeader 
            title="Trusted Devices" 
            subheader="Manage devices that don't require MFA for 30 days"
          />
          <CardContent>
            <Alert severity="info" className="mb-6">
              This is a dummy trusted devices page. Device data is for demonstration only.
            </Alert>
            
            <div className="space-y-6">
              <div>
                <Typography variant="h6" className="mb-2">
                  Your Trusted Devices
                </Typography>
                <Typography variant="body2" color="text.secondary" className="mb-4">
                  These devices won't require MFA verification for 30 days after being marked as trusted.
                </Typography>
                
                <div className="space-y-4">
                  {trustedDevices.map((device) => (
                    <Card key={device.id} variant="outlined" className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex-shrink-0">
                            <i className={`${device.icon} text-3xl text-primary`} />
                          </div>
                          <div className="flex-grow">
                            <div className="flex items-center space-x-2 mb-1">
                              <Typography variant="subtitle1" className="font-medium">
                                {device.name}
                              </Typography>
                              {device.current && (
                                <Chip 
                                  label="Current Device" 
                                  size="small" 
                                  color="success" 
                                  variant="filled"
                                />
                              )}
                            </div>
                            <Typography variant="body2" color="text.secondary">
                              {device.browser} • {device.type}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {device.location} • Last used {device.lastUsed}
                            </Typography>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <IconButton size="small" color="primary">
                            <i className="tabler-edit" />
                          </IconButton>
                          <IconButton size="small" color="error" disabled={device.current}>
                            <i className="tabler-trash" />
                          </IconButton>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
              
              <div>
                <Typography variant="h6" className="mb-2">
                  Device Trust Settings
                </Typography>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <Typography variant="subtitle2">Trust Period</Typography>
                      <Typography variant="body2" color="text.secondary">
                        How long devices remain trusted
                      </Typography>
                    </div>
                    <Chip label="30 days" variant="outlined" />
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <Typography variant="subtitle2">Auto-Remove Inactive</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Remove devices not used for 90 days
                      </Typography>
                    </div>
                    <Chip label="Enabled" color="success" variant="outlined" />
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <Typography variant="subtitle2">Location Verification</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Require MFA if location changes significantly
                      </Typography>
                    </div>
                    <Chip label="Enabled" color="success" variant="outlined" />
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <Button variant="contained" color="primary">
                  Trust This Device
                </Button>
                <Button variant="outlined" color="warning">
                  Remove All Devices
                </Button>
                <Button variant="outlined" color="secondary">
                  Refresh List
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default TrustedDevicesPage
