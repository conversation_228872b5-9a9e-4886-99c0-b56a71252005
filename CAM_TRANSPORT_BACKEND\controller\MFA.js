const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const crypto = require('crypto');
const mongoose = require('mongoose');
const Login = require('../model/Login');

/**
 * Helper function to validate ObjectId
 */
const isValidObjectId = (id) => {
    return mongoose.Types.ObjectId.isValid(id);
};

/**
 * Helper function to find user by ID or create demo user
 */
const findOrCreateDemoUser = async (userId) => {
    // If it's a valid ObjectId, try to find the user
    if (isValidObjectId(userId)) {
        return await Login.findById(userId);
    }

    // Handle numeric user IDs from NextAuth (like user ID 1)
    if (!isNaN(userId)) {
        let user = await Login.findOne({ adminId: userId.toString() });

        if (!user) {
            // Create a user record for the NextAuth user with proper credentials
            user = new Login({
                username: 'admin', // Match the frontend user
                email: '<EMAIL>', // Match the frontend user
                password: 'admin', // Match the frontend user
                adminId: userId.toString(),
                isVerified: true,
                role: 'admin'
            });
            await user.save();
        }

        // Auto-setup MFA for admin user (ID: 1) for testing purposes
        if (userId.toString() === '1' && !user.mfaEnabled) {
            console.log('🔧 Auto-setting up MFA for admin user for testing...');

            // Generate a fixed secret for testing (so we can predict the OTP)
            const testSecret = 'JBSWY3DPEHPK3PXP'; // This generates predictable OTPs for testing

            // Set the master secret
            user.mfaSecret = testSecret;

            // Create a test device (for tracking only, no individual secret)
            const deviceId = crypto.randomBytes(16).toString('hex');
            const testDevice = {
                deviceId,
                deviceName: 'Test Authenticator',
                isActive: true,
                registeredAt: new Date(),
                lastUsedAt: new Date(),
                deviceInfo: {
                    userAgent: 'Test Device',
                    ipAddress: '127.0.0.1',
                    platform: 'Test'
                }
            };

            // Generate backup codes
            const backupCodes = [];
            for (let i = 0; i < 10; i++) {
                backupCodes.push({
                    code: crypto.randomBytes(4).toString('hex').toUpperCase(),
                    used: false,
                    usedAt: null
                });
            }

            // Enable MFA with test setup
            user.mfaEnabled = true;
            user.mfaSetupAt = new Date();
            user.mfaDevices = [testDevice];
            user.backupCodes = backupCodes;

            await user.save();

            console.log('✅ Auto-setup MFA completed for admin user');
            console.log('🔑 Master secret:', testSecret);
            console.log('🔐 Use this secret in your authenticator app: JBSWY3DPEHPK3PXP');
            console.log('📱 This same secret can be used on multiple devices!');
        }

        return user;
    }

    // For demo purposes, if it's not a valid ObjectId, create/find a demo user
    if (userId.startsWith('demo-')) {
        let demoUser = await Login.findOne({ adminId: userId });

        if (!demoUser) {
            // Create a demo user
            demoUser = new Login({
                username: 'Demo User',
                email: '<EMAIL>',
                password: 'demo-password-hash', // This would be hashed in real implementation
                adminId: userId,
                isVerified: true,
                role: 'demo'
            });
            await demoUser.save();
        }

        return demoUser;
    }

    return null;
};

/**
 * Generate MFA device setup (secret and QR code)
 */
const generateMFASetup = async (req, res) => {
    try {
        const { userId } = req.params;
        const { deviceName = 'Authenticator App' } = req.body;

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check device limit
        const maxDevices = user.mfaSettings?.maxDevices || 5;
        const activeDevices = user.mfaDevices?.filter(device => device.isActive).length || 0;

        if (activeDevices >= maxDevices) {
            return res.status(400).json({
                success: false,
                message: `Maximum number of devices (${maxDevices}) reached. Please remove a device first.`
            });
        }

        // Generate a unique device ID (ensure it's unique within this user's devices)
        let deviceId;
        let attempts = 0;
        const maxAttempts = 10;

        do {
            deviceId = crypto.randomBytes(16).toString('hex');
            attempts++;

            // Check if this deviceId already exists for this user
            const existingDevice = user.mfaDevices?.find(device => device.deviceId === deviceId);
            if (!existingDevice) {
                break; // Found a unique deviceId
            }

            if (attempts >= maxAttempts) {
                return res.status(500).json({
                    success: false,
                    message: 'Unable to generate unique device ID. Please try again.'
                });
            }
        } while (attempts < maxAttempts);

        // Use existing master secret or generate a new one
        let secret;
        if (user.mfaSecret) {
            // User already has a master secret, use it
            secret = {
                base32: user.mfaSecret,
                otpauth_url: speakeasy.otpauthURL({
                    secret: user.mfaSecret,
                    label: `CAM Transport (${user.email})`,
                    issuer: 'CAM Transport',
                    encoding: 'base32'
                })
            };
        } else {
            // Generate a new master secret for this user
            secret = speakeasy.generateSecret({
                name: `CAM Transport (${user.email})`,
                issuer: 'CAM Transport',
                length: 32
            });

            // Store the master secret
            user.mfaSecret = secret.base32;
        }

        // Get device info from request
        const deviceInfo = {
            userAgent: req.headers['user-agent'] || 'Unknown',
            ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
            platform: req.headers['sec-ch-ua-platform'] || 'Unknown'
        };

        // Store device info temporarily (not active until verified)
        // Note: No individual secret stored - all devices use the master secret
        const tempDevice = {
            deviceId,
            deviceName,
            isActive: false,
            registeredAt: new Date(),
            deviceInfo
        };

        // Store in a temporary field or session (for this demo, we'll add it but mark as inactive)
        if (!user.mfaDevices) {
            user.mfaDevices = [];
        }
        user.mfaDevices.push(tempDevice);
        await user.save();

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

        res.status(200).json({
            success: true,
            message: 'MFA device setup generated successfully',
            data: {
                deviceId,
                deviceName,
                secret: secret.base32,
                qrCode: qrCodeUrl,
                manualEntryKey: secret.base32,
                issuer: 'CAM Transport',
                accountName: user.email
            }
        });

    } catch (error) {
        console.error('Error generating MFA setup:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Verify MFA device setup and activate device
 */
const verifyMFASetup = async (req, res) => {
    try {
        const { userId } = req.params;
        const { token, deviceId } = req.body;

        if (!token) {
            return res.status(400).json({
                success: false,
                message: 'TOTP token is required'
            });
        }

        if (!deviceId) {
            return res.status(400).json({
                success: false,
                message: 'Device ID is required'
            });
        }

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Find the device
        const device = user.mfaDevices?.find(d => d.deviceId === deviceId);
        if (!device) {
            return res.status(400).json({
                success: false,
                message: 'Device setup not found. Please generate setup first.'
            });
        }

        // Verify the token with the user's master secret
        if (!user.mfaSecret) {
            return res.status(400).json({
                success: false,
                message: 'MFA secret not found. Please restart the setup process.'
            });
        }

        const verified = speakeasy.totp.verify({
            secret: user.mfaSecret,
            encoding: 'base32',
            token: token,
            window: 1 // Allow 1 window tolerance for better UX
        });

        if (!verified) {
            return res.status(400).json({
                success: false,
                message: 'Invalid TOTP token. Please try again.'
            });
        }

        // Activate the device
        device.isActive = true;
        device.lastUsedAt = new Date();

        // If this is the first device, enable MFA and generate backup codes
        let backupCodes = [];
        if (!user.mfaEnabled) {
            user.mfaEnabled = true;
            user.mfaSetupAt = new Date();

            // Generate backup codes
            for (let i = 0; i < 10; i++) {
                backupCodes.push({
                    code: crypto.randomBytes(4).toString('hex').toUpperCase(),
                    used: false,
                    usedAt: null
                });
            }
            user.backupCodes = backupCodes;
        }

        await user.save();

        res.status(200).json({
            success: true,
            message: 'MFA device activated successfully',
            data: {
                deviceId: device.deviceId,
                deviceName: device.deviceName,
                backupCodes: backupCodes.map(bc => bc.code),
                mfaEnabled: user.mfaEnabled,
                isFirstDevice: backupCodes.length > 0
            }
        });

    } catch (error) {
        console.error('Error verifying MFA setup:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Verify MFA token during login
 */
const verifyMFAToken = async (req, res) => {
    try {
        const { userId } = req.params;
        const { token, isBackupCode } = req.body;

        console.log('🔐 MFA Token verification requested:', {
            userId,
            token: token ? `${token.substring(0, 2)}****` : 'null',
            tokenLength: token ? token.length : 0,
            isBackupCode
        });

        if (!token) {
            console.log('❌ No token provided');
            return res.status(400).json({
                success: false,
                message: 'Token is required'
            });
        }

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            console.log('❌ User not found for ID:', userId);
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('👤 User found for MFA verification:', {
            id: user._id,
            adminId: user.adminId,
            username: user.username,
            email: user.email,
            mfaEnabled: user.mfaEnabled,
            activeDevices: user.mfaDevices?.filter(d => d.isActive).length || 0
        });

        if (!user.mfaEnabled) {
            console.log('❌ MFA not enabled for user');
            return res.status(400).json({
                success: false,
                message: 'MFA is not enabled for this account'
            });
        }

        let verified = false;

        if (isBackupCode) {
            // Verify backup code
            const backupCode = user.backupCodes.find(bc =>
                bc.code === token.toUpperCase() && !bc.used
            );

            if (backupCode) {
                // Mark backup code as used
                backupCode.used = true;
                backupCode.usedAt = new Date();
                await user.save();
                verified = true;
            }
        } else {
            // Verify TOTP token against the user's master secret
            console.log('🔍 Verifying TOTP token against master secret');

            if (!user.mfaSecret) {
                console.log('❌ No master MFA secret found for user');
                return res.status(400).json({
                    success: false,
                    message: 'MFA secret not found. Please set up MFA again.'
                });
            }

            console.log('🔐 Using master secret:', {
                secretLength: user.mfaSecret ? user.mfaSecret.length : 0,
                secretPreview: user.mfaSecret ? `${user.mfaSecret.substring(0, 4)}****` : 'null'
            });

            verified = speakeasy.totp.verify({
                secret: user.mfaSecret,
                encoding: 'base32',
                token: token,
                window: 1 // Allow 1 window tolerance for better UX across different devices
            });

            console.log('🔍 Master secret verification result:', {
                verified: verified,
                token: `${token.substring(0, 2)}****`,
                window: 1
            });

            if (verified) {
                console.log('✅ Token verified successfully with master secret');

                // Update the most recently used device (best guess based on user agent)
                const activeDevices = user.mfaDevices?.filter(d => d.isActive) || [];
                if (activeDevices.length > 0) {
                    // Update the first active device as a fallback
                    // In a real implementation, you might want to track which device was used
                    activeDevices[0].lastUsedAt = new Date();
                }
            } else {
                console.log('❌ Token verification failed with master secret');
            }
        }

        if (!verified) {
            console.log('❌ Final verification result: FAILED');
            return res.status(400).json({
                success: false,
                message: 'Invalid token. Please try again.'
            });
        }

        console.log('✅ Final verification result: SUCCESS');

        // Update last MFA used
        user.lastMfaUsed = new Date();
        await user.save();

        res.status(200).json({
            success: true,
            message: 'MFA verification successful',
            data: {
                verified: true,
                userId: user._id
            }
        });

    } catch (error) {
        console.error('Error verifying MFA token:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Disable MFA for a user
 */
const disableMFA = async (req, res) => {
    try {
        const { userId } = req.params;
        const { password } = req.body;

        if (!password) {
            return res.status(400).json({
                success: false,
                message: 'Password is required to disable MFA'
            });
        }

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Verify password (you'll need to implement password verification)
        // const isPasswordValid = await bcrypt.compare(password, user.password);
        // if (!isPasswordValid) {
        //     return res.status(400).json({ 
        //         success: false, 
        //         message: 'Invalid password' 
        //     });
        // }

        // Disable MFA
        user.mfaEnabled = false;
        user.mfaSecret = null; // Clear the master secret
        user.mfaDevices = []; // Clear all devices
        user.backupCodes = [];
        user.lastMfaUsed = null;
        await user.save();

        res.status(200).json({
            success: true,
            message: 'MFA disabled successfully',
            data: {
                mfaEnabled: false
            }
        });

    } catch (error) {
        console.error('Error disabling MFA:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Get MFA status for a user
 */
const getMFAStatus = async (req, res) => {
    try {
        const { userId } = req.params;
        console.log('🔍 MFA Status requested for user ID:', userId);

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            console.log('❌ User not found for ID:', userId);
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('👤 User found:', {
            id: user._id,
            adminId: user.adminId,
            username: user.username,
            email: user.email,
            mfaEnabled: user.mfaEnabled,
            activeDevices: user.mfaDevices?.filter(d => d.isActive).length || 0
        });

        const response = {
            success: true,
            data: {
                mfaEnabled: user.mfaEnabled,
                mfaSetupAt: user.mfaSetupAt,
                lastMfaUsed: user.lastMfaUsed,
                backupCodesCount: user.backupCodes ? user.backupCodes.filter(bc => !bc.used).length : 0
            }
        };

        console.log('✅ MFA Status response:', response);
        res.status(200).json(response);

    } catch (error) {
        console.error('❌ Error getting MFA status:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Generate new backup codes
 */
const generateBackupCodes = async (req, res) => {
    try {
        const { userId } = req.params;

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        if (!user.mfaEnabled) {
            return res.status(400).json({
                success: false,
                message: 'MFA must be enabled to generate backup codes'
            });
        }

        // Generate new backup codes
        const backupCodes = [];
        for (let i = 0; i < 10; i++) {
            backupCodes.push({
                code: crypto.randomBytes(4).toString('hex').toUpperCase(),
                used: false,
                usedAt: null
            });
        }

        // Replace old backup codes
        user.backupCodes = backupCodes;
        await user.save();

        res.status(200).json({
            success: true,
            message: 'New backup codes generated successfully',
            data: {
                backupCodes: backupCodes.map(bc => bc.code)
            }
        });

    } catch (error) {
        console.error('Error generating backup codes:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Get all MFA devices for a user
 */
const getMFADevices = async (req, res) => {
    try {
        const { userId } = req.params;

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const devices = user.mfaDevices?.filter(device => device.isActive).map(device => ({
            deviceId: device.deviceId,
            deviceName: device.deviceName,
            registeredAt: device.registeredAt,
            lastUsedAt: device.lastUsedAt,
            deviceInfo: {
                platform: device.deviceInfo?.platform || 'Unknown',
                userAgent: device.deviceInfo?.userAgent || 'Unknown'
            }
        })) || [];

        res.status(200).json({
            success: true,
            data: {
                devices,
                totalDevices: devices.length,
                maxDevices: user.mfaSettings?.maxDevices || 5
            }
        });

    } catch (error) {
        console.error('Error getting MFA devices:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Update device name
 */
const updateDeviceName = async (req, res) => {
    try {
        const { userId, deviceId } = req.params;
        const { deviceName } = req.body;

        if (!deviceName || deviceName.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Device name is required'
            });
        }

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Find and update the device
        const device = user.mfaDevices?.find(d => d.deviceId === deviceId && d.isActive);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: 'Device not found'
            });
        }

        device.deviceName = deviceName.trim();
        await user.save();

        res.status(200).json({
            success: true,
            message: 'Device name updated successfully',
            data: {
                deviceId: device.deviceId,
                deviceName: device.deviceName
            }
        });

    } catch (error) {
        console.error('Error updating device name:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Remove/deactivate a device
 */
const removeDevice = async (req, res) => {
    try {
        const { userId, deviceId } = req.params;

        // Find the user using helper function
        const user = await findOrCreateDemoUser(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Find the device
        const device = user.mfaDevices?.find(d => d.deviceId === deviceId && d.isActive);
        if (!device) {
            return res.status(404).json({
                success: false,
                message: 'Device not found'
            });
        }

        // Check if this is the last active device
        const activeDevices = user.mfaDevices.filter(d => d.isActive);
        if (activeDevices.length === 1) {
            return res.status(400).json({
                success: false,
                message: 'Cannot remove the last MFA device. Disable MFA instead.'
            });
        }

        // Deactivate the device
        device.isActive = false;
        await user.save();

        res.status(200).json({
            success: true,
            message: 'Device removed successfully',
            data: {
                deviceId: device.deviceId,
                deviceName: device.deviceName
            }
        });

    } catch (error) {
        console.error('Error removing device:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

module.exports = {
    generateMFASetup,
    verifyMFASetup,
    verifyMFAToken,
    disableMFA,
    getMFAStatus,
    generateBackupCodes,
    getMFADevices,
    updateDeviceName,
    removeDevice
};
