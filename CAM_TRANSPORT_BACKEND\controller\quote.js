const { sendEmail } = require('../service/Mailer');
const DeckQuote = require('../model/quote');
const { quoteValidationSchema } = require('../middleware/quote_Validator');
require('dotenv').config();


const hasMongoOperators = (obj) => {
    for (const key in obj) {
        if (key.startsWith("$")) {
            return true;
        }
        if (typeof obj[key] === "object" && obj[key] !== null) {
            if (hasMongoOperators(obj[key])) return true;
        }
    }
    return false;
};


const QuoteForm = async (req, res) => {
    try {
        if (req.body._honeypot && req.body._honeypot.length > 0) {
            return res.status(400).json({ message: "Spam detected" });
        }
        if (hasMongoOperators(req.body)) {
            return res.status(400).json({ message: "Invalid input detected" });
        }
        const { error, value } = quoteValidationSchema.validate(req.body, {
            abortEarly: false,
        });
        if (error) {
            return res.status(400).json({
                message: error.details.map((err) => err.message).join(", "),
            });
        }

        const {
            quoteType,
            contactInfo,
            shipmentDetails,
            pickupDelivery,
            specialRequirements
        } = value;

        const quote = await DeckQuote.create({
            quoteType,
            contactInfo,
            shipmentDetails,
            pickupDelivery,
            specialRequirements
        });

        // Send confirmation email to user
        await sendEmail({
            to: contactInfo.emailAddress,
            subject: "Quote Request Confirmation",
            html: `
                <h2>Thank you for your quote request!</h2>
                <p>Dear ${contactInfo.fullName},</p>
                <p>We have received your quote (${quoteType}) request successfully. Our team will review your requirements and get back to you shortly.</p>
                <h3>Quote Details:</h3>
                <ul>
                    <li>Pickup Location: ${pickupDelivery.pickupLocation.city}, ${pickupDelivery.pickupLocation.stateOrProvince}</li>
                    <li>Delivery Location: ${pickupDelivery.deliveryLocation.city}, ${pickupDelivery.deliveryLocation.stateOrProvince}</li>
                    <li>Load Type: ${shipmentDetails.loadType}</li>
                    <li>Item Description: ${shipmentDetails.itemDescription}</li>
                    <li>Weight: ${shipmentDetails.approximateWeight.value} ${shipmentDetails.approximateWeight.unit}</li>
                    <li>Dimensions: ${shipmentDetails.loadDimensions.length} x ${shipmentDetails.loadDimensions.width} x ${shipmentDetails.loadDimensions.height} ${shipmentDetails.loadDimensions.unit}</li>
                    <li>Pickup Date: ${new Date(pickupDelivery.preferredPickupDate).toLocaleDateString()}</li>
                    <li>Delivery Date: ${new Date(pickupDelivery.preferredDeliveryDate).toLocaleDateString()}</li>
                </ul>
                <p>If you have any questions, please don't hesitate to contact us.</p>
                <p>Best regards,<br>CAM Transport Team</p>
            `
        });

        // Send notification to admin
        await sendEmail({
            to: process.env.SMTP_FROM,
            subject: "New Quote Request Received",
            html: `
                <h2>New Quote Request</h2>
                <p>A new quote (${quoteType}) request has been submitted:</p>
                <h3>Customer Details:</h3>
                <ul>
                    <li>Name: ${contactInfo.fullName}</li>
                    <li>Email: ${contactInfo.emailAddress}</li>
                    <li>Phone: ${contactInfo.phoneNumber}</li>
                    <li>Company: ${contactInfo.companyName || 'Not provided'}</li>
                    <li>Preferred Contact: ${contactInfo.preferredContactMethod}</li>
                </ul>
                <h3>Shipment Details:</h3>
                <ul>
                    <li>Item Description: ${shipmentDetails.itemDescription}</li>
                    <li>Load Type: ${shipmentDetails.loadType}</li>
                    <li>Weight: ${shipmentDetails.approximateWeight.value} ${shipmentDetails.approximateWeight.unit}</li>
                    <li>Dimensions: ${shipmentDetails.loadDimensions.length} x ${shipmentDetails.loadDimensions.width} x ${shipmentDetails.loadDimensions.height} ${shipmentDetails.loadDimensions.unit}</li>
                </ul>
                <h3>Pickup & Delivery:</h3>
                <ul>
                    <li>Pickup Location: ${pickupDelivery.pickupLocation.city}, ${pickupDelivery.pickupLocation.stateOrProvince}</li>
                    <li>Delivery Location: ${pickupDelivery.deliveryLocation.city}, ${pickupDelivery.deliveryLocation.stateOrProvince}</li>
                    <li>Preferred Pickup Date: ${new Date(pickupDelivery.preferredPickupDate).toLocaleDateString()}</li>
                    <li>Preferred Delivery Date: ${new Date(pickupDelivery.preferredDeliveryDate).toLocaleDateString()}</li>
                    <li>Delivery Assistance Required: ${pickupDelivery.deliveryAssistanceRequired ? 'Yes' : 'No'}</li>
                </ul>
                <h3>Special Requirements:</h3>
                <ul>
                    <li>Requires Permits/Escorts: ${specialRequirements.requiresPermitsOrEscorts ? 'Yes' : 'No'}</li>
                    <li>Delivery Type: ${specialRequirements.deliveryType}</li>
                    <li>Special Handling Instructions: ${specialRequirements.specialHandlingInstructions || 'None'}</li>
                </ul>
            `
        });

        return res.status(201).json({
            success: true,
            message: `Quote (${quoteType}) request submitted successfully`,
            data: quote
        });
    } catch (error) {
        console.error("Error in quote submission:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error"
        });
    }
};

const GetAllQuotes = async (req, res) => {
    try {
        console.log('🔍 Fetching all quote requests...');
        
        const quotes = await DeckQuote.find({})
            .sort({ createdAt: -1 }) // Sort by newest first
            .lean();

        console.log(`✅ Found ${quotes.length} quote requests`);

        return res.status(200).json({
            success: true,
            quotes,
            count: quotes.length
        });
    } catch (error) {
        console.error("GetAllQuotes error:", error);
        return res.status(500).json({ 
            success: false,
            message: "Internal server error" 
        });
    }
};

const DeleteQuote = async (req, res) => {
    try {
        const { id } = req.params;
        
        if (!id) {
            return res.status(400).json({ 
                success: false,
                message: "Quote ID is required" 
            });
        }

        const quote = await DeckQuote.findByIdAndDelete(id);
        
        if (!quote) {
            return res.status(404).json({ 
                success: false,
                message: "Quote not found" 
            });
        }

        return res.status(200).json({ 
            success: true,
            message: "Quote deleted successfully" 
        });
    } catch (error) {
        console.error("DeleteQuote error:", error);
        return res.status(500).json({ 
            success: false,
            message: "Internal server error" 
        });
    }
};

module.exports = {
    QuoteForm,
    GetAllQuotes,
    DeleteQuote
};