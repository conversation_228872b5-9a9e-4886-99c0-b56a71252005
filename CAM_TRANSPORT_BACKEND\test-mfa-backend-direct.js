const axios = require('axios');

async function testMFABackendDirect() {
    console.log('🧪 TESTING MFA BACKEND ENDPOINTS DIRECTLY');
    console.log('==========================================\n');
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    try {
        // Step 1: Get user info
        console.log('🔐 Step 1: Getting user info...');
        const loginResponse = await axios.post('http://localhost:8090/login', credentials, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        const userId = loginResponse.data.user?.id || loginResponse.data.user?._id;
        console.log(`👤 User ID: ${userId}`);
        console.log(`📧 Email: ${loginResponse.data.user.email}`);
        console.log(`👤 Username: ${loginResponse.data.user.username}\n`);
        
        // Step 2: Test /login/mfa-verify endpoint (what NextA<PERSON> is calling)
        console.log('🔐 Step 2: Testing /login/mfa-verify endpoint (NextAuth path)...');
        try {
            const mfaLoginResponse = await axios.post('http://localhost:8090/login/mfa-verify', {
                username: credentials.username,
                email: credentials.email,
                password: 'verified',
                mfaToken: '123456', // Sample token
                step: 'mfa'
            }, {
                headers: { 'Content-Type': 'application/json' }
            });
            
            console.log('✅ /login/mfa-verify response:', JSON.stringify(mfaLoginResponse.data, null, 2));
        } catch (error) {
            console.log('❌ /login/mfa-verify failed:');
            console.log('   Status:', error.response?.status);
            console.log('   Error:', JSON.stringify(error.response?.data, null, 2));
        }
        
        console.log('\n🔐 Step 3: Testing /mfa/verify/:userId endpoint (direct MFA path)...');
        try {
            const mfaDirectResponse = await axios.post(`http://localhost:8090/mfa/verify/${userId}`, {
                token: '123456', // Sample token
                isBackupCode: false
            }, {
                headers: { 'Content-Type': 'application/json' }
            });
            
            console.log('✅ /mfa/verify/:userId response:', JSON.stringify(mfaDirectResponse.data, null, 2));
        } catch (error) {
            console.log('❌ /mfa/verify/:userId failed:');
            console.log('   Status:', error.response?.status);
            console.log('   Error:', JSON.stringify(error.response?.data, null, 2));
        }
        
        console.log('\n🎯 ANALYSIS:');
        console.log('Both endpoints should fail with "Invalid token" for the sample token 123456');
        console.log('But they should show different error handling patterns');
        console.log('The NextAuth configuration is calling /login/mfa-verify');
        console.log('Make sure this endpoint properly handles MFA verification');
        
        console.log('\n📱 TO TEST WITH REAL TOKEN:');
        console.log('1. Get the current 6-digit code from your authenticator app');
        console.log('2. Replace "123456" in this script with the real code');
        console.log('3. Run the script again quickly (TOTP codes expire every 30 seconds)');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

testMFABackendDirect();
