'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Alert,
  Link,
  Divider,
  InputAdornment,
  IconButton
} from '@mui/material'
import { Security, Smartphone, Key, Visibility, VisibilityOff } from '@mui/icons-material'
import { verifyMFAToken } from '@/services/mfaApi'

const MFAVerification = ({ open, onClose, userId, onSuccess, userEmail }) => {
  const [token, setToken] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [useBackupCode, setUseBackupCode] = useState(false)
  const [showBackupCode, setShowBackupCode] = useState(false)

  const handleVerify = async () => {
    if (!token || (useBackupCode ? token.length < 4 : token.length !== 6)) {
      setError(useBackupCode ? 'Please enter a valid backup code' : 'Please enter a valid 6-digit code')
      return
    }

    try {
      setLoading(true)
      setError('')
      
      const result = await verifyMFAToken(userId, token, useBackupCode)
      
      if (onSuccess) {
        onSuccess(result)
      }
      
      handleClose()
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setToken('')
    setError('')
    setUseBackupCode(false)
    setShowBackupCode(false)
    onClose()
  }

  const toggleBackupCode = () => {
    setUseBackupCode(!useBackupCode)
    setToken('')
    setError('')
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Security />
          Two-Factor Authentication
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ py: 2 }}>
          <Typography variant="body1" gutterBottom>
            Please enter your authentication code to continue.
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {userEmail && `Signing in as: ${userEmail}`}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {!useBackupCode ? (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Smartphone sx={{ color: 'primary.main' }} />
                <Typography variant="h6">
                  Authenticator App
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Enter the 6-digit code from your authenticator app:
              </Typography>
              
              <TextField
                fullWidth
                label="Authentication Code"
                value={token}
                onChange={(e) => setToken(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="000000"
                inputProps={{ 
                  style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' },
                  maxLength: 6
                }}
                sx={{ mb: 3 }}
                autoFocus
              />
            </>
          ) : (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <Key sx={{ color: 'primary.main' }} />
                <Typography variant="h6">
                  Backup Code
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Enter one of your backup codes:
              </Typography>
              
              <TextField
                fullWidth
                label="Backup Code"
                type={showBackupCode ? 'text' : 'password'}
                value={token}
                onChange={(e) => setToken(e.target.value.toUpperCase().replace(/[^A-F0-9]/g, '').slice(0, 8))}
                placeholder="XXXXXXXX"
                inputProps={{ 
                  style: { textAlign: 'center', fontSize: '1.2rem', letterSpacing: '0.2rem' },
                  maxLength: 8
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowBackupCode(!showBackupCode)}
                        edge="end"
                      >
                        {showBackupCode ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 3 }}
                autoFocus
              />
              
              <Alert severity="warning" sx={{ mb: 2 }}>
                Each backup code can only be used once. Make sure to generate new backup codes after using several of them.
              </Alert>
            </>
          )}

          <Divider sx={{ my: 2 }} />
          
          <Box sx={{ textAlign: 'center' }}>
            <Link
              component="button"
              variant="body2"
              onClick={toggleBackupCode}
              sx={{ cursor: 'pointer' }}
            >
              {useBackupCode 
                ? "Use authenticator app instead" 
                : "Can't access your authenticator? Use a backup code"
              }
            </Link>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          Cancel
        </Button>
        <Button 
          variant="contained" 
          onClick={handleVerify}
          disabled={loading || !token || (useBackupCode ? token.length < 4 : token.length !== 6)}
        >
          {loading ? 'Verifying...' : 'Verify'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default MFAVerification
