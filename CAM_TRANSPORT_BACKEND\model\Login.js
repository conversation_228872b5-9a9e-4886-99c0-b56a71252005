const mongoose = require('mongoose');

const LoginSchema = new mongoose.Schema({

    username: {
        type: String,
    },

    email: {
        type: String,
        required: true,
    },

    password: {
        type: String,
        required: true,
    },

    otp: {
        type: Number,
    },

    otpExpiry: {
        type: Date,
    },

    isVerified: {
        type: Boolean,
        default: false
    },

    lastLoginDate: {
        type: Date,
        default: null
    },

    ipAddress: {
        type: String,
        default: null
    },

    location: {
        type: String,
        default: null
    },

    company: {
        type: String,
        default: 'CAM Transport ltd.'
    },

    adminId: {
        type: String,
        unique: true,
        required: true
    },

    role: {
        type: String,
        default: 'admin'
    },

    isActive: {
        type: Boolean,
        default: true,
    },

    // MFA (Multi-Factor Authentication) fields
    mfaEnabled: {
        type: Boolean,
        default: false
    },

    // Master MFA secret - single secret for all devices
    mfaSecret: {
        type: String,
        default: null
    },

    // MFA Devices - For tracking and management only (no individual secrets)
    mfaDevices: [{
        deviceId: {
            type: String,
            required: true
            // Removed unique: true to prevent duplicate key errors across documents
            // Uniqueness will be enforced at the application level within each user's devices
        },
        deviceName: {
            type: String,
            required: true,
            default: 'Authenticator App'
        },
        isActive: {
            type: Boolean,
            default: true
        },
        registeredAt: {
            type: Date,
            default: Date.now
        },
        lastUsedAt: {
            type: Date,
            default: null
        },
        deviceInfo: {
            userAgent: String,
            ipAddress: String,
            platform: String
        }
    }],

    // Global backup codes (work with any device)
    backupCodes: [{
        code: {
            type: String,
            required: true
        },
        used: {
            type: Boolean,
            default: false
        },
        usedAt: {
            type: Date,
            default: null
        }
    }],

    mfaSetupAt: {
        type: Date,
        default: null
    },

    lastMfaUsed: {
        type: Date,
        default: null
    },

    // Security settings
    mfaSettings: {
        requireMfaForLogin: {
            type: Boolean,
            default: true
        },
        allowBackupCodes: {
            type: Boolean,
            default: true
        },
        maxDevices: {
            type: Number,
            default: 5
        }
    }
}, { timestamps: true });



const Login = mongoose.model('Login', LoginSchema);


module.exports = Login;
