const axios = require('axios');

async function testCompleteLoginFlow() {
    console.log('🧪 TESTING COMPLETE LOGIN FLOW WITH OTP');
    console.log('=====================================\n');
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    try {
        // Step 1: Login to trigger OTP
        console.log('📧 Step 1: Triggering login to generate OTP...');
        const loginResponse = await axios.post('http://localhost:8090/login', credentials, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Login response:', JSON.stringify(loginResponse.data, null, 2));
        const userId = loginResponse.data.user?.id || loginResponse.data.user?._id;
        console.log(`👤 User ID: ${userId}\n`);
        
        // Step 2: Test user profile endpoint
        console.log('🔍 Step 2: Testing user profile endpoint...');
        try {
            const profileResponse = await axios.get(`http://localhost:8090/user-profile/${userId}`, {
                headers: { 'Content-Type': 'application/json' }
            });
            console.log('✅ User profile endpoint works:', JSON.stringify(profileResponse.data, null, 2));
        } catch (error) {
            console.log('❌ User profile endpoint failed:', error.response?.data || error.message);
        }
        
        console.log('\n📧 Step 3: Check your email for the OTP');
        console.log('🔐 Enter the OTP from your email in the frontend');
        console.log('🎯 The login should now complete successfully!');
        
        console.log('\n✅ FIXES APPLIED:');
        console.log('  ✓ Frontend now passes userId to NextAuth');
        console.log('  ✓ NextAuth now uses userId instead of username');
        console.log('  ✓ Backend user-profile endpoint expects userId');
        console.log('  ✓ Complete login flow should work now');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

testCompleteLoginFlow();
