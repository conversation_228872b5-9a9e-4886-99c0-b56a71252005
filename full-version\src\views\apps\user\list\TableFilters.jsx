// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid2'
import MenuItem from '@mui/material/MenuItem'

// Component Imports
import CustomTextField from '@core/components/mui/TextField'

const TableFilters = ({ setData, tableData }) => {
  // States
  const [inquiryType, setInquiryType] = useState('')

  useEffect(() => {
    const filteredData = tableData?.filter(user => {
      if (inquiryType && (user.inquiryType || user.type || 'General Inquiry') !== inquiryType) return false

      return true
    })

    setData(filteredData || [])
  }, [inquiryType, tableData, setData])

  return (
    <CardContent>
      <Grid container spacing={6}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <CustomTextField
            select
            fullWidth
            id='select-inquiry-type'
            value={inquiryType}
            onChange={e => setInquiryType(e.target.value)}
            slotProps={{
              select: { displayEmpty: true }
            }}
            label="Inquiry Type"
          >
            <MenuItem value=''>All Inquiry Types</MenuItem>
            <MenuItem value='General Inquiry'>General Inquiry</MenuItem>
            <MenuItem value='Support Request'>Support Request</MenuItem>
            <MenuItem value='Sales Inquiry'>Sales Inquiry</MenuItem>
            <MenuItem value='Technical Support'>Technical Support</MenuItem>
            <MenuItem value='Billing Question'>Billing Question</MenuItem>
            <MenuItem value='Partnership'>Partnership</MenuItem>
            <MenuItem value='Feedback'>Feedback</MenuItem>
            <MenuItem value='Complaint'>Complaint</MenuItem>
          </CustomTextField>
        </Grid>
      </Grid>
    </CardContent>
  )
}

export default TableFilters
