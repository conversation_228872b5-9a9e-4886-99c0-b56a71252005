// MUI Imports
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'
import Chip from '@mui/material/Chip'

const BackupCodesPage = () => {
  // Dummy backup codes
  const backupCodes = [
    'A1B2-C3D4-E5F6',
    'G7H8-I9J0-K1L2',
    'M3N4-O5P6-Q7R8',
    'S9T0-U1V2-W3X4',
    'Y5Z6-A7B8-C9D0',
    'E1F2-G3H4-I5J6',
    'K7L8-M9N0-O1P2',
    'Q3R4-S5T6-U7V8'
  ]

  return (
    <Grid container spacing={6}>
      <Grid size={{ xs: 12 }}>
        <Card>
          <CardHeader 
            title="MFA Backup Codes" 
            subheader="Use these codes when you can't access your primary MFA device"
          />
          <CardContent>
            <Alert severity="warning" className="mb-6">
              This is a dummy backup codes page. These are not real backup codes.
            </Alert>
            
            <div className="space-y-6">
              <div>
                <Typography variant="h6" className="mb-2">
                  Your Backup Codes
                </Typography>
                <Typography variant="body2" color="text.secondary" className="mb-4">
                  Each backup code can only be used once. Store them in a safe place.
                </Typography>
                
                <Alert severity="info" className="mb-4">
                  <Typography variant="body2">
                    <strong>Important:</strong> Save these codes in a secure location. You won't be able to see them again after leaving this page.
                  </Typography>
                </Alert>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
                  {backupCodes.map((code, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded border">
                      <Typography variant="body1" className="font-mono">
                        {code}
                      </Typography>
                      <Chip 
                        label="Unused" 
                        size="small" 
                        color="success" 
                        variant="outlined"
                      />
                    </div>
                  ))}
                </div>
              </div>
              
              <div>
                <Typography variant="h6" className="mb-2">
                  How to Use Backup Codes
                </Typography>
                <div className="space-y-2">
                  <Typography variant="body2" color="text.secondary">
                    • Use backup codes when you don't have access to your primary MFA device
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Enter a backup code in place of your regular MFA code
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Each code can only be used once
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Generate new codes when you're running low
                  </Typography>
                </div>
              </div>
              
              <div>
                <Typography variant="h6" className="mb-2">
                  Security Tips
                </Typography>
                <div className="space-y-2">
                  <Typography variant="body2" color="text.secondary">
                    • Store these codes in a password manager or secure location
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Don't store them on the same device you use for MFA
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Consider printing them and storing in a safe place
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Generate new codes if you suspect they've been compromised
                  </Typography>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <Button variant="contained" color="primary">
                  Download Codes
                </Button>
                <Button variant="outlined" color="primary">
                  Print Codes
                </Button>
                <Button variant="outlined" color="warning">
                  Generate New Codes
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default BackupCodesPage
