'use client'

// React Imports
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

// MUI Imports
import CircularProgress from '@mui/material/CircularProgress'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'

// Auth Imports
import { checkUserExists } from '@/middleware/authCheck'

const ClientAuthGuard = ({ children }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)

  useEffect(() => {
    const checkDatabaseUsers = async () => {
      try {
        console.log('🔒 Checking if any users exist in database...')
        
        // Check if any users exist in database
        const usersExist = await checkUserExists()
        
        if (!usersExist) {
          console.log('❌ No users exist in database, redirecting to login...')
          router.push('/en/login')
          return
        }
        
        console.log('✅ Users exist in database, allowing access')
        setHasAccess(true)
        
      } catch (error) {
        console.error('❌ Error checking database users:', error)
        // If there's an error, redirect to login for safety
        router.push('/en/login')
      } finally {
        setLoading(false)
      }
    }

    checkDatabaseUsers()
  }, [router])

  if (loading) {
    return (
      <Box className='flex items-center justify-center min-h-screen bg-backgroundPaper'>
        <Card className='w-full max-w-md mx-4'>
          <CardContent className='flex flex-col items-center gap-6 p-8'>
            <CircularProgress size={60} />
            <div className='text-center'>
              <Typography variant='h6' className='mb-2'>
                Checking Authentication
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Please wait while we verify your access to the system...
              </Typography>
            </div>
          </CardContent>
        </Card>
      </Box>
    )
  }

  if (!hasAccess) {
    return (
      <Box className='flex items-center justify-center min-h-screen bg-backgroundPaper'>
        <Card className='w-full max-w-md mx-4'>
          <CardContent className='flex flex-col items-center gap-6 p-8'>
            <div className='flex items-center justify-center w-16 h-16 rounded-full bg-primary/10'>
              <i className='tabler-lock text-3xl text-primary' />
            </div>
            <div className='text-center'>
              <Typography variant='h6' className='mb-2'>
                Access Restricted
              </Typography>
              <Typography variant='body2' color='text.secondary'>
                Redirecting to login page...
              </Typography>
            </div>
          </CardContent>
        </Card>
      </Box>
    )
  }

  return children
}

export default ClientAuthGuard
