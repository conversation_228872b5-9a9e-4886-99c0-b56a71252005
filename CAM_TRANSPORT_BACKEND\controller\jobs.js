const path = require('path');
const fs = require('fs');
const Jobs = require('../model/jobs');
const { sendEmail } = require('../service/Mailer');
const { checkJobsEmail24hBlock, checkJobsIp24hBlock } = require('../utils/rateLimitHelpers');
const { jobValidationSchema } = require('../middleware/job_Validator');
require('dotenv').config();

const hasMongoOperators = (obj) => {
    for (const key in obj) {
        if (key.startsWith("$")) {
            return true;
        }
        if (typeof obj[key] === "object" && obj[key] !== null) {
            if (hasMongoOperators(obj[key])) return true;
        }
    }
    return false;
};

const applyJob = async (req, res) => {

    if (req.body._honeypot && req.body._honeypot.length > 0) {
        return res.status(400).json({ message: "Spam detected" });
    }

    if (!req.file) {
        return res.status(400).json({ error: 'Resume file is required.' });
    }

    if (hasMongoOperators(req.body)) {
        if (req.file) {
            fs.unlinkSync(req.file.path);
        }
        return res.status(400).json({ message: "Invalid input detected." });
    }

    // Combine req.body with the resume file path for Joi validation
    const dataToValidate = {
        ...req.body,
        resume: req.file.path
    };

    const { error, value } = jobValidationSchema.validate(dataToValidate, { abortEarly: false });
    if (error) {
        if (req.file) {
            fs.unlinkSync(req.file.path);
        }
        const messages = error.details.map((d) => d.message);
        return res.status(400).json({ message: messages.join(', ') });
    }

    try {
        const {
            first_name,
            last_name,
            email,
            phone_number,
            dob,
            address,
            position,
            employment_type,
            preferred_start_date,
            relocate,
            experience,
            commercial_license,
            work_reason,
            reference,
            other_reference,
            specific_driving_role,
            specific_non_driving_role,
            other_job
        } = value;

        const userIp = req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000); // For 24-hour limits

        // --- BLOCKING LOGIC --- //

        // 1. Per-email 24-hour limit: Block after 3 applications from the same email in 24 hours
        const { isBlocked: isEmail24hBlocked, unblockDate: unblockDateEmail } = await checkJobsEmail24hBlock(email, now, oneDayAgo);
        if (isEmail24hBlocked) {
            const unblockDateString = unblockDateEmail.toLocaleString();
            return res.status(429).json({
                message: `This email has reached the 3 applications limit in 24 hours. You can apply again after: ${unblockDateString}.`
            });
        }

        // 2. IP-based 24-hour limit: Block after 3 applications from the same IP in 24 hours
        const { isBlocked: isIp24hBlocked, unblockDate: unblockDateIp } = await checkJobsIp24hBlock(userIp, now, oneDayAgo);
        if (isIp24hBlocked) {
            const unblockDateString = unblockDateIp.toLocaleString();
            return res.status(429).json({
                message: `Too many applications from this IP address. You can apply again after: ${unblockDateString}.`
            });
        }

        const resumePath = req.file.path;

        const jobData = {
            first_name,
            last_name,
            email,
            phone_number,
            dob,
            address,
            employment_type,
            preferred_start_date,
            relocate,
            experience,
            resume: resumePath,
            work_reason,
            reference,
            ip: userIp // Store the IP address
        };

        if (position === 'Driving Position') {
            jobData.position = position;
            jobData.specific_driving_role = specific_driving_role;
            jobData.commercial_license = commercial_license;

            if (specific_driving_role === 'Other Driving Role') {
                jobData.other_job = other_job;
            }
        } else if (position === 'Non-Driving Position') {
            jobData.position = position;
            jobData.specific_non_driving_role = specific_non_driving_role;

            if (specific_non_driving_role === 'Other Non-Driving Role') {
                jobData.other_job = other_job;
            }
        }

        if (reference === 'Other(Please specify)') {
            jobData.other_reference = other_reference;
        }

        const job = new Jobs(jobData);
        await job.save();

        // Try to send emails, but don't fail the job application if email fails
        try {
            const applicantTemplateData = {
                first_name,
                last_name,
                position: specific_driving_role || specific_non_driving_role || position, // Use specific role if available
            };
            await sendEmail({
                to: email,
                subject: 'CAM Transport - Application Received',
                template: 'job_user.ejs',
                templateData: applicantTemplateData,
                importance: 'high'
            });

            const adminTemplateData = {
                first_name,
                last_name,
                email,
                phone_number,
                dob,
                address,
                position: position,
                specific_driving_role: specific_driving_role || '',
                specific_non_driving_role: specific_non_driving_role || '',
                employment_type,
                preferred_start_date,
                relocate,
                experience,
                commercial_license: commercial_license || false,
                work_reason,
                reference,
                other_reference: other_reference || '',
                other_job: (specific_driving_role === 'Other Driving Role' || specific_non_driving_role === 'Other Non-Driving Role') ? other_job || '' : '',
                selectedRole: specific_driving_role || specific_non_driving_role || position,
                resume: resumePath, // Add the resume path for the email template
                resumeFileName: require('path').basename(resumePath) // Add just the filename too
            };
            await sendEmail({
                to: process.env.SMTP_FROM,
                subject: `New Job Application: ${first_name}-${last_name} for ${specific_driving_role || specific_non_driving_role || position}`,
                template: 'job_admin.ejs',
                templateData: adminTemplateData,
                attachments: [
                    {
                        filename: require('path').basename(resumePath),
                        path: resumePath,
                        contentType: 'application/pdf'
                    }
                ],
                importance: 'high'
            });

            console.log("✅ Job application emails sent successfully");
        } catch (emailError) {
            console.error("❌ Error sending job application emails (application still saved):", emailError.message);
            // Continue execution - don't fail the job application
        }

        res.status(200).json({ message: 'Application submitted successfully.' });

    } catch (err) {
        if (err.name === 'ValidationError') {
            const firstError = Object.values(err.errors)[0].message;
            return res.status(400).json({ message: firstError });
        }
        console.error('Error applying for job:', err);
        if (req.file) {
            try {
                fs.unlinkSync(req.file.path);
            } catch (unlinkErr) {
                console.error('Failed to delete file:', unlinkErr);
            }
        }
        res.status(500).json({ error: 'Server error while submitting application.' });
    }
};


const GetallJobUsers = async (req, res) => {
    try {
        const jobs = await Jobs.find({})
            .sort({ createdAt: -1 }) // Sort by newest first
            .lean();

        return res.status(200).json(jobs);
    } catch (error) {
        console.error("GetallJobUsers error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
}


const DeleteJobUser = async (req, res) => {
    try {
        const { id } = req.params;
        await Jobs.findByIdAndDelete(id);
        return res.status(200).json({ message: "User deleted successfully" });
    } catch (error) {
        console.error("DeleteJobUser error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
}

module.exports = { applyJob, GetallJobUsers, DeleteJobUser };
