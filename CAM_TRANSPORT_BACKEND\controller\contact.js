const { contactValidationSchema } = require("../middleware/contact_Validator");
const Contact = require("../model/contact");
const { sendEmail } = require("../service/Mailer");
const { checkEmail24hBlock, checkIp24hBlock } = require("../utils/rateLimitHelpers");

require("dotenv").config();

const hasMongoOperators = (obj) => {
    for (const key in obj) {
        if (key.startsWith("$")) {
            return true;
        }
        if (typeof obj[key] === "object" && obj[key] !== null) {
            if (hasMongoOperators(obj[key])) return true;
        }
    }
    return false;
};

const ContactForm = async (req, res) => {
    try {
        if (req.body._honeypot && req.body._honeypot.length > 0) {
            return res.status(400).json({ message: "Spam detected" });
        }

        if (hasMongoOperators(req.body)) {
            return res.status(400).json({ message: "Invalid input detected" });
        }

        const { error, value } = contactValidationSchema.validate(req.body, {
            abortEarly: false,
        });

        if (error) {
            return res.status(400).json({
                message: error.details.map((err) => err.message).join(", "),
            });
        }

        const {
            name,
            email,
            contact,
            company = "",
            inquiryType,
            otherInquiry = "",
            message,
            ip,
        } = value;

        const userIp =
            ip ||
            req.ip ||
            req.headers["x-forwarded-for"] ||
            req.connection.remoteAddress ||
            "";

        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);

        const recent15mEmailCount = await Contact.countDocuments({
            email: email.trim(),
            createdAt: { $gte: fifteenMinutesAgo },
        });
        if (recent15mEmailCount >= 3) {
            return res.status(429).json({
                message:
                    "This email has reached the 3 submissions limit in 15 minutes. Please try again later.",
            });
        }

        const { isBlocked: isEmail24hBlocked, unblockDate: unblockDate24h } =
            await checkEmail24hBlock(email, now, oneDayAgo);
        if (isEmail24hBlocked) {
            const unblockDateString24h = unblockDate24h.toLocaleString();
            return res.status(429).json({
                message: `This email has reached the 15 submissions limit in 24 hours. You can contact us again after: ${unblockDateString24h}.`,
            });
        }

        const { isBlocked: isIp24hBlocked, unblockDate: unblockDateIp } =
            await checkIp24hBlock(userIp, now, oneDayAgo);
        if (isIp24hBlocked) {
            const unblockDateStringIp = unblockDateIp.toLocaleString();
            return res.status(429).json({
                message: `Too many submissions from this IP address. You can contact us again after: ${unblockDateStringIp}.`,
            });
        }

        const contactData = {
            name: name?.trim(),
            email: email?.trim(),
            contact: contact?.trim(),
            company: company?.trim(),
            inquiryType,
            message: message?.trim(),
            ip: userIp,
        };

        if (inquiryType === "Other") {
            contactData.otherInquiry = otherInquiry?.trim();
        }

        if (inquiryType === "Other" && !otherInquiry) {
            return res.status(400).json({ message: "Other inquiry must be provided" });
        }

        const newContact = await Contact.create(contactData);

        // Try to send emails, but don't fail the contact submission if email fails
        try {
            const userTemplateData = {
                name,
                inquiryType,
                message,
            };
            await sendEmail({
                to: email,
                subject: "Welcome to CAM Transport",
                template: "contact_user.ejs",
                templateData: userTemplateData,
                importance: "high",
            });

            const adminTemplateData = {
                name,
                email,
                contact,
                company,
                inquiryType,
                otherInquiry: contactData.otherInquiry || "",
                message,
            };
            await sendEmail({
                to: process.env.SMTP_FROM,
                subject: "New Contact Inquiry",
                template: "contact_admin.ejs",
                templateData: adminTemplateData,
                importance: "high",
            });

            console.log("✅ Emails sent successfully");
        } catch (emailError) {
            console.error("❌ Error sending emails (contact still saved):", emailError.message);
            // Continue execution - don't fail the contact submission
        }

        return res
            .status(201)
            .json({ message: "Contact form submitted successfully", data: newContact });
    } catch (error) {
        if (error.name === "ValidationError") {
            const firstError = Object.values(error.errors)[0].message;
            return res.status(400).json({ message: firstError });
        }
        console.error("ContactForm error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
};

const GetAllUsers = async (req, res) => {
    try {
        const contacts = await Contact.find({})
            .sort({ createdAt: -1 }) // Sort by newest first
            .lean(); 

        return res.status(200).json(contacts);
    } catch (error) {
        console.error("GetAllUsers error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
}

const DeleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        await Contact.findByIdAndDelete(id);
        return res.status(200).json({ message: "User deleted successfully" });
    } catch (error) {
        console.error("DeleteUser error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
}

module.exports = {
    ContactForm,
    GetAllUsers,
    DeleteUser,
};
