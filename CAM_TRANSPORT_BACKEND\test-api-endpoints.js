const axios = require('axios');

const API_BASE_URL = 'http://localhost:8090';

async function testUserManagementAPI() {
    try {
        console.log('🧪 Testing User Management API Endpoints...\n');

        // Test 1: Get user profile for super admin
        console.log('1️⃣ Testing user profile endpoint...');
        try {
            const superAdminResponse = await axios.get(`${API_BASE_URL}/user-profile/admins/list`);
            console.log('✅ Admin list endpoint working');
            
            if (superAdminResponse.data.admins && superAdminResponse.data.admins.length > 0) {
                const superAdmin = superAdminResponse.data.admins.find(admin => admin.role === 'super_admin');
                if (superAdmin) {
                    console.log(`✅ Found super admin: ${superAdmin.username} (${superAdmin.email})`);
                    
                    // Test user profile endpoint
                    const profileResponse = await axios.get(`${API_BASE_URL}/user-profile/${superAdmin.id}`);
                    console.log(`✅ User profile endpoint working for super admin`);
                } else {
                    console.log('❌ No super admin found');
                }
            }
        } catch (error) {
            console.log('❌ Admin list endpoint failed:', error.message);
        }

        // Test 2: Test user management endpoints (should require authentication)
        console.log('\n2️⃣ Testing user management endpoints (without auth)...');
        try {
            const usersResponse = await axios.get(`${API_BASE_URL}/user-profile/users`);
            console.log('⚠️ Users endpoint accessible without auth (this might be expected for development)');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.log('✅ Users endpoint properly protected (401 Unauthorized)');
            } else {
                console.log('❌ Unexpected error:', error.message);
            }
        }

        // Test 3: Test user creation endpoint
        console.log('\n3️⃣ Testing user creation endpoint (without auth)...');
        try {
            const createResponse = await axios.post(`${API_BASE_URL}/user-profile/users/create`, {
                username: 'testuser',
                email: '<EMAIL>',
                password: 'test123',
                role: 'normal_user'
            });
            console.log('⚠️ User creation endpoint accessible without auth');
        } catch (error) {
            if (error.response && error.response.status === 401) {
                console.log('✅ User creation endpoint properly protected (401 Unauthorized)');
            } else {
                console.log('❌ Unexpected error:', error.message);
            }
        }

        // Test 4: Check if users exist endpoint
        console.log('\n4️⃣ Testing check users endpoint...');
        try {
            const checkResponse = await axios.get(`${API_BASE_URL}/user-profile/check-users`);
            console.log('✅ Check users endpoint working');
            console.log(`📊 Users exist: ${checkResponse.data.hasUsers}, Count: ${checkResponse.data.userCount}`);
        } catch (error) {
            console.log('❌ Check users endpoint failed:', error.message);
        }

        console.log('\n✅ API endpoint testing completed!');
        console.log('\n📝 Next steps:');
        console.log('1. Open http://localhost:3000 in your browser');
        console.log('2. Login with super admin: <EMAIL> / admin123');
        console.log('3. Check if "User Management" appears in the sidebar');
        console.log('4. Login with normal user: <EMAIL> / user123');
        console.log('5. Verify "User Management" is grayed out or hidden');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testUserManagementAPI();
