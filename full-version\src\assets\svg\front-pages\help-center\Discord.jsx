const Discord = props => {
  return (
    <svg xmlns='http://www.w3.org/2000/svg' width='58' height='58' viewBox='0 0 58 58' fill='none' {...props}>
      <path
        opacity='0.2'
        d='M22.8829 41.2571L20.1415 46.6946C19.9651 47.0654 19.6651 47.3632 19.293 47.5368C18.9209 47.7105 18.4999 47.7491 18.1024 47.6462C12.5517 46.2868 7.74854 43.9305 4.25947 40.8946C3.99643 40.6625 3.80541 40.3599 3.70904 40.0226C3.61267 39.6853 3.61499 39.3275 3.71572 38.9915L11.3962 13.3446C11.4709 13.0821 11.6062 12.8409 11.7912 12.6402C11.9761 12.4395 12.2055 12.285 12.461 12.1891C14.631 11.2989 16.875 10.6014 19.1673 10.1047C19.6078 10.0083 20.0684 10.0774 20.4612 10.2988C20.854 10.5203 21.1515 10.8787 21.297 11.3055L23.0868 16.7204C27.0103 16.1766 30.99 16.1766 34.9134 16.7204L36.7032 11.3055C36.8487 10.8787 37.1462 10.5203 37.539 10.2988C37.9318 10.0774 38.3924 10.0083 38.8329 10.1047C41.1252 10.6014 43.3692 11.2989 45.5392 12.1891C45.7947 12.285 46.0241 12.4395 46.209 12.6402C46.394 12.8409 46.5293 13.0821 46.604 13.3446L54.2845 38.9915C54.3852 39.3275 54.3875 39.6853 54.2912 40.0226C54.1948 40.3599 54.0038 40.6625 53.7407 40.8946C50.2517 43.9305 45.4485 46.2868 39.8978 47.6462C39.5003 47.7491 39.0793 47.7105 38.7072 47.5368C38.3351 47.3632 38.0351 47.0654 37.8587 46.6946L35.1173 41.2571C33.0907 41.5421 31.0467 41.6859 29.0001 41.6876C26.9535 41.6859 24.9095 41.5421 22.8829 41.2571Z'
        fill='currentColor'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M24.4688 32.625C24.4688 34.1265 23.2515 35.3438 21.75 35.3438C20.2485 35.3438 19.0312 34.1265 19.0312 32.625C19.0312 31.1235 20.2485 29.9062 21.75 29.9062C23.2515 29.9062 24.4688 31.1235 24.4688 32.625ZM38.9688 32.625C38.9688 34.1265 37.7515 35.3438 36.25 35.3438C34.7485 35.3438 33.5312 34.1265 33.5312 32.625C33.5312 31.1235 34.7485 29.9062 36.25 29.9062C37.7515 29.9062 38.9688 31.1235 38.9688 32.625Z'
        fill='currentColor'
      />
      <path
        d='M16.8563 18.1251C20.7855 16.8936 24.8826 16.2821 29.0001 16.3126C33.1176 16.2821 37.2147 16.8936 41.1439 18.1251M41.1439 39.8751C37.2147 41.1065 33.1176 41.718 29.0001 41.6876C24.8826 41.718 20.7855 41.1065 16.8563 39.8751M35.1173 41.2571L37.8587 46.6946C38.0351 47.0654 38.3351 47.3632 38.7072 47.5368C39.0793 47.7105 39.5003 47.7491 39.8978 47.6462C45.4485 46.2868 50.2517 43.9305 53.7407 40.8946C54.0038 40.6625 54.1948 40.3599 54.2912 40.0226C54.3875 39.6853 54.3852 39.3275 54.2845 38.9915L46.604 13.3446C46.5293 13.0821 46.394 12.8409 46.209 12.6402C46.0241 12.4395 45.7947 12.285 45.5392 12.1891C43.3692 11.2989 41.1252 10.6014 38.8329 10.1047C38.3924 10.0083 37.9318 10.0774 37.539 10.2988C37.1462 10.5203 36.8487 10.8787 36.7032 11.3055L34.9134 16.7204M22.8829 41.2571L20.1415 46.6946C19.9651 47.0654 19.6651 47.3632 19.293 47.5368C18.9209 47.7105 18.4999 47.7491 18.1024 47.6462C12.5517 46.2868 7.74854 43.9305 4.25947 40.8946C3.99643 40.6625 3.80541 40.3599 3.70904 40.0226C3.61267 39.6853 3.61499 39.3275 3.71572 38.9915L11.3962 13.3446C11.4709 13.0821 11.6062 12.8409 11.7912 12.6402C11.9761 12.4395 12.2055 12.285 12.461 12.1891C14.631 11.2989 16.875 10.6014 19.1673 10.1047C19.6078 10.0083 20.0684 10.0774 20.4612 10.2988C20.854 10.5203 21.1515 10.8787 21.297 11.3055L23.0868 16.7204'
        stroke='var(--mui-palette-text-secondary)'
        strokeOpacity='0.7'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default Discord
