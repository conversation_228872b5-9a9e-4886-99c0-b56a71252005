'use client'

// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import CardContent from '@mui/material/CardContent'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import Button from '@mui/material/Button'
import Box from '@mui/material/Box'

// Component Imports
import CustomTextField from '@core/components/mui/TextField'

const TableFilters = ({ setData, tableData }) => {
  // States
  const [position, setPosition] = useState('')
  const [employmentType, setEmploymentType] = useState('')
  const [experience, setExperience] = useState('')
  const [relocate, setRelocate] = useState('')
  const [commercialLicense, setCommercialLicense] = useState('')

  useEffect(() => {
    const filteredData = tableData?.filter(user => {
      if (position && (user.position || '').toLowerCase() !== position.toLowerCase()) return false
      if (employmentType && (user.employmentType || user.employment_type || '').toLowerCase() !== employmentType.toLowerCase()) return false
      if (experience && (user.experience || '').toLowerCase() !== experience.toLowerCase()) return false
      if (relocate && (user.relocate || '').toLowerCase() !== relocate.toLowerCase()) return false
      if (commercialLicense && commercialLicense !== 'all') {
        const hasLicense = user.commercialLicense || user.commercial_license || false
        if (commercialLicense === 'yes' && !hasLicense) return false
        if (commercialLicense === 'no' && hasLicense) return false
      }

      return true
    })

    setData(filteredData)
  }, [position, employmentType, experience, relocate, commercialLicense, tableData, setData])

  const clearAllFilters = () => {
    setPosition('')
    setEmploymentType('')
    setExperience('')
    setRelocate('')
    setCommercialLicense('')
  }

  const hasActiveFilters = position || employmentType || experience || relocate || commercialLicense

  return (
    <CardContent>
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4'>
        <FormControl fullWidth>
          <InputLabel id='position-select'>Position Category</InputLabel>
          <Select
            fullWidth
            id='select-position'
            value={position}
            onChange={e => setPosition(e.target.value)}
            label='Position Category'
            labelId='position-select'
            inputProps={{ placeholder: 'Select Position Category' }}
          >
            <MenuItem value=''>All Positions</MenuItem>
            <MenuItem value='Driving Position'>Driving Position</MenuItem>
            <MenuItem value='Non-Driving Position'>Non-Driving Position</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel id='employment-type-select'>Employment Type</InputLabel>
          <Select
            fullWidth
            id='select-employment-type'
            value={employmentType}
            onChange={e => setEmploymentType(e.target.value)}
            label='Employment Type'
            labelId='employment-type-select'
            inputProps={{ placeholder: 'Select Employment Type' }}
          >
            <MenuItem value=''>All Employment Types</MenuItem>
            <MenuItem value='Full-time'>Full-time</MenuItem>
            <MenuItem value='Part-time'>Part-time</MenuItem>
            <MenuItem value='Contract'>Contract</MenuItem>
            <MenuItem value='Temporary'>Temporary</MenuItem>
            <MenuItem value='Freelance'>Freelance</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel id='experience-select'>Experience Level</InputLabel>
          <Select
            fullWidth
            id='select-experience'
            value={experience}
            onChange={e => setExperience(e.target.value)}
            label='Experience Level'
            labelId='experience-select'
            inputProps={{ placeholder: 'Select Experience Level' }}
          >
            <MenuItem value=''>All Experience Levels</MenuItem>
            <MenuItem value='Fresher'>Fresher</MenuItem>
            <MenuItem value='<6 months'>&lt;6 months</MenuItem>
            <MenuItem value='6-12 months'>6-12 months</MenuItem>
            <MenuItem value='1-2 years'>1-2 years</MenuItem>
            <MenuItem value='2-5 years'>2-5 years</MenuItem>
            <MenuItem value='5+ years'>5+ years</MenuItem>
            <MenuItem value='10+ years'>10+ years</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel id='relocate-select'>Willing to Relocate</InputLabel>
          <Select
            fullWidth
            id='select-relocate'
            value={relocate}
            onChange={e => setRelocate(e.target.value)}
            label='Willing to Relocate'
            labelId='relocate-select'
            inputProps={{ placeholder: 'Select Relocation Preference' }}
          >
            <MenuItem value=''>All Preferences</MenuItem>
            <MenuItem value='Yes'>Yes</MenuItem>
            <MenuItem value='No'>No</MenuItem>
          </Select>
        </FormControl>

        <FormControl fullWidth>
          <InputLabel id='commercial-license-select'>Commercial License</InputLabel>
          <Select
            fullWidth
            id='select-commercial-license'
            value={commercialLicense}
            onChange={e => setCommercialLicense(e.target.value)}
            label='Commercial License'
            labelId='commercial-license-select'
            inputProps={{ placeholder: 'Select License Status' }}
          >
            <MenuItem value=''>All License Status</MenuItem>
            <MenuItem value='yes'>Has Commercial License</MenuItem>
            <MenuItem value='no'>No Commercial License</MenuItem>
          </Select>
        </FormControl>
      </div>

      {/* Clear Filters Button */}
      {hasActiveFilters && (
        <Box className='mt-4 flex justify-center'>
          <Button
            variant='outlined'
            color='secondary'
            onClick={clearAllFilters}
            startIcon={<i className='tabler-filter-off' />}
            size='small'
          >
            Clear All Filters
          </Button>
        </Box>
      )}
    </CardContent>
  )
}

export default TableFilters
