const { Router } = require('express');
const { apply<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DeleteJobUser } = require('../controller/jobs');
const upload = require('../service/Multer');
const rateLimit = require('express-rate-limit');
const Jobs = require('../model/jobs');
const { checkJobsEmail24hBlock, checkJobsIp24hBlock } = require('../utils/rateLimitHelpers');

const JobRouter = Router();

const jobRateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 15,
    message: 'Too many job submissions from this IP, please try again later',
});


/**
 * @swagger
 * /jobs:
 *   post:
 *     summary: Submit a job application
 *     tags:
 *       - Job Application
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - email
 *               - phone_number
 *               - dob
 *               - address
 *               - position
 *               - employment_type
 *               - preferred_start_date
 *               - relocate
 *               - experience
 *               - resume
 *               - work_reason
 *               - reference
 *             properties:
 *               first_name:
 *                  type: string
 *               last_name:
 *                  type: string
 *               email:
 *                 type: string
 *               phone_number:
 *                 type: string
 *               dob:
 *                 type: string
 *                 format: date
 *               address:
 *                 type: string
 *               position:
 *                 type: string
 *                 enum: ['Driving Position', 'Non-Driving Position']
 *                 description: The general category of the job position.
 *               specific_driving_role:
 *                 type: string
 *                 enum: ['CDL Driver', 'Non-CDL Driver', 'Heavy Equipment Operator', 'Local Driver', 'Long Haul Driver', 'Other Driving Role']
 *                 description: Required if position is 'Driving Position'.
 *               specific_non_driving_role:
 *                 type: string
 *                 enum: ['Dispatcher', 'Admin', 'Manager', 'Technician', 'HR', 'Accountant', 'Other Non-Driving Role']
 *                 description: Required if position is 'Non-Driving Position'.
 *               commercial_license:
 *                 type: boolean
 *                 description: Required if position is 'Driving Position'.
 *               other_job:
 *                 type: string
 *                 description: Required if specific_driving_role is 'Other Driving Role' or specific_non_driving_role is 'Other Non-Driving Role'.
 *               employment_type:
 *                 type: string
 *               preferred_start_date:
 *                 type: string
 *                 format: date
 *               relocate:
 *                 type: string
 *               experience:
 *                 type: string
 *               resume:
 *                 type: string
 *                 format: binary
 *               work_reason:
 *                 type: string
 *               reference:
 *                 type: string
 *               other_reference:
 *                 type: string
 *     responses:
 *       200:
 *         description: Job application submitted successfully
 *       400:
 *         description: Validation or missing field error
 *       422:
 *         description: Unprocessable entity (e.g., file type not allowed)
 *       500:
 *         description: Internal server error
 */


JobRouter.post('/', jobRateLimiter, upload.single('resume'), applyJob);

JobRouter.get('/get-jobs', GetallJobUsers);

JobRouter.delete('/delete-job/:id', DeleteJobUser);

// GET remaining global attempts for job form in 15 minutes
JobRouter.get('/user-limit', async (req, res) => {
    const now = new Date();
    const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Global 15-minute limit
    const recentGlobalCount = await Jobs.countDocuments({
        createdAt: { $gte: fifteenMinutesAgo }
    });
    const remaining = Math.max(0, 15 - recentGlobalCount);

    // Fetch all relevant jobs in the last 24 hours and 15 minutes for analysis
    const recentJobsIn24h = await Jobs.find({ createdAt: { $gte: oneDayAgo } }).sort({ createdAt: 1 });
    const recentJobsIn15m = await Jobs.find({ createdAt: { $gte: fifteenMinutesAgo } }).sort({ createdAt: 1 });

    // --- Per-Email 24-hour Limit Blocked Emails (3 submissions) ---
    const blocked24hEmails = [];
    const email24hMap = new Map();
    for (const job of recentJobsIn24h) {
        if (!email24hMap.has(job.email)) {
            email24hMap.set(job.email, []);
        }
        email24hMap.get(job.email).push(job.createdAt);
    }
    for (const [email, dates] of email24hMap.entries()) {
        const { isBlocked, unblockDate } = await checkJobsEmail24hBlock(email, now, oneDayAgo);
        if (isBlocked) {
            blocked24hEmails.push({
                email,
                unblockDate: unblockDate.toLocaleString(),
                reason: `Exceeded 3 applications in 24 hours`
            });
        }
    }

    // --- IP-based 24-hour Limit Blocked IPs (3 submissions) ---
    const blocked24hIPs = [];
    const ip24hMap = new Map();
    for (const job of recentJobsIn24h) {
        if (!ip24hMap.has(job.ip)) {
            ip24hMap.set(job.ip, []);
        }
        ip24hMap.get(job.ip).push(job.createdAt);
    }
    for (const [ip, dates] of ip24hMap.entries()) {
        const { isBlocked, unblockDate } = await checkJobsIp24hBlock(ip, now, oneDayAgo);
        if (isBlocked) {
            blocked24hIPs.push({
                ip,
                unblockDate: unblockDate.toLocaleString(),
                reason: `Exceeded 3 applications from this IP in 24 hours`
            });
        }
    }



    res.json({
        remaining,
        blocked24hEmails,
        blocked24hIPs,

    });
});

// Test route to check resume files
JobRouter.get('/test-resumes', async (req, res) => {
    try {
        const path = require('path');
        const fs = require('fs');

        const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');
        console.log('Checking resumes directory:', resumesDir);

        if (!fs.existsSync(resumesDir)) {
            return res.json({
                success: false,
                message: 'Resumes directory does not exist',
                path: resumesDir
            });
        }

        const files = fs.readdirSync(resumesDir);
        console.log('Files in resumes directory:', files);

        // Also get sample job applications to see resume paths
        const Jobs = require('../model/jobs');
        const sampleJobs = await Jobs.find({}).limit(3).select('first_name last_name resume createdAt');

        res.json({
            success: true,
            resumesDirectory: resumesDir,
            filesCount: files.length,
            files: files,
            sampleJobApplications: sampleJobs.map(job => ({
                name: `${job.first_name} ${job.last_name}`,
                resumePath: job.resume,
                created: job.createdAt
            }))
        });

    } catch (error) {
        console.error('Test resumes error:', error);
        res.status(500).json({
            success: false,
            message: 'Error checking resumes directory',
            error: error.message
        });
    }
});

// Download resume route
JobRouter.get('/download-resume/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        const path = require('path');
        const fs = require('fs');

        console.log('=== RESUME DOWNLOAD DEBUG ===');
        console.log('Requested filename:', filename);
        console.log('Request origin:', req.get('origin'));

        // Clean the filename - remove any path components that might be included
        let cleanFilename = filename;
        if (filename.includes('/')) {
            cleanFilename = filename.split('/').pop();
            console.log('Cleaned filename from:', filename, 'to:', cleanFilename);
        }

        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // Try multiple possible file paths using cleaned filename
        const possiblePaths = [
            path.join(__dirname, '..', 'uploads', 'resumes', cleanFilename),
            path.join(__dirname, '..', 'uploads', 'resumes', filename), // Try original too
            path.join(__dirname, '..', filename), // In case filename includes path
            path.join(__dirname, '..', 'uploads', filename)
        ];

        let filePath = null;
        let foundPath = null;

        for (const testPath of possiblePaths) {
            console.log('Testing path:', testPath);
            if (fs.existsSync(testPath)) {
                filePath = testPath;
                foundPath = testPath;
                console.log('✅ File found at:', testPath);
                break;
            } else {
                console.log('❌ File not found at:', testPath);
            }
        }

        if (!filePath) {
            console.log('❌ File not found in any location');

            // List what files actually exist in uploads/resumes
            const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');
            if (fs.existsSync(resumesDir)) {
                const files = fs.readdirSync(resumesDir);
                console.log('Files in resumes directory:', files);
            } else {
                console.log('Resumes directory does not exist:', resumesDir);
            }

            return res.status(404).json({
                success: false,
                message: 'Resume file not found',
                requestedFile: filename,
                cleanedFile: cleanFilename,
                searchedPaths: possiblePaths
            });
        }

        // Get file stats
        const stats = fs.statSync(filePath);
        console.log('✅ File found, size:', stats.size, 'bytes');

        // Set appropriate headers
        const ext = path.extname(filename).toLowerCase();
        let contentType = 'application/octet-stream';

        if (ext === '.pdf') {
            contentType = 'application/pdf';
        } else if (ext === '.doc') {
            contentType = 'application/msword';
        } else if (ext === '.docx') {
            contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        }

        console.log('Content-Type:', contentType);

        res.setHeader('Content-Type', contentType);
        res.setHeader('Content-Disposition', `attachment; filename="${cleanFilename}"`);
        res.setHeader('Content-Length', stats.size);

        // Stream the file
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);

        fileStream.on('error', (error) => {
            console.error('❌ Error streaming file:', error);
            if (!res.headersSent) {
                res.status(500).json({
                    success: false,
                    message: 'Error downloading file'
                });
            }
        });

        console.log('✅ Resume download started successfully from:', foundPath);

    } catch (error) {
        console.error('❌ Download resume error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error.message
        });
    }
});

// Simple route to list all resumes with download links
JobRouter.get('/list-all-resumes', async (req, res) => {
    try {
        const path = require('path');
        const fs = require('fs');

        // Set CORS headers
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');
        console.log('Checking directory:', resumesDir);

        if (!fs.existsSync(resumesDir)) {
            return res.json({
                success: false,
                message: 'Resumes directory does not exist',
                path: resumesDir
            });
        }

        const files = fs.readdirSync(resumesDir);
        const fileDetails = files.map(file => {
            const filePath = path.join(resumesDir, file);
            const stats = fs.statSync(filePath);
            return {
                filename: file,
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                downloadUrl: `${req.protocol}://${req.get('host')}/jobs/download-resume/${file}`,
                directUrl: `${req.protocol}://${req.get('host')}/uploads/resumes/${file}`
            };
        });

        res.json({
            success: true,
            directory: resumesDir,
            totalFiles: files.length,
            files: fileDetails
        });

    } catch (error) {
        console.error('List resumes error:', error);
        res.status(500).json({
            success: false,
            message: 'Error listing resumes',
            error: error.message
        });
    }
});

module.exports = JobRouter;