'use client'

// React Imports
import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'

// Component Imports
import Logo from '@components/layout/shared/Logo'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'

const EmailOTPVerify = () => {
  // States
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [userInfo, setUserInfo] = useState(null)

  // Hooks
  const router = useRouter()
  const searchParams = useSearchParams()
  const authBackground = useImageVariant('front-pages/backgrounds', 'auth-v2-background')

  useEffect(() => {
    // Get user info from URL parameters
    const userId = searchParams.get('userId')
    const email = searchParams.get('email')
    const username = searchParams.get('username')

    if (!userId || !email) {
      setError('Invalid verification link. Please try logging in again.')
      return
    }

    setUserInfo({ userId, email, username })
  }, [searchParams])

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP code')
      return
    }

    if (!userInfo) {
      setError('User information not found. Please try logging in again.')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('http://localhost:8090/login/verify-email-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userInfo.userId,
          otp: parseInt(otp)
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setSuccess('Email OTP verified successfully! Redirecting to your profile...')
        
        // Redirect to profile after successful verification
        setTimeout(() => {
          router.push('/pages/user-profile')
        }, 2000)
      } else {
        setError(result.message || 'OTP verification failed. Please try again.')
      }
    } catch (error) {
      console.error('OTP verification error:', error)
      setError('An error occurred during verification. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendOTP = async () => {
    if (!userInfo) {
      setError('User information not found. Please try logging in again.')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // Trigger a new login to resend OTP
      const response = await fetch('http://localhost:8090/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: userInfo.username,
          email: userInfo.email,
          password: 'resend' // This will fail but trigger OTP resend
        })
      })

      // Even if login fails, OTP should be resent
      setSuccess('New OTP sent to your email. Please check your inbox.')
    } catch (error) {
      console.error('Resend OTP error:', error)
      setError('Failed to resend OTP. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!userInfo) {
    return (
      <div className='flex justify-center items-center min-h-screen'>
        <CircularProgress />
      </div>
    )
  }

  return (
    <div className='flex bs-full justify-center'>
      <div className='flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden'>
        <img
          alt='auth-background'
          src={authBackground}
          className='max-bs-[650px] max-is-full bs-auto'
        />
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <div className='absolute block-start-5 sm:block-start-[33px] inline-start-6 sm:inline-start-[38px]'>
          <Logo />
        </div>
        <div className='flex flex-col gap-6 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset] mbs-8 sm:mbs-11 md:mbs-0'>
          <div className='flex flex-col gap-1'>
            <Typography variant='h4'>Email OTP Verification 📧</Typography>
            <Typography>
              We've sent a 6-digit verification code to <strong>{userInfo.email}</strong>
            </Typography>
          </div>

          {error && (
            <Alert severity='error' onClose={() => setError('')}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity='success'>
              {success}
            </Alert>
          )}

          <form onSubmit={handleSubmit} className='flex flex-col gap-6'>
            <TextField
              fullWidth
              label='Enter 6-digit OTP'
              placeholder='123456'
              value={otp}
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                setOtp(value)
                setError('')
              }}
              inputProps={{
                maxLength: 6,
                style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
              }}
              disabled={isLoading || success}
            />

            <Button
              fullWidth
              variant='contained'
              type='submit'
              disabled={isLoading || success || otp.length !== 6}
            >
              {isLoading ? (
                <>
                  <CircularProgress size={20} className='mr-2' />
                  Verifying...
                </>
              ) : success ? (
                'Redirecting...'
              ) : (
                'Verify OTP'
              )}
            </Button>

            <Button
              fullWidth
              variant='outlined'
              onClick={handleResendOTP}
              disabled={isLoading || success}
            >
              Resend OTP
            </Button>

            <Button
              fullWidth
              variant='text'
              onClick={() => router.push('/login')}
              disabled={isLoading}
            >
              Back to Login
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default EmailOTPVerify
