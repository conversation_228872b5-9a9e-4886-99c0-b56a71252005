'use client'

// React Imports
import { useState, useEffect } from 'react'

// NextAuth Imports
import { useSession } from 'next-auth/react'

// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import TablePagination from '@mui/material/TablePagination'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import TextField from '@mui/material/TextField'
import InputAdornment from '@mui/material/InputAdornment'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'
import Checkbox from '@mui/material/Checkbox'
import Tooltip from '@mui/material/Tooltip'

// Component Imports
import AddUserDrawer from './AddUserDrawer'
import EditUserDrawer from './EditUserDrawer'
import SimpleConfirmationDialog from '@/components/dialogs/SimpleConfirmationDialog'



const UserManagementList = () => {
  // Session
  const { data: session } = useSession()

  // States
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [totalUsers, setTotalUsers] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedUsers, setSelectedUsers] = useState([])
  const [anchorEl, setAnchorEl] = useState(null)
  const [selectedUser, setSelectedUser] = useState(null)
  const [addUserOpen, setAddUserOpen] = useState(false)
  const [editUserOpen, setEditUserOpen] = useState(false)
  const [confirmDialog, setConfirmDialog] = useState({ open: false, title: '', message: '', action: null })

  // Helper function to get authentication headers
  const getAuthHeaders = () => {
    const headers = {
      'Content-Type': 'application/json',
    }

    // Add user ID as query parameter for development authentication
    if (session?.user?.id) {
      return { headers, userId: session.user.id }
    }

    return { headers, userId: null }
  }

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError(null)

      const { headers, userId } = getAuthHeaders()

      if (!userId) {
        setError('Authentication required. Please login.')
        setLoading(false)
        return
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
      const params = new URLSearchParams({
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
        role: roleFilter,
        status: statusFilter,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        userId: userId // Add userId for authentication
      })

      const response = await fetch(`${API_BASE_URL}/user-profile/users?${params}`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        setUsers(result.users || [])
        setTotalUsers(result.pagination?.totalUsers || 0)
      } else {
        throw new Error(result.message || 'Failed to fetch users')
      }

    } catch (err) {
      console.error('❌ Error fetching users:', err)
      setError('Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  // Effects
  useEffect(() => {
    fetchUsers()
  }, [page, rowsPerPage, searchTerm, roleFilter, statusFilter])

  // Handlers
  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleMenuClick = (event, user) => {
    setAnchorEl(event.currentTarget)
    setSelectedUser(user)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedUser(null)
  }

  const handleSelectUser = (userId) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelectedUsers(users.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  const handleEditUser = () => {
    setEditUserOpen(true)
    handleMenuClose()
  }

  const handleToggleStatus = async () => {
    if (!selectedUser) return

    try {
      const { headers, userId } = getAuthHeaders()

      if (!userId) {
        setError('Authentication required. Please login.')
        return
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
      const response = await fetch(`${API_BASE_URL}/user-profile/users/${selectedUser.id}/toggle-status?userId=${userId}`, {
        method: 'PATCH',
        headers,
      })

      const result = await response.json()

      if (result.success) {
        fetchUsers() // Refresh the list
      } else {
        setError(result.message || 'Failed to update user status')
      }
    } catch (err) {
      console.error('❌ Error toggling user status:', err)
      setError('Failed to update user status')
    }

    handleMenuClose()
  }

  const handleDeleteUser = () => {
    setConfirmDialog({
      open: true,
      title: 'Delete User',
      message: `Are you sure you want to delete user "${selectedUser?.username}"? This action cannot be undone.`,
      action: async () => {
        try {
          const { headers, userId } = getAuthHeaders()

          if (!userId) {
            setError('Authentication required. Please login.')
            return
          }

          const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
          const response = await fetch(`${API_BASE_URL}/user-profile/users/${selectedUser.id}?userId=${userId}`, {
            method: 'DELETE',
            headers,
          })

          const result = await response.json()

          if (result.success) {
            fetchUsers() // Refresh the list
          } else {
            setError(result.message || 'Failed to delete user')
          }
        } catch (err) {
          console.error('❌ Error deleting user:', err)
          setError('Failed to delete user')
        }
      }
    })

    handleMenuClose()
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }



  if (loading && users.length === 0) {
    return (
      <Card>
        <CardContent className='flex justify-center items-center py-8'>
          <div className='flex flex-col items-center gap-4'>
            <CircularProgress />
            <Typography>Loading users...</Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent>
        {/* Header */}
        <div className='flex justify-between items-center mb-6'>
          <div>
            <Typography variant='h5' className='font-bold'>
              User Management
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              Manage admin users and their permissions
            </Typography>
          </div>
          <Button
            variant='contained'
            color='primary'
            startIcon={<i className='tabler-plus' />}
            onClick={() => setAddUserOpen(true)}
          >
            Add User
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert severity='error' className='mb-4' onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Filters */}
        <div className='flex gap-4 mb-6 flex-wrap'>
          <TextField
            size='small'
            placeholder='Search users...'
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <i className='tabler-search' />
                </InputAdornment>
              ),
            }}
            className='min-w-[200px]'
          />

          <FormControl size='small' className='min-w-[120px]'>
            <InputLabel>Role</InputLabel>
            <Select
              value={roleFilter}
              label='Role'
              onChange={(e) => setRoleFilter(e.target.value)}
            >
              <MenuItem value=''>All Roles</MenuItem>
              <MenuItem value='super_admin'>Super Admin</MenuItem>
              <MenuItem value='normal_user'>Normal User</MenuItem>
            </Select>
          </FormControl>

          <FormControl size='small' className='min-w-[120px]'>
            <InputLabel>Status</InputLabel>
            <Select
              value={statusFilter}
              label='Status'
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <MenuItem value=''>All Status</MenuItem>
              <MenuItem value='active'>Active</MenuItem>
              <MenuItem value='inactive'>Inactive</MenuItem>
            </Select>
          </FormControl>
        </div>

        {/* Users Table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding='checkbox'>
                  <Checkbox
                    indeterminate={selectedUsers.length > 0 && selectedUsers.length < users.length}
                    checked={users.length > 0 && selectedUsers.length === users.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>User</TableCell>
                <TableCell>Admin ID</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Login</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell padding='checkbox'>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleSelectUser(user.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <div className='flex items-center gap-3'>
                      <div className='flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-medium'>
                        {user.username?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                      <div>
                        <Typography variant='body2' className='font-medium'>
                          {user.username}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          {user.email}
                        </Typography>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2' style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                      {user.adminId}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.role === 'super_admin' ? 'Super Admin' : 'Normal User'}
                      color={user.role === 'super_admin' ? 'primary' : 'default'}
                      size='small'
                      variant='tonal'
                    />
                  </TableCell>
                  <TableCell>
                    <div className='flex flex-col gap-1'>
                      <Chip
                        label={user.isVerified ? 'Verified' : 'Pending'}
                        color={user.isVerified ? 'success' : 'warning'}
                        size='small'
                        variant='tonal'
                      />
                      <Chip
                        label={user.isActive ? 'Active' : 'Inactive'}
                        color={user.isActive ? 'primary' : 'default'}
                        size='small'
                        variant='tonal'
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Typography variant='body2'>
                      {formatDate(user.lastLoginDate)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Tooltip title='More actions'>
                      <IconButton
                        size='small'
                        onClick={(e) => handleMenuClick(e, user)}
                      >
                        <i className='tabler-dots-vertical' />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          component='div'
          count={totalUsers}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25, 50]}
        />

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleEditUser}>
            <i className='tabler-edit mr-2' />
            Edit User
          </MenuItem>
          <MenuItem onClick={handleToggleStatus}>
            <i className={`tabler-${selectedUser?.isActive ? 'user-off' : 'user-check'} mr-2`} />
            {selectedUser?.isActive ? 'Disable' : 'Enable'} User
          </MenuItem>
          <MenuItem onClick={handleDeleteUser} className='text-error'>
            <i className='tabler-trash mr-2' />
            Delete User
          </MenuItem>
        </Menu>

        {/* Drawers */}
        <AddUserDrawer
          open={addUserOpen}
          onClose={() => setAddUserOpen(false)}
          onUserAdded={fetchUsers}
        />

        <EditUserDrawer
          open={editUserOpen}
          onClose={() => setEditUserOpen(false)}
          user={selectedUser}
          onUserUpdated={fetchUsers}
        />

        {/* Confirmation Dialog */}
        <SimpleConfirmationDialog
          open={confirmDialog.open}
          onClose={() => setConfirmDialog({ ...confirmDialog, open: false })}
          onConfirm={() => {
            confirmDialog.action?.()
            setConfirmDialog({ ...confirmDialog, open: false })
          }}
          title={confirmDialog.title}
          message={confirmDialog.message}
        />
      </CardContent>
    </Card>
  )
}

export default UserManagementList
