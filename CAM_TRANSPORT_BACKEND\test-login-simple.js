const axios = require('axios');

async function testLogin() {
    console.log('🧪 Testing Login API...');
    
    try {
        const response = await axios.post('http://localhost:8090/login', {
            username: 'dhruv',
            email: '<EMAIL>',
            password: 'dhruv@123'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });
        
        console.log('✅ Login API Response:');
        console.log('Status:', response.status);
        console.log('Data:', JSON.stringify(response.data, null, 2));
        
        if (response.data.requiresEmailOTP) {
            console.log('\n🎯 SUCCESS: Email OTP is required!');
            console.log('📧 Check your email for the OTP code');
        }
        
    } catch (error) {
        console.log('❌ Login API Error:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', JSON.stringify(error.response.data, null, 2));
        } else {
            console.log('Error:', error.message);
        }
    }
}

testLogin();
