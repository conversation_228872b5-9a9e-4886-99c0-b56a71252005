'use client'

// React Imports
import { useState, useEffect } from 'react'

// NextAuth Imports
import { useSession } from 'next-auth/react'

// MUI Imports
import Drawer from '@mui/material/Drawer'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import IconButton from '@mui/material/IconButton'
import Divider from '@mui/material/Divider'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import Switch from '@mui/material/Switch'
import FormControlLabel from '@mui/material/FormControlLabel'
import Tabs from '@mui/material/Tabs'
import Tab from '@mui/material/Tab'
import Box from '@mui/material/Box'
import InputAdornment from '@mui/material/InputAdornment'

// Hook Imports
import { useForm, Controller } from 'react-hook-form'

const EditUserDrawer = ({ open, onClose, user, onUserUpdated }) => {
  // Session
  const { data: session } = useSession()

  // States
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)
  const [tabValue, setTabValue] = useState(0)
  const [showPassword, setShowPassword] = useState(false)

  // Form
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      username: '',
      email: '',
      role: 'admin',
      isActive: true,
      company: '',
      newPassword: ''
    }
  })

  // Effects
  useEffect(() => {
    if (user && open) {
      setValue('username', user.username || '')
      setValue('email', user.email || '')
      setValue('role', user.role || 'admin')
      setValue('isActive', user.isActive ?? true)
      setValue('company', user.company || '')
      setValue('newPassword', '')
      setError(null)
      setSuccess(false)
      setTabValue(0)
    }
  }, [user, open, setValue])

  // Handlers
  const onSubmit = async (data) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      if (!session?.user?.id) {
        setError('Authentication required. Please login.')
        setLoading(false)
        return
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

      // Prepare update data (exclude empty password)
      const updateData = {
        username: data.username,
        email: data.email,
        role: data.role,
        isActive: data.isActive,
        company: data.company
      }

      const response = await fetch(`${API_BASE_URL}/user-profile/users/${user.id}?userId=${session.user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      })

      const result = await response.json()

      if (result.success) {
        setSuccess(true)
        onUserUpdated?.()

        // Auto close after success
        setTimeout(() => {
          handleClose()
        }, 2000)
      } else {
        setError(result.message || 'Failed to update user')
      }

    } catch (err) {
      console.error('❌ Error updating user:', err)
      setError('Failed to update user')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordChange = async (data) => {
    if (!data.newPassword) {
      setError('New password is required')
      return
    }

    if (!session?.user?.id) {
      setError('Authentication required. Please login.')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
      const response = await fetch(`${API_BASE_URL}/user-profile/users/${user.id}/change-password?userId=${session.user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ newPassword: data.newPassword })
      })

      const result = await response.json()

      if (result.success) {
        setSuccess(true)
        setValue('newPassword', '')
      } else {
        setError(result.message || 'Failed to change password')
      }

    } catch (err) {
      console.error('❌ Error changing password:', err)
      setError('Failed to change password')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    reset()
    setError(null)
    setSuccess(false)
    setLoading(false)
    setTabValue(0)
    onClose()
  }

  if (!user) return null

  return (
    <Drawer
      open={open}
      anchor='right'
      variant='temporary'
      onClose={handleClose}
      ModalProps={{
        keepMounted: true
      }}
      sx={{ '& .MuiDrawer-paper': { width: { xs: 300, sm: 400 } } }}
    >
      <div className='flex items-center justify-between p-6'>
        <Typography variant='h5'>Edit User</Typography>
        <IconButton size='small' onClick={handleClose}>
          <i className='tabler-x' />
        </IconButton>
      </div>

      <Divider />

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label='Profile' />
          <Tab label='Security' />
        </Tabs>
      </Box>

      <div className='p-6'>
        {/* Success Alert */}
        {success && (
          <Alert severity='success' className='mb-4'>
            {tabValue === 0 ? 'User updated successfully!' : 'Password changed successfully!'}
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert severity='error' className='mb-4'>
            {error}
          </Alert>
        )}

        {/* Profile Tab */}
        {tabValue === 0 && (
          <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-4'>
            {/* User Info */}
            <div className='flex items-center gap-3 mb-4 p-4 bg-gray-50 rounded'>
              <div className='flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white font-medium'>
                {user.username?.charAt(0)?.toUpperCase() || 'U'}
              </div>
              <div>
                <Typography variant='body1' className='font-medium'>
                  {user.username}
                </Typography>
                <Typography variant='caption' color='text.secondary'>
                  ID: {user.adminId}
                </Typography>
              </div>
            </div>

            {/* Username */}
            <Controller
              name='username'
              control={control}
              rules={{
                required: 'Username is required',
                minLength: {
                  value: 2,
                  message: 'Username must be at least 2 characters'
                }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Username'
                  placeholder='Enter username'
                  error={!!errors.username}
                  helperText={errors.username?.message}
                  disabled={loading}
                />
              )}
            />

            {/* Email */}
            <Controller
              name='email'
              control={control}
              rules={{
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Email'
                  placeholder='Enter email address'
                  type='email'
                  error={!!errors.email}
                  helperText={errors.email?.message}
                  disabled={loading}
                />
              )}
            />

            {/* Role */}
            <Controller
              name='role'
              control={control}
              rules={{ required: 'Role is required' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.role} disabled={loading}>
                  <InputLabel>Role</InputLabel>
                  <Select {...field} label='Role'>
                    <MenuItem value='super_admin'>Super Admin</MenuItem>
                    <MenuItem value='normal_user'>Normal User</MenuItem>
                  </Select>
                  {errors.role && (
                    <Typography variant='caption' color='error' className='mt-1 ml-3'>
                      {errors.role.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />

            {/* Company */}
            <Controller
              name='company'
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Company'
                  placeholder='Enter company name'
                  disabled={loading}
                />
              )}
            />

            {/* Active Status */}
            <Controller
              name='isActive'
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Switch
                      {...field}
                      checked={field.value}
                      disabled={loading}
                    />
                  }
                  label='Active User'
                />
              )}
            />

            {/* Action Buttons */}
            <div className='flex gap-4 mt-6'>
              <Button
                fullWidth
                variant='contained'
                type='submit'
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <i className='tabler-check' />}
              >
                {loading ? 'Updating...' : 'Update User'}
              </Button>
              <Button
                fullWidth
                variant='tonal'
                color='secondary'
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}

        {/* Security Tab */}
        {tabValue === 1 && (
          <form onSubmit={handleSubmit(handlePasswordChange)} className='flex flex-col gap-4'>
            {/* Current Password Info */}
            <div className='p-4 bg-warning/10 rounded mb-4'>
              <Typography variant='body2' color='warning.main' className='font-medium mb-2'>
                <i className='tabler-shield-lock mr-2' />
                Password Change
              </Typography>
              <Typography variant='caption' color='text.secondary'>
                Enter a new password for this user. The user will need to use this new password for their next login.
              </Typography>
            </div>

            {/* New Password */}
            <Controller
              name='newPassword'
              control={control}
              rules={{
                minLength: {
                  value: 6,
                  message: 'Password must be at least 6 characters'
                }
              }}
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='New Password'
                  placeholder='Enter new password'
                  type={showPassword ? 'text' : 'password'}
                  error={!!errors.newPassword}
                  helperText={errors.newPassword?.message}
                  disabled={loading}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position='end'>
                        <IconButton
                          size='small'
                          onClick={() => setShowPassword(!showPassword)}
                          edge='end'
                        >
                          <i className={showPassword ? 'tabler-eye-off' : 'tabler-eye'} />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              )}
            />

            {/* Action Buttons */}
            <div className='flex gap-4 mt-6'>
              <Button
                fullWidth
                variant='contained'
                color='warning'
                type='submit'
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <i className='tabler-key' />}
              >
                {loading ? 'Changing...' : 'Change Password'}
              </Button>
              <Button
                fullWidth
                variant='tonal'
                color='secondary'
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
            </div>
          </form>
        )}
      </div>
    </Drawer>
  )
}

export default EditUserDrawer
