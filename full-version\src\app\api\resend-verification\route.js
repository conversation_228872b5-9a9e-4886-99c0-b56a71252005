// Next Imports
import { NextResponse } from 'next/server'

export async function POST(req) {
  try {
    const body = await req.json()
    const { email } = body

    if (!email) {
      return NextResponse.json(
        { success: false, message: 'Email is required' },
        { status: 400 }
      )
    }

    // Forward the request to your backend
    const backendUrl = process.env.API_URL || 'http://localhost:8090'
    const resendUrl = `${backendUrl}/resend-verification`

    const response = await fetch(resendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email })
    })

    const data = await response.json()

    if (response.ok) {
      return NextResponse.json(
        { success: true, message: 'Verification email sent successfully' },
        { status: 200 }
      )
    } else {
      return NextResponse.json(
        { success: false, message: data.message || 'Failed to send verification email' },
        { status: response.status }
      )
    }
  } catch (error) {
    console.error('Error resending verification email:', error)

    return NextResponse.json(
      { success: false, message: 'An error occurred while resending verification email' },
      { status: 500 }
    )
  }
}