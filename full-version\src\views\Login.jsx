'use client'

// React Imports
import { useState, useEffect } from 'react'

// Next Imports
import Link from 'next/link'
import { useParams, useRouter } from 'next/navigation'

// MUI Imports
import useMediaQuery from '@mui/material/useMediaQuery'
import { styled, useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import Checkbox from '@mui/material/Checkbox'
import Button from '@mui/material/Button'
import FormControlLabel from '@mui/material/FormControlLabel'

import Alert from '@mui/material/Alert'

// Third-party Imports
import { signIn, signOut, getSession } from 'next-auth/react'
import { Controller, useForm } from 'react-hook-form'
import { valibotResolver } from '@hookform/resolvers/valibot'
import { email, object, minLength, string, pipe, nonEmpty } from 'valibot'
import classnames from 'classnames'

// Component Imports
import Logo from '@components/layout/shared/Logo'
import CustomTextField from '@core/components/mui/TextField'
import TextField from '@mui/material/TextField'

// Config Imports
import themeConfig from '@configs/themeConfig'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'
import { useSettings } from '@core/hooks/useSettings'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

// Styled Custom Components
const LoginIllustration = styled('img')(({ theme }) => ({
  zIndex: 2,
  blockSize: 'auto',
  maxBlockSize: 680,
  maxInlineSize: '100%',
  margin: theme.spacing(12),
  [theme.breakpoints.down(1536)]: {
    maxBlockSize: 550
  },
  [theme.breakpoints.down('lg')]: {
    maxBlockSize: 450
  }
}))

const MaskImg = styled('img')({
  blockSize: 'auto',
  maxBlockSize: 355,
  inlineSize: '100%',
  position: 'absolute',
  insetBlockEnd: 0,
  zIndex: -1
})

const schema = object({
  username: pipe(string(), minLength(1, 'Username is required')),
  email: pipe(string(), minLength(1, 'Email is required'), email('Email is invalid')),
  password: pipe(
    string(),
    nonEmpty('Password is required'),
    minLength(5, 'Password must be at least 5 characters long')
  )
})

const Login = ({ mode }) => {
  // States
  const [isPasswordShown, setIsPasswordShown] = useState(false)
  const [errorState, setErrorState] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  // OTP verification states
  const [showOTPInput, setShowOTPInput] = useState(false)
  const [otp, setOtp] = useState('')
  const [otpError, setOtpError] = useState('')
  const [userForOTP, setUserForOTP] = useState(null)
  const [isVerifyingOTP, setIsVerifyingOTP] = useState(false)


  // Vars
  const darkImg = '/images/pages/auth-mask-dark.png'
  const lightImg = '/images/pages/auth-mask-light.png'
  const darkIllustration = '/images/illustrations/auth/v2-login-dark.png'
  const lightIllustration = '/images/illustrations/auth/v2-login-light.png'
  const borderedDarkIllustration = '/images/illustrations/auth/v2-login-dark-border.png'
  const borderedLightIllustration = '/images/illustrations/auth/v2-login-light-border.png'

  // Hooks
  const router = useRouter()
  const { lang: locale } = useParams()
  const { settings } = useSettings()
  const theme = useTheme()
  const hidden = useMediaQuery(theme.breakpoints.down('md'))
  const authBackground = useImageVariant(mode, lightImg, darkImg)

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    resolver: valibotResolver(schema),
    defaultValues: {
      username: '',
      email: '',
      password: '' // Default values for convenience
    }
  })

  const characterIllustration = useImageVariant(
    mode,
    lightIllustration,
    darkIllustration,
    borderedLightIllustration,
    borderedDarkIllustration
  )

  const handleClickShowPassword = () => setIsPasswordShown(show => !show)

  // CRITICAL SECURITY FIX: Clear any existing session when login page loads
  useEffect(() => {
    const clearExistingSession = async () => {
      try {
        console.log('🔐 Login page loaded - clearing any existing session for security')
        await signOut({ redirect: false })
      } catch (error) {
        console.log('Session clear error (expected if no session):', error)
      }
    }

    clearExistingSession()
  }, [])

  // Debug: Log form values
  console.log('Form errors:', errors)
  console.log('Form control:', control)

  const onSubmit = async data => {
    setIsLoading(true)
    setErrorState(null)
    setOtpError('')

    try {
      // Step 1: Call backend login API directly to check if OTP is required
      console.log('🔐 Attempting login with backend API...')

      const backendResponse = await fetch('http://localhost:8090/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: data.username,
          email: data.email,
          password: data.password
        })
      })

      const backendResult = await backendResponse.json()
      console.log('🔍 Backend login response:', backendResult)

      if (backendResponse.ok && backendResult.success) {
        // Check if Email OTP is required
        if (backendResult.requiresEmailOTP) {
          console.log('📧 Email OTP required, showing OTP input')

          // Show OTP input instead of redirecting
          setUserForOTP({
            id: backendResult.user.id,
            email: backendResult.user.email,
            username: backendResult.user.username
          })
          setShowOTPInput(true)
          setIsLoading(false)
          return
        }

        // Check if MFA is required
        if (backendResult.requiresMFA) {
          console.log('🔐 MFA required, redirecting to MFA verification')

          // Store user info in localStorage for MFA verification
          localStorage.setItem('mfa_user_email', data.email)
          localStorage.setItem('mfa_user_username', data.username)

          const params = new URLSearchParams({
            email: data.email,
            username: data.username
          })
          router.push(`/pages/auth/mfa-verify?${params.toString()}`)
          return
        }

        // If no additional verification required, proceed with NextAuth
        console.log('✅ No additional verification required, proceeding with NextAuth')

        const res = await signIn('credentials', {
          username: data.username,
          email: data.email,
          password: data.password,
          redirect: false
        })

        if (res && res.ok && res.error === null) {
          console.log('✅ Login successful, redirecting to profile')
          router.push(getLocalizedUrl('/pages/user-profile', locale))
        } else {
          setErrorState({ message: ['Authentication failed. Please try again.'] })
        }
      } else {
        // Backend login failed
        setErrorState({
          message: [backendResult.message || 'Invalid credentials. Please check your username, email, and password.']
        })
      }
    } catch (error) {
      console.error('Login error:', error)
      setErrorState({ message: ['An error occurred during login. Please try again.'] })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOTPVerification = async () => {
    if (!otp || otp.length !== 6) {
      setOtpError('Please enter a valid 6-digit OTP code')
      return
    }

    if (!userForOTP) {
      setOtpError('User information not found. Please try logging in again.')
      return
    }

    setIsVerifyingOTP(true)
    setOtpError('')

    try {
      console.log('🔍 Verifying OTP:', { userId: userForOTP.id, otp: parseInt(otp) })

      // Verify OTP with backend
      const response = await fetch('http://localhost:8090/login/verify-email-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userForOTP.id,
          otp: parseInt(otp)
        })
      })

      const result = await response.json()
      console.log('🔍 OTP verification response:', result)

      if (response.ok && result.success) {
        console.log('✅ OTP verified successfully, proceeding with NextAuth')

        // OTP verified, now complete the login with NextAuth
        const res = await signIn('credentials', {
          userId: userForOTP.id, // Pass user ID for profile lookup
          username: userForOTP.username,
          email: userForOTP.email,
          password: 'verified', // Special flag to indicate OTP was verified
          step: 'email-verified',
          redirect: false
        })

        if (res && res.ok && res.error === null) {
          console.log('✅ Login completed successfully')
          router.push(getLocalizedUrl('/pages/user-profile', locale))
        } else {
          setOtpError('Failed to complete login after OTP verification. Please try again.')
        }
      } else {
        setOtpError(result.message || 'Invalid OTP. Please try again.')
      }
    } catch (error) {
      console.error('OTP verification error:', error)
      setOtpError('An error occurred during OTP verification. Please try again.')
    } finally {
      setIsVerifyingOTP(false)
    }
  }

  const handleResendOTP = async () => {
    if (!userForOTP) {
      setOtpError('User information not found. Please try logging in again.')
      return
    }

    setIsVerifyingOTP(true)
    setOtpError('')

    try {
      // Trigger a new login to resend OTP
      const response = await fetch('http://localhost:8090/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: userForOTP.username,
          email: userForOTP.email,
          password: 'resend' // This will fail but trigger OTP resend
        })
      })

      // Even if login fails, OTP should be resent
      setOtpError('')
      alert('New OTP sent to your email. Please check your inbox.')
    } catch (error) {
      console.error('Resend OTP error:', error)
      setOtpError('Failed to resend OTP. Please try again.')
    } finally {
      setIsVerifyingOTP(false)
    }
  }

  const handleBackToLogin = () => {
    setShowOTPInput(false)
    setOtp('')
    setOtpError('')
    setUserForOTP(null)
  }

  return (
    <div className='flex bs-full justify-center'>
      <div
        className={classnames(
          'flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden',
          {
            'border-ie': settings.skin === 'bordered'
          }
        )}
      >
        <LoginIllustration src={characterIllustration} alt='character-illustration' />
        {!hidden && <MaskImg alt='mask' src={authBackground} />}
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <div className='absolute block-start-5 sm:block-start-[33px] inline-start-6 sm:inline-start-[38px]'>
          <Logo />
        </div>
        <div className='flex flex-col gap-6 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset] mbs-8 sm:mbs-11 md:mbs-0'>
          <div className='flex flex-col gap-1'>
            <Typography variant='h4'>
              {showOTPInput ? 'Email OTP Verification 📧' : `Welcome to ${themeConfig.templateName}! 👋🏻`}
            </Typography>
            <Typography>
              {showOTPInput
                ? `We've sent a 6-digit verification code to ${userForOTP?.email}`
                : 'Please sign-in to your account and start the adventure'
              }
            </Typography>
          </div>

          {!showOTPInput && (
            <Alert icon={false} className='bg-[var(--mui-palette-primary-lightOpacity)]'>
              <Typography variant='body2' color='primary.main'>
                Username: <span className='font-medium'>admin</span> / Email: <span className='font-medium'><EMAIL></span> / Pass:{' '}
                <span className='font-medium'>admin</span>
              </Typography>
            </Alert>
          )}

          {/* Error Display */}
          {(errorState || otpError) && (
            <Alert severity='error' onClose={() => {
              setErrorState(null)
              setOtpError('')
            }}>
              {errorState?.message?.[0] || otpError}
            </Alert>
          )}
          {/* Conditional Form Display */}
          {!showOTPInput ? (
            /* LOGIN FORM */
            <form
              noValidate
              autoComplete='off'
              onSubmit={handleSubmit(onSubmit)}
              className='flex flex-col gap-6'
            >
              {/* USERNAME FIELD */}
              <Controller
                name='username'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    autoFocus
                    type='text'
                    label='Username'
                    placeholder='Enter your username'
                    variant='outlined'
                    onChange={e => {
                      field.onChange(e.target.value)
                      errorState !== null && setErrorState(null)
                    }}
                    error={!!errors.username}
                    helperText={errors.username?.message}
                  />
                )}
              />

              {/* EMAIL FIELD */}
              <Controller
                name='email'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    type='email'
                    label='Email'
                    placeholder='Enter your email'
                    onChange={e => {
                      field.onChange(e.target.value)
                      errorState !== null && setErrorState(null)
                    }}
                    error={!!errors.email}
                    helperText={errors?.email?.message}
                  />
                )}
              />

              {/* PASSWORD FIELD */}
              <Controller
                name='password'
                control={control}
                render={({ field }) => (
                  <CustomTextField
                    {...field}
                    fullWidth
                    label='Password'
                    placeholder='············'
                    id='login-password'
                    type={isPasswordShown ? 'text' : 'password'}
                    onChange={e => {
                      field.onChange(e.target.value)
                      errorState !== null && setErrorState(null)
                    }}
                    slotProps={{
                      input: {
                        endAdornment: (
                          <InputAdornment position='end'>
                            <IconButton
                              edge='end'
                              onClick={handleClickShowPassword}
                              onMouseDown={e => e.preventDefault()}
                            >
                              <i className={isPasswordShown ? 'tabler-eye' : 'tabler-eye-off'} />
                            </IconButton>
                          </InputAdornment>
                        )
                      }
                    }}
                    {...(errors.password && { error: true, helperText: errors.password.message })}
                  />
                )}
              />
              <div className='flex justify-between items-center gap-x-3 gap-y-1 flex-wrap'>
                <FormControlLabel control={<Checkbox defaultChecked />} label='Remember me' />
                <Typography
                  className='text-end'
                  color='primary.main'
                  component={Link}
                  href={getLocalizedUrl('/forgot-password', locale)}
                >
                  Forgot password?
                </Typography>
              </div>
              <Button
                fullWidth
                variant='contained'
                type='submit'
                disabled={isLoading}
              >
                {isLoading ? 'Logging in...' : 'Login'}
              </Button>
            </form>
          ) : (
            /* OTP VERIFICATION FORM */
            <div className='flex flex-col gap-6'>
              <CustomTextField
                fullWidth
                autoFocus
                label='Enter 6-digit OTP'
                placeholder='123456'
                value={otp}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                  setOtp(value)
                  setOtpError('')
                }}
                inputProps={{
                  maxLength: 6,
                  style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
                }}
                disabled={isVerifyingOTP}
                error={!!otpError}
                helperText={otpError}
              />

              <Button
                fullWidth
                variant='contained'
                onClick={handleOTPVerification}
                disabled={isVerifyingOTP || otp.length !== 6}
              >
                {isVerifyingOTP ? 'Verifying...' : 'Verify OTP'}
              </Button>

              <Button
                fullWidth
                variant='outlined'
                onClick={handleResendOTP}
                disabled={isVerifyingOTP}
              >
                Resend OTP
              </Button>

              <Button
                fullWidth
                variant='text'
                onClick={handleBackToLogin}
                disabled={isVerifyingOTP}
              >
                Back to Login
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Login
