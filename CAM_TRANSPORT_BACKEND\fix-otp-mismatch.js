const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function fixOTPMismatch() {
    try {
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log('🔗 Connected to MongoDB');
        
        // Find the user
        const user = await Login.findOne({
            email: '<EMAIL>'
        });
        
        if (!user) {
            console.log('❌ User not found');
            return;
        }
        
        console.log('👤 Current user OTP status:');
        console.log(`Current OTP in DB: ${user.otp}`);
        console.log(`OTP from email: 774940`);
        
        // Update with the correct OTP that was sent via email
        const emailOTP = 774940;
        const newExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
        
        user.otp = emailOTP;
        user.otpExpiry = newExpiry;
        await user.save();
        
        console.log('✅ Fixed OTP mismatch!');
        console.log(`✅ Database OTP updated to: ${emailOTP}`);
        console.log(`✅ New expiry: ${newExpiry}`);
        console.log('\n🎯 You can now use OTP 774940 to verify!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
    }
}

fixOTPMismatch();
