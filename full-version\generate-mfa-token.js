const speakeasy = require('speakeasy');

// Fixed secret used by the backend for admin user
const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';

console.log('🔐 MFA Token Generator for Admin User');
console.log('=====================================');
console.log(`Secret: ${ADMIN_SECRET}`);
console.log('');

// Generate current TOTP token
const token = speakeasy.totp({
    secret: ADMIN_SECRET,
    encoding: 'base32'
});

console.log(`Current TOTP Token: ${token}`);
console.log(`Token Length: ${token.length}`);
console.log('');

// Test verification
const verified = speakeasy.totp.verify({
    secret: ADMIN_SECRET,
    encoding: 'base32',
    token: token,
    window: 2
});

console.log(`✅ Token verification test: ${verified ? 'PASS' : 'FAIL'}`);
console.log('');

console.log('📱 To set up your authenticator app:');
console.log('1. Open your authenticator app (Google Authenticator, Authy, etc.)');
console.log('2. Add a new account manually');
console.log('3. Enter this secret: JBSWY3DPEHPK3PXP');
console.log('4. Account name: CAM Transport Admin');
console.log('5. Issuer: CAM Transport');
console.log('');
console.log('🧪 For testing, use this current token in the MFA verification page.');
console.log('Note: TOTP tokens change every 30 seconds, so generate a new one if this expires.');

// Generate a few tokens for the next minute
console.log('');
console.log('📅 Next few tokens (for testing):');
for (let i = 0; i < 3; i++) {
    const futureTime = Math.floor(Date.now() / 1000) + (i * 30);
    const futureToken = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        time: futureTime
    });
    const timeStr = new Date(futureTime * 1000).toLocaleTimeString();
    console.log(`${timeStr}: ${futureToken}`);
}

// Keep generating tokens every 5 seconds for real-time testing
console.log('');
console.log('🔄 Real-time token generation (press Ctrl+C to stop):');
setInterval(() => {
    const currentToken = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32'
    });
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[${timestamp}] Current Token: ${currentToken}`);
}, 5000);
