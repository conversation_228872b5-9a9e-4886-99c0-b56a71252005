'use client'

// React Imports
import { useState } from 'react'

// NextAuth Imports
import { useSession } from 'next-auth/react'

// MUI Imports
import Drawer from '@mui/material/Drawer'
import Typography from '@mui/material/Typography'
import TextField from '@mui/material/TextField'
import Button from '@mui/material/Button'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import Select from '@mui/material/Select'
import MenuItem from '@mui/material/MenuItem'
import IconButton from '@mui/material/IconButton'
import Divider from '@mui/material/Divider'
import Alert from '@mui/material/Alert'
import CircularProgress from '@mui/material/CircularProgress'
import InputAdornment from '@mui/material/InputAdornment'

// Hook Imports
import { useForm, Controller } from 'react-hook-form'

const AddUserDrawer = ({ open, onClose, onUserAdded }) => {
  // Session
  const { data: session } = useSession()

  // States
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  // Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    defaultValues: {
      username: '',
      email: '',
      password: '',
      role: 'admin',
      company: 'CAM Transport ltd.'
    }
  })

  // Handlers
  const onSubmit = async (data) => {
    try {
      setLoading(true)
      setError(null)
      setSuccess(false)

      if (!session?.user?.id) {
        setError('Authentication required. Please login.')
        setLoading(false)
        return
      }

      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'
      const response = await fetch(`${API_BASE_URL}/user-profile/users/create?userId=${session.user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        setSuccess(true)
        reset()
        onUserAdded?.()

        // Auto close after success
        setTimeout(() => {
          handleClose()
        }, 2000)
      } else {
        setError(result.message || 'Failed to create user')
      }

    } catch (err) {
      console.error('❌ Error creating user:', err)
      setError('Failed to create user')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    reset()
    setError(null)
    setSuccess(false)
    setLoading(false)
    onClose()
  }

  return (
    <Drawer
      open={open}
      anchor='right'
      variant='temporary'
      onClose={handleClose}
      ModalProps={{
        keepMounted: true
      }}
      sx={{ '& .MuiDrawer-paper': { width: { xs: 300, sm: 400 } } }}
    >
      <div className='flex items-center justify-between p-6'>
        <Typography variant='h5'>Add New User</Typography>
        <IconButton size='small' onClick={handleClose}>
          <i className='tabler-x' />
        </IconButton>
      </div>

      <Divider />

      <div className='p-6'>
        {/* Success Alert */}
        {success && (
          <Alert severity='success' className='mb-4'>
            User created successfully! Verification email sent.
          </Alert>
        )}

        {/* Error Alert */}
        {error && (
          <Alert severity='error' className='mb-4'>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className='flex flex-col gap-4'>
          {/* Username */}
          <Controller
            name='username'
            control={control}
            rules={{
              required: 'Username is required',
              minLength: {
                value: 2,
                message: 'Username must be at least 2 characters'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='Username'
                placeholder='Enter username'
                error={!!errors.username}
                helperText={errors.username?.message}
                disabled={loading}
              />
            )}
          />

          {/* Email */}
          <Controller
            name='email'
            control={control}
            rules={{
              required: 'Email is required',
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='Email'
                placeholder='Enter email address'
                type='email'
                error={!!errors.email}
                helperText={errors.email?.message}
                disabled={loading}
              />
            )}
          />

          {/* Password */}
          <Controller
            name='password'
            control={control}
            rules={{
              required: 'Password is required',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='Password'
                placeholder='Enter password'
                type={showPassword ? 'text' : 'password'}
                error={!!errors.password}
                helperText={errors.password?.message}
                disabled={loading}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position='end'>
                      <IconButton
                        size='small'
                        onClick={() => setShowPassword(!showPassword)}
                        edge='end'
                      >
                        <i className={showPassword ? 'tabler-eye-off' : 'tabler-eye'} />
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
            )}
          />

          {/* Role */}
          <Controller
            name='role'
            control={control}
            rules={{ required: 'Role is required' }}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.role} disabled={loading}>
                <InputLabel>Role</InputLabel>
                <Select {...field} label='Role'>
                  <MenuItem value='super_admin'>Super Admin</MenuItem>
                  <MenuItem value='normal_user'>Normal User</MenuItem>
                </Select>
                {errors.role && (
                  <Typography variant='caption' color='error' className='mt-1 ml-3'>
                    {errors.role.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />

          {/* Company */}
          <Controller
            name='company'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label='Company'
                placeholder='Enter company name'
                disabled={loading}
              />
            )}
          />

          {/* Action Buttons */}
          <div className='flex gap-4 mt-6'>
            <Button
              fullWidth
              variant='contained'
              type='submit'
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <i className='tabler-check' />}
            >
              {loading ? 'Creating...' : 'Create User'}
            </Button>
            <Button
              fullWidth
              variant='tonal'
              color='secondary'
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>

        {/* Info */}
        <div className='mt-6 p-4 bg-primary/10 rounded'>
          <Typography variant='body2' color='primary' className='font-medium mb-2'>
            <i className='tabler-info-circle mr-2' />
            Important Notes
          </Typography>
          <Typography variant='caption' color='text.secondary'>
            • A verification email will be sent to the user<br />
            • User must verify their email before logging in<br />
            • Default company is set to "CAM Transport ltd."<br />
            • Password must be at least 6 characters long
          </Typography>
        </div>
      </div>
    </Drawer>
  )
}

export default AddUserDrawer
