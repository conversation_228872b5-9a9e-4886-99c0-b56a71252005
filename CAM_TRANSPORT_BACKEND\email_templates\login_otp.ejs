<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Admin Login Verification OTP</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            line-height: 1.6;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }

        .content {
            padding: 40px 30px;
        }

        .otp-section {
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 25px;
            text-align: center;
            margin: 25px 0;
        }

        .otp-code {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            letter-spacing: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }

        .otp-label {
            font-size: 14px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }

        .details-section {
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .label {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .value {
            color: #666;
            font-size: 14px;
            word-break: break-all;
        }

        .verification-link {
            background: #667eea;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 6px;
            display: inline-block;
            margin: 20px 0;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }

        .verification-link:hover {
            background: #5a6fd8;
            color: white;
            text-decoration: none;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            font-size: 14px;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
        }

        .timestamp {
            color: #999;
            font-size: 12px;
            margin-top: 15px;
        }

        @media (max-width: 600px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }

            .header,
            .content {
                padding: 20px;
            }

            .otp-code {
                font-size: 28px;
                letter-spacing: 4px;
            }

            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Admin Login Verification</h1>
            <p>CAM Transport Backend System</p>
        </div>

        <div class="content">
            <h2 style="color: #333; margin-top: 0;">New Admin Login Attempt</h2>
            <p style="color: #666; font-size: 16px;">
                A new admin login attempt has been detected. Please verify this login using the OTP below.
            </p>

            <div class="otp-section">
                <div class="otp-label">Verification Code</div>
                <div class="otp-code">
                    <%= otp %>
                </div>
                <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">
                    Valid for 10 minutes
                </p>
            </div>

            <div class="details-section">
                <h3 style="margin-top: 0; color: #333; font-size: 18px;">Login Details</h3>

                <div class="detail-row">
                    <span class="label">User Email:</span>
                    <span class="value">
                        <%= userEmail %>
                    </span>
                </div>

                <div class="detail-row">
                    <span class="label">User ID:</span>
                    <span class="value">
                        <%= userId %>
                    </span>
                </div>

                <div class="detail-row">
                    <span class="label">Timestamp:</span>
                    <span class="value">
                        <%= new Date().toLocaleString() %>
                    </span>
                </div>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <p style="color: #666; margin-bottom: 15px;">
                    Click the button below to verify this login attempt:
                </p>
                <a href="<%= verificationLink %>" class="verification-link">
                    Verify Login
                </a>
            </div>

            <div class="warning">
                <strong>⚠️ Security Notice:</strong> If you did not attempt to log in, please ignore this email and
                consider changing your password. This OTP will expire in 10 minutes for security purposes.
            </div>

            <div class="timestamp">
                Email generated on: <%= new Date().toLocaleString() %>
            </div>
        </div>

        <div class="footer">
            <p>
                <strong>CAM Transport Ltd.</strong><br>
                Regina, SK S4M 0A1 | <EMAIL> | +1 (306) 810-0100
            </p>
            <p style="margin-top: 10px;">
                This is an automated security notification. Please do not reply to this email.
            </p>
        </div>
    </div>
</body>

</html>