const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/CAM_TRANSPORT_SYSTEM');

// Define the Login schema
const LoginSchema = new mongoose.Schema({
    username: String,
    email: String,
    password: String,
    mfaSecret: String,
    mfaEnabled: <PERSON><PERSON><PERSON>,
    isVerified: <PERSON><PERSON><PERSON>,
    role: String,
    mfaDevices: Array,
    backupCodes: Array
}, { timestamps: true });

const Login = mongoose.model('Login', LoginSchema);

async function disableMFADirect() {
    console.log('🔧 DISABLING MFA DIRECTLY IN DATABASE');
    console.log('=====================================\n');
    
    const userId = '685923aa3135ef8ef080a6fe';
    
    try {
        // Find the user by ObjectId
        const user = await Login.findById(userId);
        
        if (!user) {
            console.log('❌ User not found with ID:', userId);
            process.exit(1);
        }
        
        console.log('✅ User found:');
        console.log(`   ID: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Current MFA Enabled: ${user.mfaEnabled}`);
        console.log(`   Current MFA Secret: ${user.mfaSecret ? 'EXISTS' : 'null'}`);
        
        // Disable MFA completely
        console.log('\n🔧 Disabling MFA...');
        user.mfaEnabled = false;
        user.mfaSecret = null;
        user.mfaDevices = [];
        user.backupCodes = [];
        
        await user.save();
        
        console.log('✅ MFA DISABLED SUCCESSFULLY!');
        console.log('\n🎯 VERIFICATION:');
        console.log(`   MFA Enabled: ${user.mfaEnabled}`);
        console.log(`   MFA Secret: ${user.mfaSecret || 'null'}`);
        console.log(`   MFA Devices: ${user.mfaDevices.length} devices`);
        console.log(`   Backup Codes: ${user.backupCodes.length} codes`);
        
        console.log('\n✅ YOU CAN NOW LOG IN WITHOUT MFA!');
        console.log('   Username: dhruv');
        console.log('   Email: <EMAIL>');
        console.log('   Password: dhruv@123');
        console.log('   No MFA code required!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        mongoose.connection.close();
    }
}

disableMFADirect();
