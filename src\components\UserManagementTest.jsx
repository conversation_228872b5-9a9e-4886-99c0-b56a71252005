'use client'

import { useState } from 'react'
import { 
  Card, 
  CardContent, 
  Typography, 
  Button, 
  Box, 
  Alert,
  Chip
} from '@mui/material'
import { getCurrentUser, isSuperAdmin, hasPermission } from '@/utils/auth'

const UserManagementTest = () => {
  const [testResults, setTestResults] = useState(null)

  const runTests = () => {
    const user = getCurrentUser()
    const isSuper = isSuperAdmin()
    const permissions = {
      canViewUsers: hasPermission('canViewUsers'),
      canCreateUsers: hasPermission('canCreateUsers'),
      canEditUsers: hasPermission('canEditUsers'),
      canDeleteUsers: hasPermission('canDeleteUsers'),
      canManageRoles: hasPermission('canManageRoles'),
    }

    setTestResults({
      user,
      isSuper,
      permissions
    })
  }

  return (
    <Card sx={{ m: 2 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          User Management System Test
        </Typography>
        
        <Button variant="contained" onClick={runTests} sx={{ mb: 2 }}>
          Run Authentication Test
        </Button>

        {testResults && (
          <Box>
            <Alert severity={testResults.isSuper ? 'success' : 'warning'} sx={{ mb: 2 }}>
              <Typography variant="subtitle2">
                Current User: {testResults.user?.username || 'None'}
              </Typography>
              <Typography variant="body2">
                Role: {testResults.user?.role || 'None'}
              </Typography>
              <Typography variant="body2">
                Super Admin: {testResults.isSuper ? 'Yes' : 'No'}
              </Typography>
            </Alert>

            <Typography variant="subtitle2" gutterBottom>
              Permissions:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {Object.entries(testResults.permissions).map(([permission, hasAccess]) => (
                <Chip
                  key={permission}
                  label={permission}
                  color={hasAccess ? 'success' : 'default'}
                  size="small"
                />
              ))}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default UserManagementTest
