{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_af0622be._.js", "server/edge/chunks/[root-of-the-server]__5e96eddc._.js", "server/edge/chunks/edge-wrapper_a0335704.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "3KGK2IZEJivyYixf6KYUDaQdI235ZnG7L25SngUYmG8=", "__NEXT_PREVIEW_MODE_ID": "6e70b1ac56577953d4420ed98bf6888b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "eedc812cdcd53f5eef144028d9574eb1d3e3e834be77f3c791b9754a297e1b4a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c8b1388ecbac304429981259054fb563a03f7bc5719d63806d3450a86600bda3"}}}, "sortedMiddleware": ["/"], "functions": {}}