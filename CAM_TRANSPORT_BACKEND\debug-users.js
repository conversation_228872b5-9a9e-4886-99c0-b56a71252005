const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const Login = require('./model/Login');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cam_transport');

async function debugUsers() {
  try {
    console.log('🔍 Debugging users in database...');
    
    // Check connection
    console.log('📡 MongoDB connection state:', mongoose.connection.readyState);
    
    // Get all users with their ObjectIds
    const allUsers = await Login.find({}).select('_id username email role isActive isVerified adminId');
    
    console.log(`📊 Total users found: ${allUsers.length}`);
    
    if (allUsers.length > 0) {
      console.log('\n👥 All users with ObjectIds:');
      allUsers.forEach((user, index) => {
        console.log(`${index + 1}. ObjectId: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Admin ID: ${user.adminId}`);
        console.log(`   Active: ${user.isActive}`);
        console.log(`   Verified: ${user.isVerified}`);
        console.log('');
      });
    } else {
      console.log('❌ No users found in database!');
      console.log('🔧 Creating a super admin user...');
      
      // Create super admin user
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const superAdmin = new Login({
        username: 'Super Admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'super_admin',
        isActive: true,
        isVerified: true,
        adminId: 'ADMIN001',
        company: 'CAM Transport'
      });
      
      const savedUser = await superAdmin.save();
      console.log('✅ Super admin created successfully!');
      console.log(`   ObjectId: ${savedUser._id}`);
      console.log(`   Email: ${savedUser.email}`);
      console.log(`   Role: ${savedUser.role}`);
    }
    
    // Check specifically for super_admin role
    const superAdmins = await Login.find({ role: 'super_admin' });
    console.log(`\n👑 Users with role 'super_admin': ${superAdmins.length}`);
    
    if (superAdmins.length > 0) {
      console.log('\n👑 Super Admin users:');
      superAdmins.forEach((user, index) => {
        console.log(`${index + 1}. ObjectId: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log('');
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

debugUsers();
