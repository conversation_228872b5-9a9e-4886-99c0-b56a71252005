const axios = require('axios');

async function testOTPVerification() {
    console.log('🧪 Testing OTP Verification with email OTP...');
    
    const userId = '685923aa3135ef8ef080a6fe';
    const emailOTP = 774940;
    
    console.log(`User ID: ${userId}`);
    console.log(`Email OTP: ${emailOTP}`);
    
    try {
        const response = await axios.post('http://localhost:8090/login/verify-email-otp', {
            userId: userId,
            otp: emailOTP
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ OTP Verification Success!');
        console.log('Status:', response.status);
        console.log('Response:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ OTP Verification Failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Error Response:', JSON.stringify(error.response.data, null, 2));
            
            // Let's also check what's in the database
            console.log('\n🔍 Let me check the database directly...');
            
            // Try to trigger a new login to see what O<PERSON> gets generated
            try {
                const loginResponse = await axios.post('http://localhost:8090/login', {
                    username: 'dhruv',
                    email: '<EMAIL>',
                    password: 'dhruv@123'
                }, {
                    headers: { 'Content-Type': 'application/json' }
                });
                
                console.log('📧 New login triggered - check your email for new OTP');
                console.log('Login response:', JSON.stringify(loginResponse.data, null, 2));
                
            } catch (loginError) {
                console.log('❌ Login failed:', loginError.response?.data || loginError.message);
            }
            
        } else {
            console.log('Network Error:', error.message);
        }
    }
}

testOTPVerification();
