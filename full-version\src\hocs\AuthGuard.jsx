// Third-party Imports
import { getServerSession } from 'next-auth'
import { redirect } from 'next/navigation'

// Component Imports
import AuthRedirect from '@/components/AuthRedirect'

// Util Imports
import { getLocalizedUrl } from '@/utils/i18n'

export default async function AuthGuard({ children, locale }) {
  const session = await getServerSession()

  // If no session, redirect to login
  if (!session) {
    return <AuthRedirect lang={locale} />
  }

  // User is logged in, show the protected content (regardless of verification status)
  return <>{children}</>
}


