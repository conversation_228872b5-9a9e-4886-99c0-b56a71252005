const path = require('path');
const fs = require('fs');
const Urgent = require('../model/urgent_Inquiry');
const { sendEmail } = require('../service/Mailer');
const { checkUrgentEmail24hBlock, checkUrgentIp24hBlock } = require('../utils/rateLimitHelpers');
const { urgentInquiryValidationSchema } = require('../middleware/urgent_validator');

require('dotenv').config();

const hasMongoOperators = (obj) => {
    for (const key in obj) {
        if (key.startsWith("$")) {
            return true;
        }
        if (typeof obj[key] === "object" && obj[key] !== null) {
            if (hasMongoOperators(obj[key])) return true;
        }
    }
    return false;
};

const UrgentInquiry = async (req, res) => {

    if (req.body._honeypot && req.body._honeypot.length > 0) {
        return res.status(400).json({ message: "Spam detected" });
    }


    if (hasMongoOperators(req.body)) {
        return res.status(400).json({ message: "Invalid input detected." });
    }


    const { error, value } = urgentInquiryValidationSchema.validate(req.body, { abortEarly: false });
    if (error) {
        const messages = error.details.map((d) => d.message);
        return res.status(400).json({ message: messages.join(', ') });
    }

    try {
        const {
            full_name,
            email,
            phone_number,
            urgency_type,
            other_urgency,
            ref_number,
            brief_description
        } = value;

        const userIp = req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress;
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000); // For 24-hour limits
        const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 60 * 1000); // For 15-minute limit

        // --- BLOCKING LOGIC --- //

        // 1. Per-email 15-minute limit: Block after 3 submissions from the same email in 15 minutes
        const recent15mEmailCount = await Urgent.countDocuments({
            email: email.trim(),
            createdAt: { $gte: fifteenMinutesAgo }
        });
        if (recent15mEmailCount >= 3) {
            return res.status(429).json({
                message: `This email has reached the 3 submissions limit in 15 minutes for urgent inquiries. Please try again later.`
            });
        }

        // 2. Per-email 24-hour limit: Block after 15 submissions from the same email in 24 hours
        const { isBlocked: isEmail24hBlocked, unblockDate: unblockDate24h } = await checkUrgentEmail24hBlock(email, now, oneDayAgo);
        if (isEmail24hBlocked) {
            const unblockDateString24h = unblockDate24h.toLocaleString();
            return res.status(429).json({
                message: `This email has reached the 15 submissions limit in 24 hours for urgent inquiries. You can contact us again after: ${unblockDateString24h}.`
            });
        }

        // 3. IP-based 24-hour limit: Block after 5 submissions from the same IP in 24 hours
        const { isBlocked: isIp24hBlocked, unblockDate: unblockDateIp } = await checkUrgentIp24hBlock(userIp, now, oneDayAgo);
        if (isIp24hBlocked) {
            const unblockDateStringIp = unblockDateIp.toLocaleString();
            return res.status(429).json({
                message: `Too many urgent inquiries from this IP address. You can contact us again after: ${unblockDateStringIp}.`
            });
        }

        const allowedTypes = ['.doc', '.docx', '.pdf', '.png', '.jpeg', '.jpg'];
        const file = req.file;
        let documentPath = null;

        if (file) {
            const ext = path.extname(file.originalname).toLowerCase();
            if (!allowedTypes.includes(ext)) {
                fs.unlinkSync(file.path);
                return res.status(400).json({ error: 'Invalid file type.' });
            }
            documentPath = file.path;
        }

        let storedUrgency = urgency_type;
        if (urgency_type === 'Other') {
            if (other_urgency && other_urgency.trim() !== '') {
                storedUrgency = other_urgency;
            } else {
                return res.status(400).json({ error: 'Please specify the other urgency type.' });
            }
        }

        const needsRefNumber = ['Delivery Issue', 'Shipment Delay'].includes(storedUrgency);
        if (needsRefNumber && (!ref_number || ref_number.trim() === '')) {
            return res.status(400).json({ error: 'Reference number is required for this urgency type.' });
        }

        const urgentData = {
            full_name,
            email,
            phone_number,
            urgency_type: storedUrgency,
            ref_number: needsRefNumber ? ref_number : undefined,
            brief_description,
            documents: documentPath, // Use 'documents' to match schema
            ip: userIp // Store the IP address
        };

        const urgent = new Urgent(urgentData);
        await urgent.save();

        // After saving urgent, send user and admin emails using EJS templates
        const userTemplateData = {
            full_name,
            urgency_type: storedUrgency,
            brief_description
        };
        await sendEmail({
            to: email,
            subject: 'Urgent Inquiry Received',
            template: 'urgent_user.ejs',
            templateData: userTemplateData,
            importance: 'high'
        });

        const adminTemplateData = {
            full_name,
            email,
            phone_number,
            urgency_type: storedUrgency,
            other_urgency: urgency_type === 'Other' ? other_urgency : null,
            ref_number,
            documents: documentPath ? 'Document attached' : null,
            brief_description
        };
        const adminAttachments = file
            ? [
                {
                    filename: require('path').basename(documentPath),
                    path: documentPath,
                    contentType: file.mimetype
                }
            ]
            : [];
        await sendEmail({
            to: process.env.SMTP_FROM,
            subject: `Urgent Inquiry: ${storedUrgency} from ${full_name}`,
            template: 'urgent_admin.ejs',
            templateData: adminTemplateData,
            attachments: adminAttachments,
            importance: 'high'
        });

        res.status(200).json({ message: 'Urgent inquiry submitted successfully.' });
    } catch (err) {
        if (err.name === 'ValidationError') {
            const firstError = Object.values(err.errors)[0].message;
            return res.status(400).json({ error: firstError });
        }
        console.error('Urgent inquiry error:', err);
        if (req.file) {
            try {
                fs.unlinkSync(req.file.path);
            } catch (unlinkErr) {
                console.error('Failed to delete file:', unlinkErr);
                return res.status(500).json({ error: 'Failed to delete uploaded file.' });
            }
        }
        res.status(500).json({ error: 'Server error while submitting urgent inquiry.' });
    }
};


const GetAllInquiries = async (req, res) => {
    try {
        const urgents = await Urgent.find({})
            .sort({ createdAt: -1 }) // Sort by newest first
            .lean();

        return res.status(200).json(urgents);
    } catch (error) {
        console.error("GetAllInquiries error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
}

const DeleteInquiry = async (req, res) => {
    try {
        const { id } = req.params;
        await Urgent.findByIdAndDelete(id);
        return res.status(200).json({ message: "Inquiry deleted successfully" });
    } catch (error) {
        console.error("DeleteInquiry error:", error);
        return res.status(500).json({ message: "Internal server error" });
    }
}


module.exports = { UrgentInquiry, GetAllInquiries, DeleteInquiry };



