import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/libs/auth'

export default async function Page({ params }) {
    const session = await getServerSession(authOptions)

    // If no session, redirect to login
    if (!session) {
        redirect(`/${params.lang}/login`)
        return null
    }

    // If user has session, redirect to profile (regardless of verification status)
    redirect(`/${params.lang}/pages/user-profile`)
    return null
}

