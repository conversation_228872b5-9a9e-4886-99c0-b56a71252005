// API service for MFA (Multi-Factor Authentication) management
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

/**
 * Generate MFA device setup (secret and QR code)
 * @param {string} userId - User ID
 * @param {string} deviceName - Name for the device (optional)
 * @returns {Promise<Object>} MFA setup data including QR code and device ID
 */
export const generateMFASetup = async (userId, deviceName = 'Authenticator App') => {
  try {
    console.log('🔐 Generating MFA setup for user:', userId, 'device:', deviceName)

    const response = await fetch(`${API_BASE_URL}/mfa/setup/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ deviceName })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ MFA setup generated successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error generating MFA setup:', error)
    throw error
  }
}

/**
 * Verify MFA device setup and activate device
 * @param {string} userId - User ID
 * @param {string} token - 6-digit TOTP token
 * @param {string} deviceId - Device ID from setup
 * @returns {Promise<Object>} Verification result with backup codes
 */
export const verifyMFASetup = async (userId, token, deviceId) => {
  try {
    console.log('🔐 Verifying MFA setup for user:', userId, 'device:', deviceId)

    const response = await fetch(`${API_BASE_URL}/mfa/setup/verify/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token, deviceId })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ MFA setup verified successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error verifying MFA setup:', error)
    throw error
  }
}

/**
 * Verify MFA token during login
 * @param {string} userId - User ID
 * @param {string} token - 6-digit TOTP token or backup code
 * @param {boolean} isBackupCode - Whether the token is a backup code
 * @returns {Promise<Object>} Verification result
 */
export const verifyMFAToken = async (userId, token, isBackupCode = false) => {
  try {
    console.log('🔐 Verifying MFA token for user:', userId)

    const response = await fetch(`${API_BASE_URL}/mfa/verify/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token, isBackupCode })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ MFA token verified successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error verifying MFA token:', error)
    throw error
  }
}

/**
 * Get MFA status for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} MFA status information
 */
export const getMFAStatus = async (userId) => {
  try {
    console.log('🔍 Getting MFA status for user:', userId)

    const response = await fetch(`${API_BASE_URL}/mfa/status/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ MFA status retrieved successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error getting MFA status:', error)
    throw error
  }
}

/**
 * Disable MFA for a user
 * @param {string} userId - User ID
 * @param {string} password - User's current password
 * @returns {Promise<Object>} Disable result
 */
export const disableMFA = async (userId, password) => {
  try {
    console.log('🔐 Disabling MFA for user:', userId)

    const response = await fetch(`${API_BASE_URL}/mfa/disable/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ password })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ MFA disabled successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error disabling MFA:', error)
    throw error
  }
}

/**
 * Generate new backup codes
 * @param {string} userId - User ID
 * @returns {Promise<Object>} New backup codes
 */
export const generateBackupCodes = async (userId) => {
  try {
    console.log('🔐 Generating backup codes for user:', userId)

    const response = await fetch(`${API_BASE_URL}/mfa/backup-codes/${userId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Backup codes generated successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error generating backup codes:', error)
    throw error
  }
}

/**
 * Get all MFA devices for a user
 * @param {string} userId - User ID
 * @returns {Promise<Object>} List of MFA devices
 */
export const getMFADevices = async (userId) => {
  try {
    console.log('📱 Getting MFA devices for user:', userId)

    const response = await fetch(`${API_BASE_URL}/mfa/devices/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ MFA devices retrieved successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error getting MFA devices:', error)
    throw error
  }
}

/**
 * Update device name
 * @param {string} userId - User ID
 * @param {string} deviceId - Device ID
 * @param {string} deviceName - New device name
 * @returns {Promise<Object>} Update result
 */
export const updateDeviceName = async (userId, deviceId, deviceName) => {
  try {
    console.log('📝 Updating device name for user:', userId, 'device:', deviceId)

    const response = await fetch(`${API_BASE_URL}/mfa/devices/${userId}/${deviceId}/name`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ deviceName })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Device name updated successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error updating device name:', error)
    throw error
  }
}

/**
 * Remove/deactivate a device
 * @param {string} userId - User ID
 * @param {string} deviceId - Device ID
 * @returns {Promise<Object>} Removal result
 */
export const removeDevice = async (userId, deviceId) => {
  try {
    console.log('🗑️ Removing device for user:', userId, 'device:', deviceId)

    const response = await fetch(`${API_BASE_URL}/mfa/devices/${userId}/${deviceId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Device removed successfully:', result)

    return result
  } catch (error) {
    console.error('❌ Error removing device:', error)
    throw error
  }
}
