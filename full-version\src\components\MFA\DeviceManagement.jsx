'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Divider,
  Grid,
  Tooltip
} from '@mui/material'
import {
  Smartphone,
  Edit,
  Delete,
  Add,
  AccessTime,
  CalendarToday,
  Computer,
  PhoneAndroid,
  Tablet
} from '@mui/icons-material'
import { getMFADevices, updateDeviceName, removeDevice } from '@/services/mfaApi'
import MFASetup from './MFASetup'

const DeviceManagement = ({ userId, onDeviceChange }) => {
  const [devices, setDevices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [removeDialogOpen, setRemoveDialogOpen] = useState(false)
  const [addDeviceOpen, setAddDeviceOpen] = useState(false)
  const [selectedDevice, setSelectedDevice] = useState(null)
  const [newDeviceName, setNewDeviceName] = useState('')
  const [maxDevices, setMaxDevices] = useState(5)

  useEffect(() => {
    loadDevices()
  }, [userId])

  const loadDevices = async () => {
    try {
      setLoading(true)
      setError('')
      
      const result = await getMFADevices(userId)
      setDevices(result.data.devices)
      setMaxDevices(result.data.maxDevices)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleEditDevice = (device) => {
    setSelectedDevice(device)
    setNewDeviceName(device.deviceName)
    setEditDialogOpen(true)
  }

  const handleUpdateDeviceName = async () => {
    if (!newDeviceName.trim()) {
      setError('Device name cannot be empty')
      return
    }

    try {
      setLoading(true)
      setError('')
      
      await updateDeviceName(userId, selectedDevice.deviceId, newDeviceName.trim())
      await loadDevices()
      setEditDialogOpen(false)
      setSelectedDevice(null)
      setNewDeviceName('')
      
      if (onDeviceChange) onDeviceChange()
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveDevice = (device) => {
    setSelectedDevice(device)
    setRemoveDialogOpen(true)
  }

  const confirmRemoveDevice = async () => {
    try {
      setLoading(true)
      setError('')
      
      await removeDevice(userId, selectedDevice.deviceId)
      await loadDevices()
      setRemoveDialogOpen(false)
      setSelectedDevice(null)
      
      if (onDeviceChange) onDeviceChange()
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const getDeviceIcon = (deviceInfo) => {
    const platform = deviceInfo?.platform?.toLowerCase() || ''
    const userAgent = deviceInfo?.userAgent?.toLowerCase() || ''
    
    if (platform.includes('android') || userAgent.includes('android')) {
      return <PhoneAndroid />
    } else if (platform.includes('ios') || userAgent.includes('iphone') || userAgent.includes('ipad')) {
      return userAgent.includes('ipad') ? <Tablet /> : <PhoneAndroid />
    } else if (platform.includes('windows') || platform.includes('mac') || platform.includes('linux')) {
      return <Computer />
    }
    return <Smartphone />
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString()
  }

  const formatDateTime = (dateString) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleString()
  }

  if (loading && devices.length === 0) {
    return (
      <Card>
        <CardContent>
          <Typography>Loading devices...</Typography>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader
          avatar={<Smartphone sx={{ color: 'primary.main' }} />}
          title="MFA Devices"
          subheader={`${devices.length} of ${maxDevices} devices registered`}
          action={
            devices.length < maxDevices && (
              <Button
                variant="outlined"
                startIcon={<Add />}
                onClick={() => setAddDeviceOpen(true)}
                disabled={loading}
              >
                Add Device
              </Button>
            )
          }
        />
        <CardContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {devices.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Smartphone sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No MFA devices registered
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Add your first authenticator device to enable MFA
              </Typography>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => setAddDeviceOpen(true)}
              >
                Add First Device
              </Button>
            </Box>
          ) : (
            <List>
              {devices.map((device, index) => (
                <Box key={device.deviceId}>
                  <ListItem>
                    <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                      {getDeviceIcon(device.deviceInfo)}
                    </Box>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">
                            {device.deviceName}
                          </Typography>
                          <Chip 
                            label="Active" 
                            color="success" 
                            size="small" 
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Grid container spacing={2} sx={{ mt: 0.5 }}>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <CalendarToday sx={{ fontSize: 16, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                Added: {formatDate(device.registeredAt)}
                              </Typography>
                            </Box>
                          </Grid>
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              <AccessTime sx={{ fontSize: 16, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                Last used: {formatDateTime(device.lastUsedAt)}
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="Edit device name">
                          <IconButton
                            edge="end"
                            onClick={() => handleEditDevice(device)}
                            disabled={loading}
                          >
                            <Edit />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Remove device">
                          <IconButton
                            edge="end"
                            onClick={() => handleRemoveDevice(device)}
                            disabled={loading || devices.length === 1}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < devices.length - 1 && <Divider />}
                </Box>
              ))}
            </List>
          )}

          {devices.length >= maxDevices && (
            <Alert severity="info" sx={{ mt: 2 }}>
              You have reached the maximum number of devices ({maxDevices}). 
              Remove a device to add a new one.
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Edit Device Name Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Edit Device Name</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Device Name"
            value={newDeviceName}
            onChange={(e) => setNewDeviceName(e.target.value)}
            sx={{ mt: 2 }}
            placeholder="e.g., iPhone, Work Phone, Personal Tablet"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            variant="contained"
            onClick={handleUpdateDeviceName}
            disabled={loading || !newDeviceName.trim()}
          >
            {loading ? 'Updating...' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Remove Device Dialog */}
      <Dialog open={removeDialogOpen} onClose={() => setRemoveDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Remove Device</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Are you sure you want to remove "{selectedDevice?.deviceName}"? 
            This action cannot be undone.
          </Alert>
          <Typography variant="body2" color="text.secondary">
            You will no longer be able to use this device for MFA verification. 
            Make sure you have access to other devices or backup codes.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setRemoveDialogOpen(false)}>
            Cancel
          </Button>
          <Button 
            color="error" 
            variant="contained"
            onClick={confirmRemoveDevice}
            disabled={loading}
          >
            {loading ? 'Removing...' : 'Remove Device'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add Device Dialog */}
      <MFASetup
        open={addDeviceOpen}
        onClose={() => setAddDeviceOpen(false)}
        userId={userId}
        onSuccess={(message) => {
          alert(message)
          setAddDeviceOpen(false)
          loadDevices()
          if (onDeviceChange) onDeviceChange()
        }}
        isAddingDevice={true}
      />
    </>
  )
}

export default DeviceManagement
