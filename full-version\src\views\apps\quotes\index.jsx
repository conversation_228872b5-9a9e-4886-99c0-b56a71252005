'use client'

// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Divider from '@mui/material/Divider'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Paper from '@mui/material/Paper'
import Chip from '@mui/material/Chip'
import Button from '@mui/material/Button'
import IconButton from '@mui/material/IconButton'
import CircularProgress from '@mui/material/CircularProgress'
import Box from '@mui/material/Box'

// Component Imports
import QuotesTable from './QuotesTable'

// Sam<PERSON> data for demonstration
const sampleQuotes = [
    {
        id: 1,
        customer: '<PERSON>',
        email: '<EMAIL>',
        date: '2023-07-15',
        amount: '$1,250.00',
        status: 'Pending'
    },
    {
        id: 2,
        customer: 'Jane Smith',
        email: '<EMAIL>',
        date: '2023-07-12',
        amount: '$2,450.00',
        status: 'Approved'
    },
    {
        id: 3,
        customer: 'Mike Johnson',
        email: '<EMAIL>',
        date: '2023-07-10',
        amount: '$850.00',
        status: 'Rejected'
    },
    {
        id: 4,
        customer: 'Sarah Williams',
        email: '<EMAIL>',
        date: '2023-07-08',
        amount: '$3,150.00',
        status: 'Approved'
    },
    {
        id: 5,
        customer: 'David Brown',
        email: '<EMAIL>',
        date: '2023-07-05',
        amount: '$1,750.00',
        status: 'Pending'
    }
]

const QuotesApp = () => {
    return (
        <div className='w-full min-h-screen bg-gray-50 p-2 sm:p-4 lg:p-6'>
            <div className='max-w-full mx-auto'>
                <Card>
                    <CardHeader
                        title="Quotes Management"
                        action={
                            <Button
                                variant="contained"
                                color="primary"
                                startIcon={<i className='tabler-plus'></i>}
                            >
                                Create New Quote
                            </Button>
                        }
                    />
                    <CardContent>
                        <Typography variant="body2" sx={{ mb: 4 }}>
                            View and manage quotes sent by users.
                        </Typography>
                        <Divider sx={{ mb: 4 }} />

                        <QuotesTable />
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default QuotesApp 
