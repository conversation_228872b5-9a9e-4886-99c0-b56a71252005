'use client'

// React Imports
import { useEffect, useState, useMemo } from 'react'

// Next Imports
// import Link from 'next/link'
// import { useParams } from 'next/navigation'

// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Chip from '@mui/material/Chip'
import Checkbox from '@mui/material/Checkbox'
import IconButton from '@mui/material/IconButton'
import Box from '@mui/material/Box'
import useMediaQuery from '@mui/material/useMediaQuery'
import { useTheme } from '@mui/material/styles'
// import { styled } from '@mui/material/styles'
import TablePagination from '@mui/material/TablePagination'
import MenuItem from '@mui/material/MenuItem'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'
import Select from '@mui/material/Select'
import FormControl from '@mui/material/FormControl'
import ListItemIcon from '@mui/material/ListItemIcon'

// Third-party Imports
import classnames from 'classnames'
import { rankItem } from '@tanstack/match-sorter-utils'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

// Component Imports
import TableFilters from './TableFilters'
import UserDetailsModal from './UserDetailsModal'

import TablePaginationComponent from '@components/TablePaginationComponent'
import CustomTextField from '@core/components/mui/TextField'
import CustomAvatar from '@core/components/mui/Avatar'

// PDF Export
import jsPDF from 'jspdf'
// Import autotable plugin
import 'jspdf-autotable'

// API Imports
import { fetchContacts, deleteContact } from '@/services/contactApi'

// Util Imports
// import { getLocalizedUrl } from '@/utils/i18n'

// Style Imports
import tableStyles from '@core/styles/table.module.css'

// Custom styles for responsive icons
const actionButtonStyles = {
  minWidth: '32px !important',
  width: '32px !important',
  height: '32px !important',
  padding: '4px !important',
  flexShrink: 0,
  '& i': {
    fontSize: '18px !important',
    minWidth: '18px',
    minHeight: '18px'
  },
  '&:hover': {
    backgroundColor: 'action.hover'
  }
}

// Styled Components
// const Icon = styled('i')({})

const fuzzyFilter = (row, columnId, value, addMeta) => {
  // Rank the item
  const itemRank = rankItem(row.getValue(columnId), value)

  // Store the itemRank info
  addMeta({
    itemRank
  })

  // Return if the item should be filtered in/out
  return itemRank.passed
}

const DebouncedInput = ({ value: initialValue, onChange, debounce = 500, ...props }) => {
  // States
  const [value, setValue] = useState(initialValue)

  useEffect(() => {
    setValue(initialValue)
  }, [initialValue])
  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value)
    }, debounce)

    return () => clearTimeout(timeout)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value])

  return <CustomTextField {...props} value={value} onChange={e => setValue(e.target.value)} />
}

// Vars
const statusConfig = {
  pending: {
    label: 'Pending',
    color: 'warning',
    icon: 'tabler-clock',
    description: 'Waiting for review'
  },
  'in-view': {
    label: 'In View',
    color: 'info',
    icon: 'tabler-eye',
    description: 'Being reviewed'
  },
  completed: {
    label: 'Completed',
    color: 'success',
    icon: 'tabler-check',
    description: 'Review completed'
  }
}

// Status options for dropdown
const statusOptions = [
  { value: 'pending', label: 'Pending', color: 'warning', icon: 'tabler-clock' },
  { value: 'in-view', label: 'In View', color: 'info', icon: 'tabler-eye' },
  { value: 'completed', label: 'Completed', color: 'success', icon: 'tabler-check' }
]

// Status Dropdown Component
const StatusDropdown = ({ currentStatus, onStatusChange, contactId }) => {
  const [isOpen, setIsOpen] = useState(false)
  const config = statusConfig[currentStatus] || statusConfig.pending

  const handleStatusSelect = (newStatus) => {
    console.log('StatusDropdown: Selecting status', newStatus, 'for contact', contactId)
    onStatusChange(contactId, newStatus)
    setIsOpen(false)
  }

  if (!isOpen) {
    // Show as button/chip when closed
    return (
      <Chip
        icon={<i className={`${config.icon} text-xs sm:text-sm`} />}
        label={config.label}
        color={config.color}
        variant='filled'
        size='small'
        className='text-xs sm:text-sm cursor-pointer'
        onClick={() => setIsOpen(true)}
        title="Click to change status"
        sx={{
          height: { xs: '28px', sm: '32px' },
          width: { xs: '100px', sm: '110px' },
          minWidth: { xs: '100px', sm: '110px' },
          maxWidth: { xs: '100px', sm: '110px' },
          fontSize: { xs: '0.75rem', sm: '0.8rem' },
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '& .MuiChip-label': {
            padding: { xs: '0 6px', sm: '0 8px' },
            fontSize: { xs: '0.7rem', sm: '0.75rem' },
            fontWeight: 500,
            whiteSpace: 'nowrap',
            overflow: 'visible',
            textOverflow: 'unset'
          },
          '& .MuiChip-icon': {
            fontSize: { xs: '14px', sm: '16px' },
            marginLeft: { xs: '6px', sm: '8px' },
            marginRight: { xs: '0px', sm: '2px' }
          },
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: 2
          }
        }}
      />
    )
  }

  // Show as dropdown when open
  return (
    <FormControl size="small" sx={{ minWidth: 120 }}>
      <Select
        value={currentStatus}
        onChange={(e) => handleStatusSelect(e.target.value)}
        onClose={() => setIsOpen(false)}
        open={isOpen}
        size="small"
        autoFocus
        sx={{
          height: '32px',
          fontSize: '0.75rem',
          '& .MuiSelect-select': {
            padding: '4px 8px',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }
        }}
      >
        {statusOptions.map((option) => (
          <MenuItem
            key={option.value}
            value={option.value}
            onClick={() => handleStatusSelect(option.value)}
          >
            <ListItemIcon sx={{ minWidth: '20px !important' }}>
              <i className={`${option.icon} text-sm`} />
            </ListItemIcon>
            <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
              {option.label}
            </Typography>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}



// Column Definitions
const columnHelper = createColumnHelper()

// Mobile Card Component
const MobileContactCard = ({ contact, onViewDetails, onDelete, onStatusChange }) => {
  const status = contact.status || 'pending'

  return (
    <Card className='mb-4 shadow-sm'>
      <CardContent className='p-4'>
        <div className='flex items-start justify-between mb-3'>
          <div className='flex items-center gap-3 flex-1 min-w-0'>
            <CustomAvatar size={40}>
              {contact.fullName?.charAt(0)?.toUpperCase()}
            </CustomAvatar>
            <div className='flex flex-col min-w-0 flex-1'>
              <Typography variant='h6' className='font-semibold text-sm truncate'>
                {contact.fullName}
              </Typography>
              <Typography variant='body2' color='text.secondary' className='text-xs truncate'>
                {contact.email || contact.username}
              </Typography>
            </div>
          </div>
          <div className='flex items-center gap-1 ml-2'>
            <IconButton
              onClick={() => onViewDetails(contact)}
              title="View Details"
              size='small'
              sx={actionButtonStyles}
            >
              <i className='tabler-info-circle text-textSecondary' />
            </IconButton>
            <IconButton
              onClick={() => onDelete(contact.id)}
              title="Delete"
              size='small'
              sx={actionButtonStyles}
            >
              <i className='tabler-trash text-textSecondary' />
            </IconButton>
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm'>
          <div>
            <Typography variant='caption' color='text.secondary' className='font-medium'>
              Contact
            </Typography>
            <Typography variant='body2' className='truncate'>
              {contact.phone || contact.contact || '+****************'}
            </Typography>
          </div>
          <div>
            <Typography variant='caption' color='text.secondary' className='font-medium'>
              Type
            </Typography>
            <Typography variant='body2' className='truncate'>
              {contact.inquiryType || contact.type || 'General Inquiry'}
            </Typography>
          </div>
          <div className='sm:col-span-2'>
            <Typography variant='caption' color='text.secondary' className='font-medium'>
              Status
            </Typography>
            <div className='mt-1'>
              <StatusDropdown
                currentStatus={status}
                onStatusChange={onStatusChange}
                contactId={contact.id}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

const UserListTable = () => {
  // States
  const [rowSelection, setRowSelection] = useState({})
  const [data, setData] = useState([])
  const [filteredData, setFilteredData] = useState([])
  const [globalFilter, setGlobalFilter] = useState('')
  const [modalOpen, setModalOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Hooks
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  // const { lang: locale } = useParams()

  // Load contacts from backend
  const loadContacts = async () => {
    try {
      console.log('🔄 Loading contacts from backend...')
      setLoading(true)
      setError(null)

      const contacts = await fetchContacts()
      console.log('✅ Fetched contacts:', contacts.length, 'items')
      console.log('📋 Sample contact:', contacts[0])

      // Restore saved statuses from localStorage
      const savedStatuses = JSON.parse(localStorage.getItem('contactStatuses') || '{}')
      const contactsWithSavedStatuses = contacts.map(contact => ({
        ...contact,
        status: savedStatuses[contact.id] || contact.status || 'pending'
      }))

      console.log('📦 Restored contact statuses from localStorage:', Object.keys(savedStatuses).length, 'items')

      setData(contactsWithSavedStatuses)
      setFilteredData(contactsWithSavedStatuses)
    } catch (err) {
      console.error('❌ Error loading contacts:', err)
      setError('Failed to load contacts. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Load contacts from backend on component mount
  useEffect(() => {
    loadContacts()
  }, [])

  const handleViewDetails = (userData) => {
    setSelectedUser(userData)
    setModalOpen(true)
  }

  const handleModalClose = () => {
    setModalOpen(false)
    setSelectedUser(null)
  }

  const handleStatusChange = (userId, newStatus) => {
    console.log('Updating status for user:', userId, 'to:', newStatus)

    // Save status to localStorage for persistence
    const savedStatuses = JSON.parse(localStorage.getItem('contactStatuses') || '{}')
    savedStatuses[userId] = newStatus
    localStorage.setItem('contactStatuses', JSON.stringify(savedStatuses))

    // Update local state (frontend-only status management)
    setData(prevData =>
      prevData.map(item =>
        item.id === userId
          ? { ...item, status: newStatus }
          : item
      )
    )
    setFilteredData(prevData =>
      prevData.map(item =>
        item.id === userId
          ? { ...item, status: newStatus }
          : item
      )
    )

    console.log('Status updated and saved to localStorage successfully')
  }

  // Optional: Clear all saved statuses (for debugging)
  const clearSavedStatuses = () => {
    localStorage.removeItem('contactStatuses')
    console.log('Cleared all saved contact statuses')
  }

  const handleDeleteContact = async (contactId) => {
    try {
      // Find the contact to get their name for confirmation
      const contact = data.find(item => item.id === contactId)
      const contactName = contact?.fullName || 'this contact'

      // Show confirmation dialog
      const confirmed = window.confirm(
        `Are you sure you want to delete ${contactName}?\n\nThis action cannot be undone and will permanently remove the contact from the database.`
      )

      if (!confirmed) {
        return
      }

      // Call backend API to delete contact
      await deleteContact(contactId)

      // Remove contact from local state
      setData(prevData => prevData.filter(item => item.id !== contactId))
      setFilteredData(prevData => prevData.filter(item => item.id !== contactId))

      // Clear selection if deleted contact was selected
      setRowSelection(prevSelection => {
        const newSelection = { ...prevSelection }
        delete newSelection[contactId]
        return newSelection
      })

      // Show success message
      alert(`${contactName} has been deleted successfully!`)

    } catch (error) {
      console.error('Error deleting contact:', error)
      alert('Failed to delete contact. Please try again.')
    }
  }

  // Get selected rows data
  const getSelectedRowsData = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows
    return selectedRows.map(row => row.original)
  }

  // PDF Export function
  const exportSelectedToPDF = () => {
    const selectedData = getSelectedRowsData()

    if (selectedData.length === 0) {
      alert('Please select at least one contact to export.')
      return
    }

    try {
      const doc = new jsPDF()

      // Add title
      doc.setFontSize(20)
      doc.setTextColor(40, 40, 40)
      doc.text('CAM Transport - Contact Export', 20, 20)

      // Add export info
      doc.setFontSize(12)
      doc.setTextColor(100, 100, 100)
      doc.text(`Export Date: ${new Date().toLocaleDateString()}`, 20, 35)
      doc.text(`Selected Contacts: ${selectedData.length}`, 20, 45)

      // Prepare table data
      const tableData = selectedData.map(contact => [
        contact.fullName || 'N/A',
        contact.email || contact.username || 'N/A',
        contact.phone || contact.contact || 'N/A',
        contact.company || contact.currentPlan || 'CAM Transport',
        contact.inquiryType || contact.type || 'General Inquiry',
        statusConfig[contact.status || 'pending']?.label || 'Pending'
      ])

      // Try different autoTable approaches
      if (typeof doc.autoTable === 'function') {
        // Method 1: Direct method
        doc.autoTable({
          head: [['Name', 'Email', 'Contact', 'Company', 'Inquiry Type', 'Status']],
          body: tableData,
          startY: 60,
          styles: {
            fontSize: 10,
            cellPadding: 3
          },
          headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold'
          },
          alternateRowStyles: {
            fillColor: [245, 245, 245]
          }
        })
      } else if (typeof autoTable === 'function') {
        // Method 2: Imported function
        autoTable(doc, {
          head: [['Name', 'Email', 'Contact', 'Company', 'Inquiry Type', 'Status']],
          body: tableData,
          startY: 60,
          styles: {
            fontSize: 10,
            cellPadding: 3
          },
          headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold'
          },
          alternateRowStyles: {
            fillColor: [245, 245, 245]
          }
        })
      } else {
        // Fallback: Manual table creation
        let yPosition = 70
        const lineHeight = 8

        // Add headers
        doc.setFontSize(10)
        doc.setFont(undefined, 'bold')
        doc.text('Name', 20, yPosition)
        doc.text('Email', 60, yPosition)
        doc.text('Contact', 110, yPosition)
        doc.text('Company', 150, yPosition)
        doc.text('Type', 180, yPosition)

        yPosition += lineHeight + 2

        // Add data rows
        doc.setFont(undefined, 'normal')
        tableData.forEach(row => {
          doc.text(row[0].substring(0, 15), 20, yPosition)
          doc.text(row[1].substring(0, 20), 60, yPosition)
          doc.text(row[2].substring(0, 15), 110, yPosition)
          doc.text(row[3].substring(0, 12), 150, yPosition)
          doc.text(row[4].substring(0, 10), 180, yPosition)
          yPosition += lineHeight

          // Add new page if needed
          if (yPosition > 270) {
            doc.addPage()
            yPosition = 20
          }
        })
      }

      // Save the PDF
      doc.save(`CAM_Transport_Contacts_${new Date().toISOString().split('T')[0]}.pdf`)

    } catch (error) {
      console.error('PDF Export Error:', error)
      alert('Error generating PDF. Please try again.')
    }
  }

  const columns = useMemo(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            {...{
              checked: table.getIsAllRowsSelected(),
              indeterminate: table.getIsSomeRowsSelected(),
              onChange: table.getToggleAllRowsSelectedHandler()
            }}
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            {...{
              checked: row.getIsSelected(),
              disabled: !row.getCanSelect(),
              indeterminate: row.getIsSomeSelected(),
              onChange: row.getToggleSelectedHandler()
            }}
          />
        )
      },
      columnHelper.accessor('fullName', {
        header: 'User',
        cell: ({ row }) => (
          <div className='flex items-center gap-2 sm:gap-3 min-w-[150px] sm:min-w-[200px] max-w-[250px]'>
            <div className='flex-shrink-0'>
              {getAvatar({ avatar: row.original.avatar, fullName: row.original.fullName })}
            </div>
            <div className='flex flex-col overflow-hidden min-w-0'>
              <Typography
                color='text.primary'
                className='font-medium truncate text-sm sm:text-base'
                title={row.original.fullName}
              >
                {row.original.fullName}
              </Typography>
              <Typography
                variant='body2'
                color='text.secondary'
                className='truncate text-xs sm:text-sm'
                style={{ letterSpacing: '0.3px' }}
                title={row.original.email || row.original.username}
              >
                {row.original.email || row.original.username}
              </Typography>
            </div>
          </div>
        )
      }),
      columnHelper.accessor('role', {
        header: 'Contact',
        cell: ({ row }) => (
          <div className='min-w-[100px] sm:min-w-[120px] pr-3'>
            <Typography
              color='text.primary'
              className='font-medium text-xs sm:text-sm truncate'
              title={row.original.phone || row.original.contact || '+****************'}
            >
              {row.original.phone || row.original.contact || '+****************'}
            </Typography>
          </div>
        ),
        enableSorting: false
      }),

      columnHelper.accessor('billing', {
        header: 'Type',
        cell: ({ row }) => (
          <div className='min-w-[110px] sm:min-w-[130px] pr-3'>
            <Typography
              color='text.primary'
              className='font-medium text-xs sm:text-sm'
              style={{
                whiteSpace: 'normal',
                wordWrap: 'break-word',
                lineHeight: '1.3'
              }}
              title={row.original.inquiryType || row.original.type || 'General Inquiry'}
            >
              {row.original.inquiryType || row.original.type || 'General Inquiry'}
            </Typography>
          </div>
        ),
        enableSorting: false
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: ({ row }) => {
          const status = row.original.status || 'pending'

          return (
            <div className='flex items-center justify-start gap-1 min-w-[110px] sm:min-w-[120px] pr-3'>
              <StatusDropdown
                currentStatus={status}
                onStatusChange={handleStatusChange}
                contactId={row.original.id}
              />
            </div>
          )
        },
        enableSorting: false
      }),

      columnHelper.accessor('action', {
        header: 'Action',
        cell: ({ row }) => (
          <div className='flex items-center justify-start gap-2 min-w-[120px] w-[120px] pr-4' style={{ flexShrink: 0 }}>
            {/* View Details - Info Icon */}
            <IconButton
              onClick={() => handleViewDetails(row.original)}
              title="View Details"
              size='small'
              sx={actionButtonStyles}
            >
              <i className='tabler-info-circle text-textSecondary' />
            </IconButton>

            {/* Delete */}
            <IconButton
              onClick={() => handleDeleteContact(row.original.id)}
              title="Delete"
              size='small'
              sx={actionButtonStyles}
            >
              <i className='tabler-trash text-textSecondary' />
            </IconButton>
          </div>
        ),
        enableSorting: false
      })
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data, filteredData]
  )

  const table = useReactTable({
    data: filteredData,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter
    },
    state: {
      rowSelection,
      globalFilter
    },
    initialState: {
      pagination: {
        pageSize: 10
      }
    },
    enableRowSelection: true, //enable row selection for all rows
    // enableRowSelection: row => row.original.age > 18, // or enable row selection conditionally per row
    globalFilterFn: fuzzyFilter,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    onGlobalFilterChange: setGlobalFilter,
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const getAvatar = params => {
    const { fullName } = params

    return <CustomAvatar size={34}>{fullName?.charAt(0)?.toUpperCase()}</CustomAvatar>
  }

  // Show loading state
  if (loading) {
    return (
      <Card>
        <CardHeader title='Contact List' className='pbe-4' />
        <CardContent className='flex justify-center items-center py-8'>
          <div className='flex flex-col items-center gap-4'>
            <CircularProgress />
            <Typography variant='body2' color='text.secondary'>
              Loading contacts...
            </Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show error state
  if (error) {
    return (
      <Card>
        <CardHeader title='Contact List' className='pbe-4' />
        <CardContent>
          <Alert severity='error' className='mb-4'>
            {error}
          </Alert>
          <Button
            variant='contained'
            onClick={() => window.location.reload()}
            startIcon={<i className='tabler-refresh' />}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  // Show empty state
  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader title='Contact List' className='pbe-4' />
        <CardContent className='flex justify-center items-center py-8'>
          <div className='flex flex-col items-center gap-4 text-center'>
            <i className='tabler-users text-6xl text-textSecondary' />
            <Typography variant='h6' color='text.secondary'>
              No contacts found
            </Typography>
            <Typography variant='body2' color='text.secondary'>
              Contacts submitted through the website will appear here.
            </Typography>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      {isMobile ? (
        // Mobile View
        <Card className='w-full'>
          <CardHeader
            title='Contact Management'
            subheader='Manage contact submissions and review status'
            className='pb-2 px-4'
          />
          <CardContent className='px-4'>
            <TableFilters setData={setFilteredData} tableData={data} />

            <div className='flex flex-col gap-4 mb-4 mt-4'>
              <DebouncedInput
                value={globalFilter ?? ''}
                onChange={value => setGlobalFilter(String(value))}
                placeholder='Search contacts...'
                className='w-full'
                size='small'
              />

              {/* Selection Counter */}
              {Object.keys(rowSelection).length > 0 && (
                <div className='flex items-center justify-between p-3 bg-primary/10 rounded-lg border border-primary/20'>
                  <div className='flex items-center gap-2'>
                    <i className='tabler-check-circle text-primary' />
                    <Typography variant='body2' className='font-medium text-primary'>
                      {Object.keys(rowSelection).length} contact{Object.keys(rowSelection).length !== 1 ? 's' : ''} selected
                    </Typography>
                  </div>
                  <Button
                    size='small'
                    variant='text'
                    onClick={() => setRowSelection({})}
                    className='text-primary'
                  >
                    Clear Selection
                  </Button>
                </div>
              )}

              <Button
                color='secondary'
                variant='tonal'
                startIcon={<i className='tabler-file-type-pdf' />}
                className='w-full'
                size='small'
                onClick={exportSelectedToPDF}
                disabled={Object.keys(rowSelection).length === 0}
              >
                Export PDF ({Object.keys(rowSelection).length})
              </Button>

              <IconButton
                color='primary'
                onClick={loadContacts}
                disabled={loading}
                title={loading ? 'Loading...' : 'Refresh Data'}
                sx={{
                  border: '1px solid',
                  borderColor: 'primary.main',
                  width: '100%',
                  height: '40px',
                  borderRadius: '8px',
                  '&:hover': {
                    backgroundColor: 'primary.light',
                    transform: 'scale(1.02)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />
              </IconButton>
            </div>

            <Box sx={{ '& > *:not(:last-child)': { marginBottom: 2 } }}>
              {table.getFilteredRowModel().rows.length === 0 ? (
                <Card className='p-8 text-center'>
                  <Typography color='text.secondary'>No contacts found</Typography>
                </Card>
              ) : (
                table
                  .getRowModel()
                  .rows.slice(0, table.getState().pagination.pageSize)
                  .map(row => (
                    <MobileContactCard
                      key={row.id}
                      contact={row.original}
                      onViewDetails={handleViewDetails}
                      onDelete={handleDeleteContact}
                      onStatusChange={handleStatusChange}
                    />
                  ))
              )}
            </Box>

            <Box className='mt-4'>
              <TablePagination
                component={() => <TablePaginationComponent table={table} />}
                count={table.getFilteredRowModel().rows.length}
                rowsPerPage={table.getState().pagination.pageSize}
                page={table.getState().pagination.pageIndex}
                onPageChange={(_, page) => {
                  table.setPageIndex(page)
                }}
              />
            </Box>
          </CardContent>
        </Card>
      ) : (
        // Desktop View
        <Card className='w-full'>
          <CardHeader
            title={
              <Typography variant='h5' className='text-lg sm:text-xl lg:text-2xl'>
                Contact Management
              </Typography>
            }
            subheader={
              <Typography variant='body2' className='text-xs sm:text-sm'>
                Manage contact submissions and review status
              </Typography>
            }
            className='pbe-4 px-3 sm:px-6'
          />
          <TableFilters setData={setFilteredData} tableData={data} />
        <div className='flex justify-between flex-col items-start lg:flex-row lg:items-center p-4 sm:p-6 border-bs gap-4'>
          <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto'>
            <CustomTextField
              select
              value={table.getState().pagination.pageSize}
              onChange={e => table.setPageSize(Number(e.target.value))}
              className='w-full sm:w-[100px]'
              size='small'
            >
              <MenuItem value='10'>10</MenuItem>
              <MenuItem value='25'>25</MenuItem>
              <MenuItem value='50'>50</MenuItem>
            </CustomTextField>
            <Typography variant='body2' color='text.secondary' className='hidden sm:block'>
              entries per page
            </Typography>

            {/* Selection Counter for Desktop */}
            {Object.keys(rowSelection).length > 0 && (
              <div className='flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-full border border-primary/20'>
                <i className='tabler-check-circle text-primary text-sm' />
                <Typography variant='caption' className='font-medium text-primary'>
                  {Object.keys(rowSelection).length} selected
                </Typography>
                <Button
                  size='small'
                  variant='text'
                  onClick={() => setRowSelection({})}
                  className='text-primary p-1 min-w-auto'
                  sx={{ fontSize: '0.7rem', padding: '2px 6px' }}
                >
                  Clear
                </Button>
              </div>
            )}
          </div>
          <div className='flex flex-col sm:flex-row w-full lg:w-auto items-start sm:items-center gap-4'>
            <DebouncedInput
              value={globalFilter ?? ''}
              onChange={value => setGlobalFilter(String(value))}
              placeholder='Search contacts...'
              className='w-full sm:w-[250px]'
              size='small'
            />
            <Button
              color='secondary'
              variant='tonal'
              startIcon={<i className='tabler-file-type-pdf' />}
              className='w-full sm:w-auto'
              size='small'
              onClick={exportSelectedToPDF}
              disabled={Object.keys(rowSelection).length === 0}
            >
              Export PDF ({Object.keys(rowSelection).length})
            </Button>

            <IconButton
              color='primary'
              onClick={loadContacts}
              disabled={loading}
              title={loading ? 'Loading...' : 'Refresh Data'}
              sx={{
                border: '1px solid',
                borderColor: 'primary.main',
                '&:hover': {
                  backgroundColor: 'primary.light',
                  transform: 'scale(1.05)'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <i className={`tabler-refresh ${loading ? 'animate-spin' : ''}`} />
            </IconButton>
          </div>
        </div>
        <div className='overflow-x-auto overflow-y-hidden'>
          <div className='min-w-[580px] sm:min-w-[780px] lg:min-w-[980px]'>
            <table className={`${tableStyles.table} w-full table-fixed`}>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header, index) => {
                    // Define column widths
                    let width = 'auto'
                    if (header.id === 'select') width = '50px'
                    else if (header.id === 'fullName') width = '30%'
                    else if (header.id === 'role') width = '20%'
                    else if (header.id === 'billing') width = '20%'
                    else if (header.id === 'status') width = '18%'
                    else if (header.id === 'action') width = '120px'

                    return (
                      <th
                        key={header.id}
                        className={`px-3 sm:px-4 py-2 sm:py-3 ${header.id === 'action' ? 'pr-6' : ''} ${header.id === 'status' ? 'pr-3' : ''} ${header.id === 'role' ? 'pr-3' : ''} ${header.id === 'billing' ? 'pr-3' : ''}`}
                        style={{ width }}
                      >
                        {header.isPlaceholder ? null : (
                          <>
                            <div
                              className={classnames({
                                'flex items-center': header.column.getIsSorted(),
                                'cursor-pointer select-none': header.column.getCanSort()
                              })}
                              onClick={header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined}
                            >
                              <Typography
                                variant='body2'
                                className='font-semibold text-xs sm:text-sm'
                                color='text.primary'
                              >
                                {flexRender(header.column.columnDef.header, header.getContext())}
                              </Typography>
                              {header.column.getCanSort() && (
                                {
                                  asc: <i className='tabler-chevron-up text-lg sm:text-xl ml-1' />,
                                  desc: <i className='tabler-chevron-down text-lg sm:text-xl ml-1' />
                                }[header.column.getIsSorted()] ?? null
                              )}
                            </div>
                          </>
                        )}
                      </th>
                    )
                  })}
                </tr>
              ))}
            </thead>
            {table.getFilteredRowModel().rows.length === 0 ? (
              <tbody>
                <tr>
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center'>
                    No data available
                  </td>
                </tr>
              </tbody>
            ) : (
              <tbody>
                {table
                  .getRowModel()
                  .rows.slice(0, table.getState().pagination.pageSize)
                  .map(row => {
                    return (
                      <tr key={row.id} className={classnames({ selected: row.getIsSelected() })}>
                        {row.getVisibleCells().map((cell, index) => {
                          // Define column widths to match header
                          let width = 'auto'
                          if (cell.column.id === 'select') width = '50px'
                          else if (cell.column.id === 'fullName') width = '30%'
                          else if (cell.column.id === 'role') width = '20%'
                          else if (cell.column.id === 'billing') width = '20%'
                          else if (cell.column.id === 'status') width = '18%'
                          else if (cell.column.id === 'action') width = '120px'

                          return (
                            <td
                              key={cell.id}
                              className={`px-3 sm:px-4 py-2 sm:py-3 ${cell.column.id === 'action' ? 'pr-6' : ''} ${cell.column.id === 'status' ? 'pr-3' : ''} ${cell.column.id === 'role' ? 'pr-3' : ''} ${cell.column.id === 'billing' ? 'pr-3' : ''}`}
                              style={{ width }}
                            >
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </td>
                          )
                        })}
                      </tr>
                    )
                  })}
              </tbody>
            )}
            </table>
          </div>
        </div>
        <TablePagination
          component={() => <TablePaginationComponent table={table} />}
          count={table.getFilteredRowModel().rows.length}
          rowsPerPage={table.getState().pagination.pageSize}
          page={table.getState().pagination.pageIndex}
          onPageChange={(_, page) => {
            table.setPageIndex(page)
          }}
        />
        </Card>
      )}

      <UserDetailsModal
        open={modalOpen}
        onClose={handleModalClose}
        userData={selectedUser}
      />
    </>
  )
}

export default UserListTable
