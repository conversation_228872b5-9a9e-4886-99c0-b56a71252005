// Next Imports
import { NextResponse } from 'next/server'

export async function GET(req) {
  try {
    // Get the URL parameters
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Missing userId parameter' },
        { status: 400 }
      )
    }

    // Forward the request to your backend
    const backendUrl = process.env.API_URL || 'http://localhost:8090'
    const checkUrl = `${backendUrl}/check-verification/${userId}`

    const response = await fetch(checkUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const data = await response.json()
      return NextResponse.json(
        { success: true, isVerified: data.isVerified },
        { status: 200 }
      )
    } else {
      return NextResponse.json(
        { success: false, message: 'Failed to check verification status' },
        { status: response.status }
      )
    }
  } catch (error) {
    console.error('Error checking verification status:', error)
    return NextResponse.json(
      { success: false, message: 'An error occurred while checking verification status' },
      { status: 500 }
    )
  }
}