const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const Login = require('./model/Login');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cam_transport');

async function createSuperAdmin() {
  try {
    console.log('🔍 Creating Super Admin User...');

    // Check if super admin already exists
    const existingSuperAdmin = await Login.findOne({ role: 'super_admin' });

    if (existingSuperAdmin) {
      console.log('✅ Super admin already exists:', existingSuperAdmin.email);
      console.log('   Username:', existingSuperAdmin.username);
      console.log('   Admin ID:', existingSuperAdmin.adminId);
      console.log('   Active:', existingSuperAdmin.isActive);
      console.log('   Verified:', existingSuperAdmin.isVerified);
      return;
    }

    // Create super admin user
    const email = '<EMAIL>';
    const password = 'admin123';
    const username = 'admin';

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate admin ID
    const adminCount = await Login.countDocuments();
    const adminId = `ADMIN_${String(adminCount + 1).padStart(3, '0')}`;

    const superAdmin = await Login.create({
      username: username,
      email: email,
      password: hashedPassword,
      adminId: adminId,
      role: 'super_admin',
      isActive: true,
      isVerified: true, // Auto-verify super admin
      ipAddress: 'System',
      location: 'System Created',
      company: 'CAM Transport ltd.',
      loginHistory: [{
        loginDate: new Date(),
        ipAddress: 'System',
        userAgent: 'System',
        location: 'Account Created',
        success: true
      }]
    });

    console.log('✅ Super admin created successfully!');
    console.log('   Email:', superAdmin.email);
    console.log('   Username:', superAdmin.username);
    console.log('   Password:', password);
    console.log('   Admin ID:', superAdmin.adminId);
    console.log('   Role:', superAdmin.role);
    console.log('   Active:', superAdmin.isActive);
    console.log('   Verified:', superAdmin.isVerified);

    console.log('\n🔑 Login Credentials:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);

  } catch (error) {
    console.error('❌ Error creating super admin:', error);
  } finally {
    mongoose.connection.close();
  }
}

createSuperAdmin();
