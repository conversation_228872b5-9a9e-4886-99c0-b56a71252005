const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

/**
 * Migration script to convert existing MFA users from per-device secrets to master secret
 * This script will:
 * 1. Find users with MFA enabled but no master secret
 * 2. Use the first device's secret as the master secret
 * 3. Remove individual secrets from devices
 * 4. Update the database
 */

async function migrateMFAToMasterSecret() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cam_transport', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('🔗 Connected to MongoDB');

        // Find users with MFA enabled but no master secret
        const usersToMigrate = await Login.find({
            mfaEnabled: true,
            $or: [
                { mfaSecret: { $exists: false } },
                { mfaSecret: null },
                { mfaSecret: '' }
            ]
        });

        console.log(`📊 Found ${usersToMigrate.length} users to migrate`);

        if (usersToMigrate.length === 0) {
            console.log('✅ No users need migration');
            return;
        }

        for (const user of usersToMigrate) {
            console.log(`\n🔄 Migrating user: ${user.username} (${user.email})`);

            // Find the first active device with a secret
            const deviceWithSecret = user.mfaDevices?.find(device => 
                device.isActive && device.secret
            );

            if (deviceWithSecret) {
                // Use the first device's secret as the master secret
                user.mfaSecret = deviceWithSecret.secret;
                console.log(`  ✅ Set master secret from device: ${deviceWithSecret.deviceName}`);

                // Remove secrets from all devices (they'll use the master secret now)
                user.mfaDevices.forEach(device => {
                    if (device.secret) {
                        delete device.secret;
                        console.log(`  🔧 Removed individual secret from device: ${device.deviceName}`);
                    }
                });

                // Save the updated user
                await user.save();
                console.log(`  💾 User migration completed`);
            } else {
                console.log(`  ⚠️  No active device with secret found for user ${user.username}`);
                console.log(`  🔧 Disabling MFA for this user - they'll need to set it up again`);
                
                // Disable MFA if no valid device found
                user.mfaEnabled = false;
                user.mfaDevices = [];
                user.backupCodes = [];
                await user.save();
            }
        }

        console.log('\n🎉 Migration completed successfully!');
        console.log('\n📋 Summary:');
        console.log(`  - Users migrated: ${usersToMigrate.length}`);
        console.log(`  - All users now use a single master secret`);
        console.log(`  - Users can now use the same QR code on multiple devices`);
        console.log(`  - Multiple authenticator apps will work with the same account`);

    } catch (error) {
        console.error('❌ Migration failed:', error);
    } finally {
        // Close the database connection
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

// Run the migration
if (require.main === module) {
    console.log('🚀 Starting MFA Master Secret Migration...');
    migrateMFAToMasterSecret()
        .then(() => {
            console.log('✅ Migration script completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Migration script failed:', error);
            process.exit(1);
        });
}

module.exports = { migrateMFAToMasterSecret };
