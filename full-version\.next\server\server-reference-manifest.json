{"node": {"7f1c457e0c0705539cd3bb2fecd1846801a604019a": {"workers": {"app/[lang]/pages/user-profile/page": {"moduleId": "[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[lang]/pages/user-profile/page": "rsc"}}, "7f27096b32fcebfe43c05866c28f62dafa12f2af15": {"workers": {"app/[lang]/pages/user-profile/page": {"moduleId": "[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[lang]/pages/user-profile/page": "rsc"}}, "7f8f15f4893a0c6661561b55de5e700090a567f747": {"workers": {"app/[lang]/pages/user-profile/page": {"moduleId": "[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[lang]/pages/user-profile/page": "rsc"}}, "7fa86eb7a919b6e5f5c31f51c0435a015785819d8a": {"workers": {"app/[lang]/pages/user-profile/page": {"moduleId": "[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[lang]/pages/user-profile/page": "action-browser"}}, "7fcb01234d46807f3b15bc50ada2dffdbd2b77f83d": {"workers": {"app/[lang]/pages/user-profile/page": {"moduleId": "[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[lang]/pages/user-profile/page": "rsc"}}, "7feff801cb850da8076429e1cb16b5d49fc5f9e303": {"workers": {"app/[lang]/pages/user-profile/page": {"moduleId": "[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/[lang]/pages/user-profile/page": "rsc"}}}, "edge": {}, "encryptionKey": "3KGK2IZEJivyYixf6KYUDaQdI235ZnG7L25SngUYmG8="}