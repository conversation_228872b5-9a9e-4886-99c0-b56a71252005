const mongoose = require('mongoose');
const chalk = require('chalk');
const dns = require('dns');
const { promisify } = require('util');
require('dotenv').config();

const dnsLookup = promisify(dns.lookup);

async function parseMongoURL(url) {
    try {
        const urlObj = new URL(url);
        return {
            protocol: urlObj.protocol,
            hostname: urlObj.hostname,
            port: urlObj.port || (urlObj.protocol === 'mongodb+srv:' ? 27017 : 27017),
            username: urlObj.username,
            password: urlObj.password ? '***HIDDEN***' : 'NOT_SET',
            database: urlObj.pathname.split('/')[1]?.split('?')[0] || 'NOT_SPECIFIED',
            params: Object.fromEntries(urlObj.searchParams)
        };
    } catch (error) {
        return { error: error.message };
    }
}

async function testDNSResolution(hostname) {
    try {
        const result = await dnsLookup(hostname);
        return { success: true, ip: result.address };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function detailedMongoTest() {
    console.log(chalk.blue.bold('🔍 Detailed MongoDB Atlas Diagnostic'));
    console.log('=' .repeat(50));

    // 1. Check environment variables
    console.log('\n📋 Environment Check:');
    console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`MONGO_URL exists: ${!!process.env.MONGO_URL}`);
    
    if (!process.env.MONGO_URL) {
        console.error(chalk.red('❌ MONGO_URL not found in environment'));
        return false;
    }

    // 2. Parse MongoDB URL
    console.log('\n🔗 MongoDB URL Analysis:');
    const urlInfo = await parseMongoURL(process.env.MONGO_URL);
    if (urlInfo.error) {
        console.error(chalk.red(`❌ Invalid MongoDB URL: ${urlInfo.error}`));
        return false;
    }

    console.log(`Protocol: ${urlInfo.protocol}`);
    console.log(`Hostname: ${urlInfo.hostname}`);
    console.log(`Port: ${urlInfo.port}`);
    console.log(`Username: ${urlInfo.username}`);
    console.log(`Password: ${urlInfo.password}`);
    console.log(`Database: ${urlInfo.database}`);
    console.log(`Parameters:`, JSON.stringify(urlInfo.params, null, 2));

    // 3. Test DNS resolution
    console.log('\n🌐 DNS Resolution Test:');
    if (urlInfo.hostname) {
        const dnsResult = await testDNSResolution(urlInfo.hostname);
        if (dnsResult.success) {
            console.log(chalk.green(`✅ DNS resolved: ${urlInfo.hostname} → ${dnsResult.ip}`));
        } else {
            console.log(chalk.red(`❌ DNS resolution failed: ${dnsResult.error}`));
            console.log(chalk.yellow('💡 This could indicate network connectivity issues'));
        }
    }

    // 4. Test different connection options
    console.log('\n🔌 Connection Tests:');
    
    const connectionOptions = [
        {
            name: 'Default Options',
            options: { ssl: process.env.NODE_ENV === 'production' }
        },
        {
            name: 'With SSL Enabled',
            options: { ssl: true }
        },
        {
            name: 'With Timeout Settings',
            options: { 
                ssl: true,
                serverSelectionTimeoutMS: 5000,
                connectTimeoutMS: 10000
            }
        },
        {
            name: 'Minimal Options',
            options: {}
        }
    ];

    for (const test of connectionOptions) {
        console.log(`\n🧪 Testing: ${test.name}`);
        try {
            console.log('Connecting...');
            await mongoose.connect(process.env.MONGO_URL, test.options);
            console.log(chalk.green('✅ Connection successful!'));
            
            // Test basic operation
            const admin = mongoose.connection.db.admin();
            const result = await admin.ping();
            console.log(chalk.green('✅ Database ping successful'));
            
            await mongoose.connection.close();
            console.log('🔌 Connection closed');
            return true;
            
        } catch (error) {
            console.log(chalk.red(`❌ Failed: ${error.message}`));
            
            // Analyze specific error types
            if (error.message.includes('authentication failed')) {
                console.log(chalk.yellow('💡 Authentication issue - check username/password'));
            } else if (error.message.includes('ENOTFOUND')) {
                console.log(chalk.yellow('💡 DNS resolution issue - check hostname'));
            } else if (error.message.includes('ETIMEDOUT')) {
                console.log(chalk.yellow('💡 Connection timeout - check network/firewall'));
            } else if (error.message.includes('whitelist')) {
                console.log(chalk.yellow('💡 IP whitelist issue - check Atlas network access'));
            }
        }
    }

    return false;
}

// Additional troubleshooting suggestions
function printTroubleshootingTips() {
    console.log('\n' + '=' .repeat(50));
    console.log(chalk.yellow.bold('🛠️  Troubleshooting Tips:'));
    console.log('\n1. MongoDB Atlas Network Access:');
    console.log('   - Ensure 0.0.0.0/0 is added to IP whitelist');
    console.log('   - Check if there are any other IP restrictions');
    
    console.log('\n2. Database User Permissions:');
    console.log('   - Verify the database user exists');
    console.log('   - Check if user has proper read/write permissions');
    console.log('   - Ensure password is correct');
    
    console.log('\n3. Cluster Status:');
    console.log('   - Check if the Atlas cluster is running');
    console.log('   - Verify cluster is not paused');
    
    console.log('\n4. Network Issues:');
    console.log('   - Try connecting from a different network');
    console.log('   - Check corporate firewall settings');
    console.log('   - Test with VPN on/off');
    
    console.log('\n5. Alternative Solutions:');
    console.log('   - Try using the connection string from Atlas dashboard');
    console.log('   - Use local MongoDB for development');
    console.log('   - Test with MongoDB Compass GUI tool');
}

// Run the diagnostic
if (require.main === module) {
    detailedMongoTest().then(success => {
        if (!success) {
            printTroubleshootingTips();
        }
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error(chalk.red.bold('💥 Diagnostic failed:'), error);
        printTroubleshootingTips();
        process.exit(1);
    });
}

module.exports = { detailedMongoTest };
