const mongoose = require('mongoose');
const Login = require('./model/Login');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cam_transport');

async function cleanupDuplicateAdmin() {
  try {
    console.log('🧹 Starting cleanup of duplicate admin users...');
    
    // Get all users
    const allUsers = await Login.find({}).select('_id username email role adminId isActive isVerified');
    
    console.log(`📊 Total users before cleanup: ${allUsers.length}`);
    
    if (allUsers.length > 0) {
      console.log('\n👥 Current users:');
      allUsers.forEach((user, index) => {
        console.log(`${index + 1}. ObjectId: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Admin ID: ${user.adminId}`);
        console.log(`   Active: ${user.isActive}`);
        console.log(`   Verified: ${user.isVerified}`);
        console.log('');
      });
    }
    
    // Find the super admin user
    const superAdmin = await Login.findOne({ role: 'super_admin' });
    
    if (!superAdmin) {
      console.log('❌ No super admin found! Cannot proceed with cleanup.');
      return;
    }
    
    console.log('👑 Super Admin found:');
    console.log(`   ObjectId: ${superAdmin._id}`);
    console.log(`   Email: ${superAdmin.email}`);
    console.log(`   Admin ID: ${superAdmin.adminId}`);
    console.log(`   Verified: ${superAdmin.isVerified}`);
    
    // Find all other admin users (not super_admin)
    const otherAdmins = await Login.find({ 
      role: { $ne: 'super_admin' },
      email: '<EMAIL>' 
    });
    
    if (otherAdmins.length > 0) {
      console.log(`\n🗑️ Found ${otherAdmins.length} duplicate admin user(s) to remove:`);
      
      for (const admin of otherAdmins) {
        console.log(`   - ObjectId: ${admin._id}, Role: ${admin.role}, Admin ID: ${admin.adminId}`);
      }
      
      // Delete the duplicate admin users
      const deleteResult = await Login.deleteMany({ 
        role: { $ne: 'super_admin' },
        email: '<EMAIL>' 
      });
      
      console.log(`✅ Deleted ${deleteResult.deletedCount} duplicate admin user(s)`);
    } else {
      console.log('✅ No duplicate admin users found');
    }
    
    // Ensure super admin is verified and active
    if (!superAdmin.isVerified || !superAdmin.isActive) {
      console.log('🔧 Updating super admin status...');
      
      superAdmin.isVerified = true;
      superAdmin.isActive = true;
      await superAdmin.save();
      
      console.log('✅ Super admin is now verified and active');
    }
    
    // Final check
    const finalUsers = await Login.find({}).select('_id username email role adminId isActive isVerified');
    
    console.log(`\n📊 Total users after cleanup: ${finalUsers.length}`);
    
    if (finalUsers.length > 0) {
      console.log('\n👥 Final users:');
      finalUsers.forEach((user, index) => {
        console.log(`${index + 1}. ObjectId: ${user._id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Admin ID: ${user.adminId}`);
        console.log(`   Active: ${user.isActive}`);
        console.log(`   Verified: ${user.isVerified}`);
        console.log('');
      });
    }
    
    console.log('🎉 Cleanup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Clear browser cache and cookies');
    console.log('2. Login <NAME_EMAIL>');
    console.log('3. The session will now use the correct super admin ObjectId');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    mongoose.connection.close();
  }
}

cleanupDuplicateAdmin();
