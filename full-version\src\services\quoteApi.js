import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8090';
const API_KEY = process.env.NEXT_PUBLIC_API_KEY;

/**
 * @typedef {Object} ContactInfo
 * @property {string} firstName
 * @property {string} lastName
 * @property {string} email
 * @property {string} phone
 * @property {string} [company]
 */

/**
 * @typedef {Object} ShipmentDetails
 * @property {string} freightType
 * @property {string} weight
 * @property {string} dimensions
 * @property {string} commodity
 */

/**
 * @typedef {Object} PickupDelivery
 * @property {string} pickupLocation
 * @property {string} deliveryLocation
 * @property {string} pickupDate
 * @property {string} deliveryDate
 */

/**
 * @typedef {Object} SpecialRequirements
 * @property {boolean} liftGate
 * @property {boolean} insideDelivery
 * @property {boolean} notification
 * @property {string} [other]
 */

/**
 * @typedef {Object} Quote
 * @property {string} _id
 * @property {ContactInfo} contactInfo
 * @property {ShipmentDetails} shipmentDetails
 * @property {PickupDelivery} pickupDelivery
 * @property {SpecialRequirements} [specialRequirements]
 * @property {string} createdAt
 * @property {string} updatedAt
 */

/**
 * @typedef {Object} QuotesResponse
 * @property {boolean} success
 * @property {string} message
 * @property {Quote[]} quotes
 */

/**
 * Fetches all quotes from the API
 * @returns {Promise<QuotesResponse>}
 * @throws {Error} If the API call fails
 */
export const fetchQuotes = async () => {
  if (!API_KEY) {
    throw new Error('API key is not configured. Please set NEXT_PUBLIC_API_KEY in your environment variables.');
  }

  try {
    const response = await axios.get(`${API_BASE_URL}/quote/get-quotes`, {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json'
      }
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          throw new Error('Invalid API key');
        case 404:
          // Return empty quotes array with success true
          return { success: true, message: 'No quotes found', quotes: [] };
        case 500:
          throw new Error('Server error occurred');
        default:
          throw new Error(`Unexpected error: ${error.response.data.message}`);
      }
    }
    throw new Error('Network error occurred');
  }
};

/**
 * Updates a quote's status
 * @param {string} quoteId - The ID of the quote to update
 * @param {string} status - The new status (pending, in-review, approved, rejected)
 * @returns {Promise<{success: boolean, message: string}>}
 * @throws {Error} If the API call fails
 */
export const updateQuoteStatus = async (quoteId, status) => {
  if (!API_KEY) {
    throw new Error('API key is not configured. Please set NEXT_PUBLIC_API_KEY in your environment variables.');
  }

  if (!quoteId) {
    throw new Error('Quote ID is required');
  }

  if (!status) {
    throw new Error('Status is required');
  }

  try {
    console.log('API: Updating quote status', quoteId, 'to', status);
    console.log('API URL:', `${API_BASE_URL}/quote/update-status/${quoteId}`);

    const response = await axios.patch(`${API_BASE_URL}/quote/update-status/${quoteId}`,
      { status },
      {
        headers: {
          'x-api-key': API_KEY,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('API Response status:', response.status);
    console.log('API Success result:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating quote status:', error);
    if (error.response) {
      switch (error.response.status) {
        case 401:
          throw new Error('Invalid API key');
        case 404:
          throw new Error('Quote not found');
        case 500:
          throw new Error('Server error occurred while updating quote status');
        default:
          throw new Error(`Unexpected error: ${error.response.data.message || 'Failed to update quote status'}`);
      }
    }
    throw new Error('Network error occurred while updating quote status');
  }
};

/**
 * Deletes a quote by ID
 * @param {string} quoteId - The ID of the quote to delete
 * @returns {Promise<{success: boolean, message: string}>}
 * @throws {Error} If the API call fails
 */
export const deleteQuote = async (quoteId) => {
  if (!API_KEY) {
    throw new Error('API key is not configured. Please set NEXT_PUBLIC_API_KEY in your environment variables.');
  }

  if (!quoteId) {
    throw new Error('Quote ID is required');
  }

  try {
    const response = await axios.delete(`${API_BASE_URL}/quote/delete-quote/${quoteId}`, {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json'
      }
    });

    console.log('Quote deleted successfully:', response.data);
    return response.data;
  } catch (error) {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          throw new Error('Invalid API key');
        case 404:
          throw new Error('Quote not found');
        case 500:
          throw new Error('Server error occurred while deleting quote');
        default:
          throw new Error(`Unexpected error: ${error.response.data.message || 'Failed to delete quote'}`);
      }
    }
    throw new Error('Network error occurred while deleting quote');
  }
};
