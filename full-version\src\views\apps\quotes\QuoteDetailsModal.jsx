'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Avatar from '@mui/material/Avatar'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'

// PDF Export
import jsPDF from 'jspdf'
import 'jspdf-autotable'

const QuoteDetailsModal = ({ open, onClose, quoteData }) => {
  if (!quoteData) return null

  // Handle PDF download
  const handleDownloadPDF = async () => {
    try {
      const doc = new jsPDF()

      // Header
      doc.setFontSize(20)
      doc.setTextColor(40, 40, 40)
      doc.setFont(undefined, 'bold')
      doc.text('Quote Details Report', 20, 30)

      let yPosition = 50
      const lineHeight = 8

      // Quote header
      doc.setFontSize(16)
      doc.setTextColor(25, 118, 210) // Primary color
      doc.setFont(undefined, 'bold')
      doc.text(`Quote for ${quoteData.contactInfo?.fullName || 'Unknown Person'}`, 20, yPosition)
      yPosition += 12

      // Draw a line under the name
      doc.setDrawColor(25, 118, 210)
      doc.line(20, yPosition - 2, 190, yPosition - 2)
      yPosition += 8

      // Contact Information Section
      doc.setFontSize(12)
      doc.setTextColor(0, 0, 0)
      doc.setFont(undefined, 'bold')
      doc.text('Contact Information:', 20, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont(undefined, 'normal')

      const contactInfo = [
        `Full Name: ${quoteData.contactInfo?.fullName || 'Not provided'}`,
        `Email: ${quoteData.contactInfo?.emailAddress || 'Not provided'}`,
        `Phone: ${quoteData.contactInfo?.phoneNumber || 'Not provided'}`,
        `Company: ${quoteData.contactInfo?.companyName || 'Not provided'}`
      ]

      contactInfo.forEach(info => {
        doc.text(info, 20, yPosition)
        yPosition += lineHeight
      })

      yPosition += 5

      // Shipment Details Section
      doc.setFontSize(12)
      doc.setFont(undefined, 'bold')
      doc.text('Shipment Details:', 20, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont(undefined, 'normal')

      const shipmentDetails = [
        `Freight Type: ${quoteData.shipmentDetails?.loadType || 'Not specified'}`,
        `Weight: ${quoteData.shipmentDetails?.approximateWeight ?
          `${quoteData.shipmentDetails.approximateWeight.value} ${quoteData.shipmentDetails.approximateWeight.unit}` :
          'Not specified'}`,
        `Dimensions: ${quoteData.shipmentDetails?.loadDimensions ?
          `${quoteData.shipmentDetails.loadDimensions.length}x${quoteData.shipmentDetails.loadDimensions.width}x${quoteData.shipmentDetails.loadDimensions.height} ${quoteData.shipmentDetails.loadDimensions.unit}` :
          'Not specified'}`,
        `Commodity: ${quoteData.shipmentDetails?.itemDescription || 'Not specified'}`
      ]

      shipmentDetails.forEach(detail => {
        doc.text(detail, 20, yPosition)
        yPosition += lineHeight
      })

      yPosition += 5

      // Pickup & Delivery Section
      doc.setFontSize(12)
      doc.setFont(undefined, 'bold')
      doc.text('Pickup & Delivery:', 20, yPosition)
      yPosition += 8

      doc.setFontSize(10)
      doc.setFont(undefined, 'normal')

      const pickupDelivery = [
        `Pickup Location: ${quoteData.pickupDelivery?.pickupLocation ?
          `${quoteData.pickupDelivery.pickupLocation.city}, ${quoteData.pickupDelivery.pickupLocation.stateOrProvince}` :
          'Not specified'}`,
        `Delivery Location: ${quoteData.pickupDelivery?.deliveryLocation ?
          `${quoteData.pickupDelivery.deliveryLocation.city}, ${quoteData.pickupDelivery.deliveryLocation.stateOrProvince}` :
          'Not specified'}`,
        `Pickup Date: ${(() => {
          const formatDateTime = (dateString) => {
            if (!dateString) return 'Not specified'
            try {
              const date = new Date(dateString)
              if (isNaN(date.getTime())) return dateString
              const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
              const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }
              const formattedDate = date.toLocaleDateString('en-US', dateOptions)
              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)
              return `${formattedDate} ${formattedTime}`
            } catch (error) {
              return dateString
            }
          }
          return formatDateTime(quoteData.pickupDelivery?.preferredPickupDate)
        })()}`,
        `Delivery Date: ${(() => {
          const formatDateTime = (dateString) => {
            if (!dateString) return 'Not specified'
            try {
              const date = new Date(dateString)
              if (isNaN(date.getTime())) return dateString
              const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
              const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }
              const formattedDate = date.toLocaleDateString('en-US', dateOptions)
              const formattedTime = date.toLocaleTimeString('en-US', timeOptions)
              return `${formattedDate} ${formattedTime}`
            } catch (error) {
              return dateString
            }
          }
          return formatDateTime(quoteData.pickupDelivery?.preferredDeliveryDate)
        })()}`
      ]

      pickupDelivery.forEach(detail => {
        doc.text(detail, 20, yPosition)
        yPosition += lineHeight
      })

      // Footer
      yPosition += 20
      doc.setFontSize(8)
      doc.setTextColor(100, 100, 100)
      doc.text(`Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 20, yPosition)
      doc.text('CAM Transport - Quote Management System', 20, yPosition + 5)

      // Save the PDF
      const fileName = `quote-${quoteData.contactInfo?.fullName?.replace(/\s+/g, '-') || 'unknown'}-${new Date().toISOString().split('T')[0]}.pdf`
      doc.save(fileName)
    } catch (error) {
      console.error('Error generating PDF:', error)
      alert('Failed to generate PDF. Please try again.')
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h4" style={{ fontSize: '1.8rem' }}>Quote Details</Typography>
        <div className="flex items-center gap-2">
          <Button
            variant="outlined"
            color="primary"
            size="large"
            startIcon={<i className="tabler-file-type-pdf" />}
            onClick={handleDownloadPDF}
            className="font-bold"
            sx={{ fontSize: '1rem', padding: '12px 24px' }}
          >
            Download PDF
          </Button>
          <IconButton onClick={onClose}>
            <i className="tabler-x" />
          </IconButton>
        </div>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={6}>
          {/* Contact Profile Section */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <div className="flex items-center gap-6 mb-6">
                  <Avatar
                    sx={{ width: 120, height: 120, fontSize: '3.5rem', fontWeight: 'bold', backgroundColor: '#1976d2' }}
                  >
                    {quoteData.contactInfo?.fullName?.charAt(0)?.toUpperCase() || 'Q'}
                  </Avatar>
                  <div>
                    <Typography variant="h3" className="mb-3 font-bold" style={{ fontSize: '2.2rem' }}>
                      {quoteData.contactInfo?.fullName || 'Unknown Contact'}
                    </Typography>
                    <Typography variant="h5" color="text.secondary" className="font-medium" style={{ fontSize: '1.4rem' }}>
                      {quoteData.contactInfo?.emailAddress || 'No email provided'}
                    </Typography>
                  </div>
                </div>

                <Divider className="mb-6" />

                {/* Contact Details Grid */}
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Phone Number
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {quoteData.contactInfo?.phoneNumber || 'Not provided'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Company
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {quoteData.contactInfo?.companyName || 'Not provided'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Preferred Contact Method
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {quoteData.contactInfo?.preferredContactMethod || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-3 font-medium" style={{ fontSize: '1.2rem' }}>
                      Quote Date
                    </Typography>
                    <Typography variant="h5" className="font-bold" style={{ fontSize: '1.2rem' }}>
                      {(() => {
                        const formatDateTime = (dateString) => {
                          if (!dateString) return 'Not available'

                          try {
                            const date = new Date(dateString)
                            if (isNaN(date.getTime())) return dateString

                            const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
                            const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }

                            const formattedDate = date.toLocaleDateString('en-US', dateOptions)
                            const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

                            return `${formattedDate} ${formattedTime}`
                          } catch (error) {
                            return dateString
                          }
                        }

                        return formatDateTime(quoteData.createdAt)
                      })()}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Shipment Details Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card className="mb-4">
              <CardContent>
                <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                  SHIPMENT DETAILS
                </Typography>
                <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  <strong>Freight Type:</strong> {quoteData.shipmentDetails?.loadType || 'Not specified'}<br />
                  <strong>Weight:</strong> {quoteData.shipmentDetails?.approximateWeight ?
                    `${quoteData.shipmentDetails.approximateWeight.value} ${quoteData.shipmentDetails.approximateWeight.unit}` :
                    'Not specified'}<br />
                  <strong>Dimensions:</strong> {quoteData.shipmentDetails?.loadDimensions ?
                    `${quoteData.shipmentDetails.loadDimensions.length}x${quoteData.shipmentDetails.loadDimensions.width}x${quoteData.shipmentDetails.loadDimensions.height} ${quoteData.shipmentDetails.loadDimensions.unit}` :
                    'Not specified'}<br />
                  <strong>Commodity:</strong> {quoteData.shipmentDetails?.itemDescription || 'Not specified'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Pickup & Delivery Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card className="mb-4">
              <CardContent>
                <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                  PICKUP & DELIVERY
                </Typography>
                <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                  <strong>Pickup Location:</strong> {quoteData.pickupDelivery?.pickupLocation ?
                    `${quoteData.pickupDelivery.pickupLocation.city}, ${quoteData.pickupDelivery.pickupLocation.stateOrProvince}` :
                    'Not specified'}<br />
                  <strong>Delivery Location:</strong> {quoteData.pickupDelivery?.deliveryLocation ?
                    `${quoteData.pickupDelivery.deliveryLocation.city}, ${quoteData.pickupDelivery.deliveryLocation.stateOrProvince}` :
                    'Not specified'}<br />
                  <strong>Pickup Date:</strong> {(() => {
                    const formatDateTime = (dateString) => {
                      if (!dateString) return 'Not specified'
                      try {
                        const date = new Date(dateString)
                        if (isNaN(date.getTime())) return dateString
                        const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
                        const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }
                        const formattedDate = date.toLocaleDateString('en-US', dateOptions)
                        const formattedTime = date.toLocaleTimeString('en-US', timeOptions)
                        return `${formattedDate} ${formattedTime}`
                      } catch (error) {
                        return dateString
                      }
                    }
                    return formatDateTime(quoteData.pickupDelivery?.preferredPickupDate)
                  })()}<br />
                  <strong>Delivery Date:</strong> {(() => {
                    const formatDateTime = (dateString) => {
                      if (!dateString) return 'Not specified'
                      try {
                        const date = new Date(dateString)
                        if (isNaN(date.getTime())) return dateString
                        const dateOptions = { month: '2-digit', day: '2-digit', year: 'numeric' }
                        const timeOptions = { hour: '2-digit', minute: '2-digit', hour12: true }
                        const formattedDate = date.toLocaleDateString('en-US', dateOptions)
                        const formattedTime = date.toLocaleTimeString('en-US', timeOptions)
                        return `${formattedDate} ${formattedTime}`
                      } catch (error) {
                        return dateString
                      }
                    }
                    return formatDateTime(quoteData.pickupDelivery?.preferredDeliveryDate)
                  })()}<br />
                  <strong>Delivery Assistance Required:</strong> {typeof quoteData.pickupDelivery?.deliveryAssistanceRequired === 'boolean' ?
                    (quoteData.pickupDelivery.deliveryAssistanceRequired ? 'Yes' : 'No') :
                    'Not specified'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Special Requirements Section */}
          {quoteData.specialRequirements && (
            <Grid size={{ xs: 12 }}>
              <Card className="mb-4">
                <CardContent>
                  <Typography variant="h5" className="font-bold mb-3" style={{ fontSize: '1.4rem', borderBottom: '2px solid #1976d2', paddingBottom: '4px' }}>
                    SPECIAL REQUIREMENTS
                  </Typography>
                  <Typography variant="body1" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                    <strong>Delivery Type:</strong> {quoteData.specialRequirements.deliveryType || 'Not specified'}<br />
                    <strong>Requires Permits/Escorts:</strong> {quoteData.specialRequirements.requiresPermitsOrEscorts ? 'Yes' : 'No'}<br />
                    <strong>Special Handling Instructions:</strong> {quoteData.specialRequirements.specialHandlingInstructions || 'None specified'}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default QuoteDetailsModal
