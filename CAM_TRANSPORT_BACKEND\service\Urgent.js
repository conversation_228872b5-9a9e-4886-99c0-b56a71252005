const multer = require('multer');
const path = require('path');

const storageUrgent = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/urgent-inquiries');
    },
    filename: (req, file, cb) => {
        const ext = path.extname(file.originalname);
        cb(null, `${file.fieldname}-${Date.now()}${ext}`);
    }
});

const fileFilterUrgent = (req, file, cb) => {
    const allowedExt = ['.pdf', '.doc', '.docx', '.png', '.jpeg', '.jpg'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedExt.includes(ext)) {
        cb(null, true);
    } else {
        cb(new Error('Unsupported file format.'));
    }
};

const uploadUrgent = multer({ storage: storageUrgent, fileFilter: fileFilterUrgent });

module.exports = uploadUrgent;
