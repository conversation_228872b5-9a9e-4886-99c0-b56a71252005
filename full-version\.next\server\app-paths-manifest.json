{"/[lang]/(blank-layout-pages)/(guest-only)/login/page": "app/[lang]/(blank-layout-pages)/(guest-only)/login/page.js", "/[lang]/(blank-layout-pages)/pages/auth/mfa-verify/page": "app/[lang]/(blank-layout-pages)/pages/auth/mfa-verify/page.js", "/[lang]/(dashboard)/(private)/apps/mfa-settings/page": "app/[lang]/(dashboard)/(private)/apps/mfa-settings/page.js", "/[lang]/(dashboard)/(private)/apps/quotes/page": "app/[lang]/(dashboard)/(private)/apps/quotes/page.js", "/[lang]/(dashboard)/(private)/apps/user/contact/page": "app/[lang]/(dashboard)/(private)/apps/user/contact/page.js", "/[lang]/(dashboard)/(private)/apps/user/jobs/page": "app/[lang]/(dashboard)/(private)/apps/user/jobs/page.js", "/[lang]/(dashboard)/(private)/apps/user/urgent/page": "app/[lang]/(dashboard)/(private)/apps/user/urgent/page.js", "/[lang]/[...not-found]/page": "app/[lang]/[...not-found]/page.js", "/[lang]/pages/user-profile/page": "app/[lang]/pages/user-profile/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/user-location/route": "app/api/user-location/route.js", "/favicon.ico/route": "app/favicon.ico/route.js"}