'use client'

// React Imports
import { useState, useEffect } from 'react'

// MUI Imports
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import CircularProgress from '@mui/material/CircularProgress'
import Alert from '@mui/material/Alert'

// Next Auth
import { useSession } from 'next-auth/react'

// API Imports
import { getUserProfile, formatDateTime } from '@/services/userProfileApi'
import { getCurrentUser } from '@/middleware/authCheck'

const AboutOverview = ({ data }) => {
  const { data: session } = useSession()

  // States for dynamic user data
  const [userProfile, setUserProfile] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        // Get current user from localStorage (set during email verification)
        const currentUser = getCurrentUser()

        if (!currentUser || !currentUser.id) {
          setError('No user session found. Please login again.')
          setLoading(false)
          return
        }

        console.log('🔍 Fetching user profile for current admin:', currentUser.adminId || currentUser.id)
        const profile = await getUserProfile(currentUser.id)
        setUserProfile(profile)
        setError(null)
      } catch (err) {
        console.error('❌ Error fetching user profile:', err)
        setError('Failed to load user profile')
      } finally {
        setLoading(false)
      }
    }

    fetchUserProfile()
  }, [])

  if (loading) {
    return (
      <Grid container spacing={6}>
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent className='flex justify-center items-center py-8'>
              <div className='flex flex-col items-center gap-4'>
                <CircularProgress />
                <Typography>Loading profile...</Typography>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    )
  }

  if (error) {
    return (
      <Grid container spacing={6}>
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Alert severity='error'>{error}</Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    )
  }

  return (
    <Grid container spacing={6}>
      <Grid size={{ xs: 12 }}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex flex-col gap-4'>
              <Typography className='uppercase' variant='body2' color='text.disabled'>
                About
              </Typography>
              <div className='flex flex-col gap-2'>
                <div className='flex items-center gap-2'>
                  <i className='tabler-id' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>Admin ID:</Typography>
                    <Typography color='primary.main' style={{ fontFamily: 'monospace', fontWeight: 'bold' }}>
                      {userProfile?.adminId || 'Not assigned'}
                    </Typography>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <i className='tabler-user' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>Username:</Typography>
                    <Typography>{userProfile?.username || 'Not available'}</Typography>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <i className='tabler-mail' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>Email:</Typography>
                    <Typography>{userProfile?.email || 'Not available'}</Typography>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <i className='tabler-building' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>Company:</Typography>
                    <Typography>{userProfile?.company || 'CAM Transport Ltd.'}</Typography>
                  </div>
                </div>

                {/* Additional dynamic fields */}
                <div className='flex items-center gap-2'>
                  <i className='tabler-clock' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>Last Login:</Typography>
                    <Typography>{formatDateTime(userProfile?.lastLoginDate) || 'Not available'}</Typography>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <i className='tabler-map-pin' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>Location:</Typography>
                    <Typography>{userProfile?.location || 'Unknown Location'}</Typography>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <i className='tabler-world' />
                  <div className='flex items-center flex-wrap gap-2'>
                    <Typography className='font-medium'>IP Address:</Typography>
                    <Typography color='text.secondary' style={{ fontFamily: 'monospace' }}>
                      {userProfile?.ipAddress || 'Not recorded'}
                    </Typography>
                  </div>
                </div>
              </div>

              {/* Admin Welcome Message */}
              <div className='flex flex-col gap-3 mt-4 p-5 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl border-l-4 border-primary shadow-sm'>
                <div className='flex items-center gap-2'>
                  <i className='tabler-crown text-yellow-600 text-lg' />
                  <Typography variant='h4' className='font-bold text-primary' style={{ fontFamily: 'Georgia, serif' }}>
                    Welcome, {userProfile?.username || 'Admin'}!
                  </Typography>
                </div>
                <Typography
                  variant='body1'
                  color='text.secondary'
                  className='leading-relaxed'
                  style={{
                    fontFamily: 'Poppins, sans-serif',
                    fontWeight: '400',
                    lineHeight: '1.6',
                    letterSpacing: '0.3px'
                  }}
                >
                  "As the administrator of CAM Transport Ltd., you hold the keys to our digital kingdom! 🗝️ Your expertise keeps our operations running smoothly and our team connected. Thank you for your dedication and leadership in making our transport solutions world-class!"
                </Typography>
                <div className='flex items-center justify-center gap-2 mt-2 p-2 bg-white/50 rounded-lg'>
                  <i className='tabler-shield-check text-green-600 text-sm' />
                  <Typography
                    variant='caption'
                    color='text.secondary'
                    className='font-semibold'
                    style={{
                      fontFamily: 'Roboto Mono, monospace',
                      letterSpacing: '0.5px'
                    }}
                  >
                    ADMIN ACCESS GRANTED • HAVE A PRODUCTIVE DAY!
                  </Typography>
                  <i className='tabler-shield-check text-green-600 text-[40px]' />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default AboutOverview
