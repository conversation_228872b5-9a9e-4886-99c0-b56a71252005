const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function fixDuplicateAdmin() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/cam_transport', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('🔗 Connected to MongoDB');

        // Find all admin users
        const adminUsers = await Login.find({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' },
                { adminId: 'ADMIN_001' }
            ]
        });

        console.log(`📊 Found ${adminUsers.length} admin users`);

        if (adminUsers.length <= 1) {
            console.log('✅ No duplicate admin users found');
            return;
        }

        // Find the user with the correct MFA secret (JBSWY3DPEHPK3PXP)
        const correctUser = adminUsers.find(u => u.mfaSecret === 'JBSWY3DPEHPK3PXP');
        const otherUsers = adminUsers.filter(u => u.mfaSecret !== 'JBSWY3DPEHPK3PXP');

        if (!correctUser) {
            console.log('❌ No user found with the correct MFA secret');
            return;
        }

        console.log('🎯 Correct user found:', {
            id: correctUser._id,
            adminId: correctUser.adminId,
            mfaSecret: correctUser.mfaSecret
        });

        // First, remove the other duplicate users
        for (const user of otherUsers) {
            console.log(`🗑️  Removing duplicate user: ${user._id} (adminId: ${user.adminId})`);
            await Login.findByIdAndDelete(user._id);
        }

        console.log('✅ Duplicate admin users removed');

        // Now update the correct user to have adminId: "1"
        correctUser.adminId = '1';
        correctUser.isVerified = true; // Make sure it's verified
        await correctUser.save();

        console.log('✅ Updated correct user to have adminId: "1"');

        // Verify the fix
        const finalUser = await Login.findOne({ adminId: '1' });
        if (finalUser) {
            console.log('🔍 Final verification:');
            console.log(`  ID: ${finalUser._id}`);
            console.log(`  Admin ID: ${finalUser.adminId}`);
            console.log(`  Username: ${finalUser.username}`);
            console.log(`  Email: ${finalUser.email}`);
            console.log(`  MFA Secret: ${finalUser.mfaSecret}`);
            console.log(`  MFA Enabled: ${finalUser.mfaEnabled}`);
            console.log(`  Is Verified: ${finalUser.isVerified}`);
            console.log(`  Active Devices: ${finalUser.mfaDevices?.filter(d => d.isActive).length || 0}`);
            
            if (finalUser.mfaSecret === 'JBSWY3DPEHPK3PXP') {
                console.log('✅ Perfect! Now both endpoints will use the same user with the correct secret');
                console.log('🔐 Your authenticator apps should use secret: JBSWY3DPEHPK3PXP');
            } else {
                console.log('❌ Something went wrong - the secret is not correct');
            }
        }

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

fixDuplicateAdmin();
