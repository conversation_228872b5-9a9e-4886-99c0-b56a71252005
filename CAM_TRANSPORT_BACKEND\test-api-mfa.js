const speakeasy = require('speakeasy');

const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';
const API_BASE_URL = 'http://localhost:8090';

async function testMFAAPI() {
    try {
        // Generate current token
        const currentToken = speakeasy.totp({
            secret: ADMIN_SECRET,
            encoding: 'base32'
        });

        console.log('🧪 Testing MFA API');
        console.log('==================');
        console.log(`Current token: ${currentToken}`);
        console.log(`Time: ${new Date().toLocaleString()}`);
        console.log('');

        // Test the login endpoint (MFA step)
        console.log('1️⃣ Testing /login endpoint (MFA step)...');
        
        const loginResponse = await fetch(`${API_BASE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'admin',
                email: '<EMAIL>',
                password: 'verified',
                mfaToken: currentToken,
                step: 'mfa'
            })
        });

        const loginResult = await loginResponse.json();
        console.log('Login endpoint result:', loginResult.success ? '✅ SUCCESS' : '❌ FAILED');
        if (!loginResult.success) {
            console.log('Error:', loginResult.message);
        }
        console.log('');

        // Test the MFA verify endpoint
        console.log('2️⃣ Testing /mfa/verify/1 endpoint...');
        
        const mfaResponse = await fetch(`${API_BASE_URL}/mfa/verify/1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: currentToken,
                isBackupCode: false
            })
        });

        const mfaResult = await mfaResponse.json();
        console.log('MFA verify endpoint result:', mfaResult.success ? '✅ SUCCESS' : '❌ FAILED');
        if (!mfaResult.success) {
            console.log('Error:', mfaResult.message);
        }
        console.log('');

        // Test with a wrong token
        console.log('3️⃣ Testing with wrong token (should fail)...');
        
        const wrongResponse = await fetch(`${API_BASE_URL}/mfa/verify/1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: '123456',
                isBackupCode: false
            })
        });

        const wrongResult = await wrongResponse.json();
        console.log('Wrong token result:', wrongResult.success ? '❌ UNEXPECTED SUCCESS' : '✅ CORRECTLY FAILED');
        console.log('');

        console.log('🎯 Summary:');
        console.log('- Both endpoints should accept the same token');
        console.log('- Your authenticator apps should show the same token');
        console.log('- If Microsoft Authenticator shows a different token, re-add the account');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testMFAAPI();
