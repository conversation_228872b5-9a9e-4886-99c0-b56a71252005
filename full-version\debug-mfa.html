<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MFA Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 MFA Debug Tool</h1>
        <p>This tool helps debug the MFA login flow for CAM Transport.</p>

        <div class="section info">
            <h3>📋 Admin Credentials</h3>
            <p><strong>Username:</strong> admin</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> admin</p>
            <p><strong>MFA Secret:</strong> JBSWY3DPEHPK3PXP</p>
        </div>

        <div class="section">
            <h3>🧪 Test 1: Regular Login</h3>
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult" class="log"></div>
        </div>

        <div class="section">
            <h3>🔐 Test 2: MFA Verification</h3>
            <input type="text" id="mfaToken" placeholder="Enter 6-digit MFA token" maxlength="6">
            <button onclick="testMFAVerification()">Test MFA Verification</button>
            <div id="mfaResult" class="log"></div>
        </div>

        <div class="section">
            <h3>📊 Test 3: Check MFA Status</h3>
            <button onclick="checkMFAStatus()">Check MFA Status</button>
            <div id="statusResult" class="log"></div>
        </div>

        <div class="section">
            <h3>🔄 Test 4: Reset Admin MFA</h3>
            <button onclick="resetAdminMFA()">Reset Admin MFA</button>
            <div id="resetResult" class="log"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8090';
        let currentUserId = null;

        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '';
            
            try {
                log('loginResult', '🚀 Testing login...');
                
                const response = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        email: '<EMAIL>',
                        password: 'admin'
                    })
                });

                const result = await response.json();
                log('loginResult', `Response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.user) {
                    currentUserId = result.user.id;
                    log('loginResult', `✅ Login successful! User ID: ${currentUserId}`);
                    log('loginResult', `🔐 MFA Enabled: ${result.user.mfaEnabled}`);
                    log('loginResult', `🔐 Requires MFA: ${result.user.requiresMFA}`);
                } else {
                    log('loginResult', `❌ Login failed: ${result.message}`);
                }
            } catch (error) {
                log('loginResult', `❌ Error: ${error.message}`);
            }
        }

        async function testMFAVerification() {
            const resultDiv = document.getElementById('mfaResult');
            const token = document.getElementById('mfaToken').value;
            resultDiv.textContent = '';
            
            if (!token || token.length !== 6) {
                log('mfaResult', '❌ Please enter a 6-digit MFA token');
                return;
            }

            try {
                log('mfaResult', `🔐 Testing MFA verification with token: ${token}`);
                
                const response = await fetch(`${API_BASE_URL}/login/mfa-verify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        email: '<EMAIL>',
                        password: 'verified',
                        mfaToken: token,
                        step: 'mfa'
                    })
                });

                const result = await response.json();
                log('mfaResult', `Response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log('mfaResult', '✅ MFA verification successful!');
                } else {
                    log('mfaResult', `❌ MFA verification failed: ${result.message}`);
                }
            } catch (error) {
                log('mfaResult', `❌ Error: ${error.message}`);
            }
        }

        async function checkMFAStatus() {
            const resultDiv = document.getElementById('statusResult');
            resultDiv.textContent = '';
            
            if (!currentUserId) {
                log('statusResult', '❌ Please run login test first to get user ID');
                return;
            }

            try {
                log('statusResult', `📊 Checking MFA status for user: ${currentUserId}`);
                
                const response = await fetch(`${API_BASE_URL}/mfa/status/${currentUserId}`);
                const result = await response.json();
                
                log('statusResult', `Response: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log('statusResult', `❌ Error: ${error.message}`);
            }
        }

        async function resetAdminMFA() {
            const resultDiv = document.getElementById('resetResult');
            resultDiv.textContent = '';
            
            try {
                log('resetResult', '🔄 Resetting admin MFA...');
                
                const response = await fetch(`${API_BASE_URL}/login/reset-admin-mfa`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                log('resetResult', `Response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log('resetResult', '✅ Admin MFA reset successful!');
                    log('resetResult', `🔑 New secret: ${result.data.secret}`);
                } else {
                    log('resetResult', `❌ Reset failed: ${result.message}`);
                }
            } catch (error) {
                log('resetResult', `❌ Error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
