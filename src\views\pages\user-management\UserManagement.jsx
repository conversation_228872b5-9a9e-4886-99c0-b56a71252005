'use client'

import { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Grid,
  Alert,
  Pagination,
  InputAdornment
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Visibility as ViewIcon,
  Security as SecurityIcon,
  CircularProgress
} from '@mui/icons-material'
import { toast } from 'react-toastify'
import { getCurrentUser, isSuperAdmin, hasPermission, getAuthHeaders } from '@/utils/auth'

const UserManagement = () => {
  const [error, setError] = useState(null)
  const [currentUser, setCurrentUser] = useState(null)
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [statistics, setStatistics] = useState({})
  const [openDialog, setOpenDialog] = useState(false)
  const [editingUser, setEditingUser] = useState(null)
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [userToDelete, setUserToDelete] = useState(null)
  const [viewUserOpen, setViewUserOpen] = useState(false)
  const [viewingUser, setViewingUser] = useState(null)

  // Filters and pagination
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    role: 'normal_user',
    password: '',
    company: 'CAM Transport ltd.'
  })

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

  // Initialize current user
  useEffect(() => {
    const user = getCurrentUser()
    setCurrentUser(user)
  }, [])

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError(null)
      const params = new URLSearchParams({
        page: currentPage,
        limit: 10,
        role: roleFilter,
        search: searchTerm,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      })

      const response = await fetch(`${API_BASE_URL}/api/users?${params}`, {
        headers: getAuthHeaders()
      })

      if (response.status === 401) {
        setError('Authentication required. Please log in as a Super Admin.')
        return
      }

      const result = await response.json()

      if (result.success) {
        setUsers(result.data.users)
        setTotalPages(result.data.pagination.totalPages)
        setTotalUsers(result.data.pagination.totalUsers)
        setStatistics(result.data.statistics)
      } else {
        setError(result.message || 'Failed to fetch users')
        toast.error(result.message || 'Failed to fetch users')
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      setError('Failed to connect to server')
      toast.error('Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  // Fetch user statistics
  const fetchStatistics = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/statistics`, {
        headers: getAuthHeaders()
      })

      if (response.status === 401) {
        return // Skip statistics if not authenticated
      }

      const result = await response.json()

      if (result.success) {
        setStatistics(result.data)
      }
    } catch (error) {
      console.error('Error fetching statistics:', error)
    }
  }

  useEffect(() => {
    fetchUsers()
    fetchStatistics()
  }, [currentPage, roleFilter, searchTerm])

  // Handle form submission
  const handleSubmit = async () => {
    try {
      const url = editingUser
        ? `${API_BASE_URL}/api/users/${editingUser._id}`
        : `${API_BASE_URL}/api/users`

      const method = editingUser ? 'PUT' : 'POST'
      const payload = editingUser
        ? { ...formData, newPassword: formData.password || undefined }
        : formData

      const response = await fetch(url, {
        method,
        headers: getAuthHeaders(),
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (result.success) {
        toast.success(editingUser ? 'User updated successfully' : 'User created successfully')
        setOpenDialog(false)
        resetForm()
        fetchUsers()
        fetchStatistics()
      } else {
        toast.error(result.message || 'Operation failed')
      }
    } catch (error) {
      console.error('Error saving user:', error)
      toast.error('Failed to save user')
    }
  }

  // Handle delete user
  const handleDeleteUser = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/${userToDelete._id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      })

      const result = await response.json()

      if (result.success) {
        toast.success('User deleted successfully')
        setDeleteConfirmOpen(false)
        setUserToDelete(null)
        fetchUsers()
        fetchStatistics()
      } else {
        toast.error(result.message || 'Failed to delete user')
      }
    } catch (error) {
      console.error('Error deleting user:', error)
      toast.error('Failed to delete user')
    }
  }

  // Handle role change
  const handleRoleChange = async (userId, newRole) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/users/${userId}/role`, {
        method: 'PATCH',
        headers: getAuthHeaders(),
        body: JSON.stringify({ role: newRole })
      })

      const result = await response.json()

      if (result.success) {
        toast.success(`User role changed to ${newRole}`)
        fetchUsers()
        fetchStatistics()
      } else {
        toast.error(result.message || 'Failed to change role')
      }
    } catch (error) {
      console.error('Error changing role:', error)
      toast.error('Failed to change role')
    }
  }

  const resetForm = () => {
    setFormData({
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      role: 'normal_user',
      password: '',
      company: 'CAM Transport ltd.'
    })
    setEditingUser(null)
  }

  const openEditDialog = (user) => {
    setEditingUser(user)
    setFormData({
      username: user.username || '',
      email: user.email || '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      phoneNumber: user.phoneNumber || '',
      role: user.role || 'normal_user',
      password: '',
      company: user.company || 'CAM Transport ltd.'
    })
    setOpenDialog(true)
  }

  const getRoleColor = (role) => {
    switch (role) {
      case 'super_admin':
        return 'error'
      case 'normal_user':
        return 'primary'
      default:
        return 'default'
    }
  }

  const getRoleIcon = (role) => {
    return role === 'super_admin' ? <AdminIcon /> : <PersonIcon />
  }

  const openViewDialog = (user) => {
    setViewingUser(user)
    setViewUserOpen(true)
  }

  return (
    <div className="p-6">
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Authentication Notice */}
      {currentUser && isSuperAdmin() ? (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Welcome, {currentUser.username}!</strong> You have Super Admin access to manage users.
          </Typography>
        </Alert>
      ) : (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>Access Restricted:</strong> This page requires Super Admin privileges.
            {currentUser ? `Current user: ${currentUser.username} (${currentUser.role})` : 'Please log in as a Super Admin.'}
          </Typography>
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} className="mb-6">
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Users
              </Typography>
              <Typography variant="h4">
                {statistics.totalUsers || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Super Admins
              </Typography>
              <Typography variant="h4">
                {statistics.totalSuperAdmins || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Normal Users
              </Typography>
              <Typography variant="h4">
                {statistics.totalNormalUsers || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                MFA Enabled
              </Typography>
              <Typography variant="h4">
                {statistics.totalMfaEnabled || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {statistics.mfaAdoptionRate || 0}% adoption
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Card>
        <CardHeader
          title="User Management"
          action={
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => {
                  fetchUsers()
                  fetchStatistics()
                }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  resetForm()
                  setOpenDialog(true)
                }}
                disabled={!hasPermission('canCreateUsers')}
              >
                Add User
              </Button>
            </Box>
          }
        />
        <CardContent>
          {/* Filters */}
          <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
            <TextField
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ minWidth: 250 }}
            />
            <FormControl sx={{ minWidth: 150 }}>
              <InputLabel>Role</InputLabel>
              <Select
                value={roleFilter}
                label="Role"
                onChange={(e) => setRoleFilter(e.target.value)}
              >
                <MenuItem value="all">All Roles</MenuItem>
                <MenuItem value="super_admin">Super Admin</MenuItem>
                <MenuItem value="normal_user">Normal User</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Users Table */}
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>MFA</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user._id}>
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {user.firstName && user.lastName
                              ? `${user.firstName} ${user.lastName}`
                              : user.username
                            }
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            @{user.username}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <Select
                            value={user.role}
                            onChange={(e) => handleRoleChange(user._id, e.target.value)}
                            displayEmpty
                            variant="outlined"
                            size="small"
                            disabled={!hasPermission('canManageRoles')}
                          >
                            <MenuItem value="normal_user">
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <PersonIcon fontSize="small" />
                                Normal User
                              </Box>
                            </MenuItem>
                            <MenuItem value="super_admin">
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <AdminIcon fontSize="small" />
                                Super Admin
                              </Box>
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </TableCell>
                      <TableCell>{user.phoneNumber || 'N/A'}</TableCell>
                      <TableCell>
                        <Chip
                          label={user.mfaEnabled ? 'Enabled' : 'Disabled'}
                          color={user.mfaEnabled ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {user.lastLoginDate
                          ? new Date(user.lastLoginDate).toLocaleDateString()
                          : 'Never'
                        }
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() => openViewDialog(user)}
                            color="info"
                            title="View Details"
                          >
                            <ViewIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => openEditDialog(user)}
                            color="primary"
                            title="Edit User"
                            disabled={!hasPermission('canEditUsers')}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => {
                              setUserToDelete(user)
                              setDeleteConfirmOpen(true)
                            }}
                            color="error"
                            title="Delete User"
                            disabled={!hasPermission('canDeleteUsers')}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(event, value) => setCurrentPage(value)}
                color="primary"
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit User Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone Number"
                value={formData.phoneNumber}
                onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                >
                  <MenuItem value="normal_user">Normal User</MenuItem>
                  <MenuItem value="super_admin">Super Admin</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Company"
                value={formData.company}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={editingUser ? 'New Password (leave blank to keep current)' : 'Password'}
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                required={!editingUser}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingUser ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={() => setDeleteConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete user "{userToDelete?.username}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteUser} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* View User Details Dialog */}
      <Dialog open={viewUserOpen} onClose={() => setViewUserOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <SecurityIcon />
            User Details
          </Box>
        </DialogTitle>
        <DialogContent>
          {viewingUser && (
            <Grid container spacing={3} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Username</Typography>
                <Typography variant="body1">{viewingUser.username}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Email</Typography>
                <Typography variant="body1">{viewingUser.email}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Full Name</Typography>
                <Typography variant="body1">
                  {viewingUser.firstName && viewingUser.lastName
                    ? `${viewingUser.firstName} ${viewingUser.lastName}`
                    : 'Not provided'
                  }
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Phone Number</Typography>
                <Typography variant="body1">{viewingUser.phoneNumber || 'Not provided'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Role</Typography>
                <Chip
                  icon={getRoleIcon(viewingUser.role)}
                  label={viewingUser.role === 'super_admin' ? 'Super Admin' : 'Normal User'}
                  color={getRoleColor(viewingUser.role)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Company</Typography>
                <Typography variant="body1">{viewingUser.company || 'Not provided'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">MFA Status</Typography>
                <Chip
                  label={viewingUser.mfaEnabled ? 'Enabled' : 'Disabled'}
                  color={viewingUser.mfaEnabled ? 'success' : 'default'}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Account Status</Typography>
                <Chip
                  label={viewingUser.isActive ? 'Active' : 'Inactive'}
                  color={viewingUser.isActive ? 'success' : 'error'}
                  size="small"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Last Login</Typography>
                <Typography variant="body1">
                  {viewingUser.lastLoginDate
                    ? new Date(viewingUser.lastLoginDate).toLocaleString()
                    : 'Never'
                  }
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Created At</Typography>
                <Typography variant="body1">
                  {viewingUser.createdAt
                    ? new Date(viewingUser.createdAt).toLocaleString()
                    : 'Unknown'
                  }
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Admin ID</Typography>
                <Typography variant="body1">{viewingUser.adminId || 'Not assigned'}</Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Verified</Typography>
                <Chip
                  label={viewingUser.isVerified ? 'Verified' : 'Not Verified'}
                  color={viewingUser.isVerified ? 'success' : 'warning'}
                  size="small"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewUserOpen(false)}>Close</Button>
          <Button
            onClick={() => {
              setViewUserOpen(false)
              openEditDialog(viewingUser)
            }}
            variant="contained"
          >
            Edit User
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  )
}

export default UserManagement
