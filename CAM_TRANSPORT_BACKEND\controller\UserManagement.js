const Login = require('../model/Login');
const bcrypt = require('bcrypt');
const { sendEmail } = require('../service/Mailer');
const crypto = require('crypto');

/**
 * Get all users with pagination and filtering
 */
const getAllUsers = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            search = '',
            role = '',
            status = '',
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        // Build filter query
        const filter = {};

        if (search) {
            filter.$or = [
                { username: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { company: { $regex: search, $options: 'i' } }
            ];
        }

        if (role) {
            filter.role = role;
        }

        if (status === 'active') {
            filter.isActive = true;
        } else if (status === 'inactive') {
            filter.isActive = false;
        }

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get users with pagination
        const users = await Login.find(filter)
            .select('-password -otp -otpExpiry -mfaSecret -backupCodes')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const totalUsers = await Login.countDocuments(filter);
        const totalPages = Math.ceil(totalUsers / parseInt(limit));

        console.log(`✅ Retrieved ${users.length} users (page ${page}/${totalPages})`);

        return res.status(200).json({
            success: true,
            users: users.map(user => ({
                id: user._id,
                adminId: user.adminId,
                username: user.username,
                email: user.email,
                role: user.role,
                isActive: user.isActive,
                isVerified: user.isVerified,
                mfaEnabled: user.mfaEnabled,
                lastLoginDate: user.lastLoginDate,
                company: user.company,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            })),
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalUsers,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });

    } catch (error) {
        console.error('❌ Error fetching users:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Create a new user
 */
const createUser = async (req, res) => {
    try {
        const { username, email, password, role = 'admin' } = req.body;

        // Validate required fields
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Username, email, and password are required'
            });
        }

        // Validate role
        if (!['admin', 'super_admin', 'normal_user'].includes(role)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid role. Must be admin, super_admin, or normal_user'
            });
        }

        // Check if user already exists
        const existingUser = await Login.findOne({
            $or: [{ email }, { username }]
        });

        if (existingUser) {
            return res.status(409).json({
                success: false,
                message: 'User with this email or username already exists'
            });
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Generate unique admin ID
        const adminId = `admin_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create new user
        const newUser = new Login({
            username: username.trim(),
            email: email.toLowerCase().trim(),
            password: hashedPassword,
            role,
            adminId,
            isActive: true,
            isVerified: false, // Will be verified via email OTP
            mfaEnabled: false, // Will be set up after first login
            company: 'CAM Transport ltd.'
        });

        await newUser.save();

        console.log(`✅ New user created: ${email} with role: ${role}`);

        // Send welcome email with login instructions
        // TODO: Implement email template for new user welcome

        return res.status(201).json({
            success: true,
            message: 'User created successfully',
            user: {
                id: newUser._id,
                username: newUser.username,
                email: newUser.email,
                role: newUser.role,
                isActive: newUser.isActive,
                isVerified: newUser.isVerified,
                createdAt: newUser.createdAt
            }
        });

    } catch (error) {
        console.error('❌ Error creating user:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Update an existing user
 */
const updateUser = async (req, res) => {
    try {
        const { userId: targetUserId } = req.params;
        const { username, email, role, isActive } = req.body;

        // Find the user to update
        const user = await Login.findById(targetUserId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Prevent super admin from being demoted by non-super admin
        if (user.role === 'super_admin' && req.user.role !== 'super_admin') {
            return res.status(403).json({
                success: false,
                message: 'Only super admin can modify super admin accounts'
            });
        }

        // Validate role if provided
        if (role && !['admin', 'super_admin', 'normal_user'].includes(role)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid role. Must be admin, super_admin, or normal_user'
            });
        }

        // Only super admin can assign super_admin role
        if (role === 'super_admin' && req.user.role !== 'super_admin') {
            return res.status(403).json({
                success: false,
                message: 'Only super admin can assign super_admin role'
            });
        }

        // Check for duplicate email/username if being changed
        if (email && email !== user.email) {
            const existingUser = await Login.findOne({ email: email.toLowerCase().trim() });
            if (existingUser) {
                return res.status(409).json({
                    success: false,
                    message: 'Email already exists'
                });
            }
        }

        if (username && username !== user.username) {
            const existingUser = await Login.findOne({ username: username.trim() });
            if (existingUser) {
                return res.status(409).json({
                    success: false,
                    message: 'Username already exists'
                });
            }
        }

        // Update user fields
        if (username) user.username = username.trim();
        if (email) user.email = email.toLowerCase().trim();
        if (role) user.role = role;
        if (typeof isActive === 'boolean') user.isActive = isActive;

        await user.save();

        console.log(`✅ User updated: ${user.email}`);

        return res.status(200).json({
            success: true,
            message: 'User updated successfully',
            user: {
                id: user._id,
                username: user.username,
                email: user.email,
                role: user.role,
                isActive: user.isActive,
                isVerified: user.isVerified,
                updatedAt: user.updatedAt
            }
        });

    } catch (error) {
        console.error('❌ Error updating user:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Delete a user
 */
const deleteUser = async (req, res) => {
    try {
        const { userId: targetUserId } = req.params;

        // Find the user to delete
        const user = await Login.findById(targetUserId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Prevent super admin from being deleted by non-super admin
        if (user.role === 'super_admin' && req.user.role !== 'super_admin') {
            return res.status(403).json({
                success: false,
                message: 'Only super admin can delete super admin accounts'
            });
        }

        // Prevent user from deleting themselves
        if (user._id.toString() === req.user._id.toString()) {
            return res.status(403).json({
                success: false,
                message: 'Cannot delete your own account'
            });
        }

        await Login.findByIdAndDelete(targetUserId);

        console.log(`✅ User deleted: ${user.email}`);

        return res.status(200).json({
            success: true,
            message: 'User deleted successfully'
        });

    } catch (error) {
        console.error('❌ Error deleting user:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

/**
 * Change user password
 */
const changeUserPassword = async (req, res) => {
    try {
        const { userId: targetUserId } = req.params;
        const { newPassword } = req.body;

        if (!newPassword || newPassword.length < 6) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 6 characters long'
            });
        }

        // Find the user
        const user = await Login.findById(targetUserId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Hash new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        user.password = hashedPassword;
        await user.save();

        console.log(`✅ Password changed for user: ${user.email}`);

        return res.status(200).json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('❌ Error changing password:', error);
        return res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
};

module.exports = {
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    changeUserPassword
};
