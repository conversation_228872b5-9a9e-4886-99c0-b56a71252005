const speakeasy = require('speakeasy');

// Test the fixed secret that should be used for admin user
const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';

console.log('🔐 Current Expected Token');
console.log('========================');

// Generate current TOTP token
const currentToken = speakeasy.totp({
    secret: ADMIN_SECRET,
    encoding: 'base32'
});

console.log(`Current Time: ${new Date().toLocaleString()}`);
console.log(`Expected Token: ${currentToken}`);
console.log('');
console.log('📱 Instructions:');
console.log('1. Check your Google Authenticator - it should show:', currentToken);
console.log('2. Check your Microsoft Authenticator - it should show:', currentToken);
console.log('3. If both show the same token, try logging in with either one');
console.log('4. If they show different tokens, there might be a setup issue');
console.log('');

// Show next few tokens
console.log('⏰ Next tokens (in case of timing issues):');
for (let i = 1; i <= 3; i++) {
    const futureTime = Math.floor(Date.now() / 1000) + (i * 30);
    const futureToken = speakeasy.totp({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        time: futureTime
    });
    const timeStr = new Date(futureTime * 1000).toLocaleTimeString();
    console.log(`${timeStr}: ${futureToken}`);
}

// Test verification
console.log('');
console.log('🧪 Verification Test:');
const verified = speakeasy.totp.verify({
    secret: ADMIN_SECRET,
    encoding: 'base32',
    token: currentToken,
    window: 1
});
console.log(`Token ${currentToken} verification: ${verified ? '✅ VALID' : '❌ INVALID'}`);

// Manual test function
function testToken(inputToken) {
    console.log(`\n🔍 Testing token: ${inputToken}`);
    
    const verified = speakeasy.totp.verify({
        secret: ADMIN_SECRET,
        encoding: 'base32',
        token: inputToken,
        window: 2
    });
    
    console.log(`Result: ${verified ? '✅ VALID' : '❌ INVALID'}`);
    
    if (!verified) {
        console.log('❌ This token is not valid for the current time window');
        console.log(`Expected: ${currentToken}`);
    } else {
        console.log('✅ This token should work for login!');
    }
}

// Export for manual testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { testToken, currentToken: currentToken };
}

console.log('');
console.log('💡 To test a specific token, run:');
console.log('const { testToken } = require("./test-current-token.js");');
console.log('testToken("123456"); // Replace with your token');
