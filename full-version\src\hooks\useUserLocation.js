'use client'

import { useState, useEffect } from 'react'

export const useUserLocation = () => {
  const [location, setLocation] = useState({
    ip: 'Loading...',
    city: 'Loading...',
    region: 'Loading...',
    country: 'Loading...',
    countryCode: 'XX',
    timezone: 'UTC',
    latitude: null,
    longitude: null,
    isp: 'Loading...',
    fullLocation: 'Loading location...',
    isLoading: true,
    error: null
  })

  useEffect(() => {
    const fetchLocation = async () => {
      try {
        const response = await fetch('/api/user-location')
        
        if (!response.ok) {
          throw new Error('Failed to fetch location')
        }
        
        const locationData = await response.json()
        
        setLocation({
          ...locationData,
          isLoading: false,
          error: null
        })
        
      } catch (error) {
        console.error('Error fetching user location:', error)
        
        setLocation(prev => ({
          ...prev,
          isLoading: false,
          error: error.message,
          city: 'Unknown City',
          fullLocation: 'Location unavailable'
        }))
      }
    }

    fetchLocation()
  }, [])

  return location
}
