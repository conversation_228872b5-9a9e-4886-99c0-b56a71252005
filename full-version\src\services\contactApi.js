// API service for contact management
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8090'

// Fetch all contacts from backend
export const fetchContacts = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/contact/get-contacts`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const contacts = await response.json()
    
    // Transform backend data to match frontend structure
    return contacts.map(contact => ({
      id: contact._id,
      fullName: contact.name,
      username: contact.name,
      email: contact.email,
      phone: contact.contact,
      contact: contact.contact,
      company: contact.company || 'CAM Transport',
      currentPlan: contact.company || 'CAM Transport',
      inquiryType: contact.inquiryType,
      type: contact.inquiryType,
      billing: contact.inquiryType,
      userMessage: contact.message,
      message: contact.message,
      otherInquiry: contact.otherInquiry,
      ip: contact.ip,
      status: contact.status || 'pending', // Use backend status or default to pending
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
      inquiryDate: contact.createdAt,
      // Add avatar placeholder
      avatar: null
    }))
  } catch (error) {
    console.error('Error fetching contacts:', error)
    throw error
  }
}

// Update contact status
export const updateContactStatus = async (contactId, status) => {
  try {
    console.log('API: Updating contact status', contactId, 'to', status)
    console.log('API URL:', `${API_BASE_URL}/contact/update-status/${contactId}`)

    const response = await fetch(`${API_BASE_URL}/contact/update-status/${contactId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status })
    })

    console.log('API Response status:', response.status)
    console.log('API Response ok:', response.ok)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('API Error response:', errorText)
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('API Success result:', result)
    return result
  } catch (error) {
    console.error('Error updating contact status:', error)
    throw error
  }
}

// Delete contact from backend
export const deleteContact = async (contactId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/contact/delete-contact/${contactId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Error deleting contact:', error)
    throw error
  }
}

// Get contact by ID
export const getContactById = async (contactId) => {
  try {
    const contacts = await fetchContacts()
    return contacts.find(contact => contact.id === contactId)
  } catch (error) {
    console.error('Error fetching contact by ID:', error)
    throw error
  }
}
