<!DOCTYPE html>
<html>
<head>
    <title>Resume Download Test</title>
</head>
<body>
    <h1>Resume Download Test</h1>
    <button onclick="testResumeDirectory()">Test Resume Directory</button>
    <button onclick="testJobApplications()">Test Job Applications</button>
    <button onclick="testDownloadResume()">Test Download Resume</button>
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8090';

        async function testResumeDirectory() {
            try {
                const response = await fetch(`${API_BASE_URL}/jobs/test-resumes`);
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h3>Resume Directory Test:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Resume Directory Test Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testJobApplications() {
            try {
                const response = await fetch(`${API_BASE_URL}/jobs/get-jobs`);
                const data = await response.json();
                document.getElementById('results').innerHTML = `
                    <h3>Job Applications Test:</h3>
                    <p>Found ${data.length} applications</p>
                    <pre>${JSON.stringify(data.slice(0, 2), null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Job Applications Test Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        async function testDownloadResume() {
            try {
                // First get job applications to find a resume
                const appsResponse = await fetch(`${API_BASE_URL}/jobs/get-jobs`);
                const apps = await appsResponse.json();
                
                if (apps.length === 0) {
                    document.getElementById('results').innerHTML = `
                        <h3>Download Test Error:</h3>
                        <pre>No job applications found to test with</pre>
                    `;
                    return;
                }

                const firstApp = apps[0];
                if (!firstApp.resume) {
                    document.getElementById('results').innerHTML = `
                        <h3>Download Test Error:</h3>
                        <pre>First application has no resume file</pre>
                    `;
                    return;
                }

                const filename = firstApp.resume.split('/').pop() || firstApp.resume;
                
                // Test download
                const downloadResponse = await fetch(`${API_BASE_URL}/jobs/download-resume/${filename}`);
                
                document.getElementById('results').innerHTML = `
                    <h3>Download Test Result:</h3>
                    <p>Application: ${firstApp.first_name} ${firstApp.last_name}</p>
                    <p>Resume path: ${firstApp.resume}</p>
                    <p>Filename: ${filename}</p>
                    <p>Download URL: ${API_BASE_URL}/jobs/download-resume/${filename}</p>
                    <p>Response status: ${downloadResponse.status}</p>
                    <p>Response ok: ${downloadResponse.ok}</p>
                `;
                
                if (downloadResponse.ok) {
                    const blob = await downloadResponse.blob();
                    document.getElementById('results').innerHTML += `
                        <p>File size: ${blob.size} bytes</p>
                        <p>File type: ${blob.type}</p>
                        <button onclick="downloadBlob('${filename}', '${blob.type}')">Download File</button>
                    `;
                } else {
                    const errorText = await downloadResponse.text();
                    document.getElementById('results').innerHTML += `
                        <p>Error: ${errorText}</p>
                    `;
                }
                
            } catch (error) {
                document.getElementById('results').innerHTML = `
                    <h3>Download Test Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }

        function downloadBlob(filename, type) {
            // This is just for testing - in real app we'd use the actual blob
            alert('Download test successful! File: ' + filename + ', Type: ' + type);
        }
    </script>
</body>
</html>
