const { testEmailService } = require('./service/SimpleMailer');

async function runTest() {
    console.log('🧪 Testing Simple Email Service...');
    
    const success = await testEmailService();
    
    if (success) {
        console.log('✅ Simple email service is working!');
        console.log('📧 Check <EMAIL> for the test email.');
    } else {
        console.log('❌ Simple email service failed.');
        console.log('💡 Make sure the PASS variable in .env is a valid Gmail app password.');
    }
}

if (require.main === module) {
    runTest().then(() => {
        process.exit(0);
    }).catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
