
require('dotenv').config()


const api_key_auth = (req, res, next) => {
    const apiKey = req.header('x-api-key');

    if (!apiKey) {
        return res.status(401).json({ message: 'Access Denied: No API Key Provided!' });
    }

    if (apiKey === process.env.API_KEY) {
        next();
    } else {
        return res.status(401).json({ message: 'Access Denied: Invalid API Key!' });
    }
};

module.exports = api_key_auth; 