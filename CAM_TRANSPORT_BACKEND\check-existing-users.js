const mongoose = require('mongoose');
const Login = require('./model/Login');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/cam_transport');

async function checkUsers() {
  try {
    console.log('🔍 Checking existing users...');
    
    // Get all users
    const allUsers = await Login.find({}).select('username email role isActive isVerified adminId');
    
    console.log(`📊 Total users: ${allUsers.length}`);
    
    if (allUsers.length > 0) {
      console.log('\n👥 All users:');
      allUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.username} (${user.email})`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Admin ID: ${user.adminId}`);
        console.log(`   Active: ${user.isActive}`);
        console.log(`   Verified: ${user.isVerified}`);
        console.log('');
      });
    }
    
    // Check specifically for super_admin role
    const superAdmins = await Login.find({ role: 'super_admin' });
    console.log(`👑 Users with role 'super_admin': ${superAdmins.length}`);
    
    // Check for other admin roles
    const admins = await Login.find({ role: { $regex: /admin/i } });
    console.log(`🔧 Users with admin-like roles: ${admins.length}`);
    
    if (admins.length > 0) {
      console.log('\n🔧 Admin users:');
      admins.forEach((user, index) => {
        console.log(`${index + 1}. ${user.username} - Role: ${user.role}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkUsers();
