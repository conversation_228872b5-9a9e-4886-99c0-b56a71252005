const Netflix = props => {
  return (
    <svg width='88' height='35' viewBox='0 0 111 30' xmlns='http://www.w3.org/2000/svg' aria-hidden='true' {...props}>
      <path
        fill='currentColor'
        d='M105.062 14.28 111 30c-1.75-.25-3.499-.563-5.28-.845l-3.345-8.686-3.437 7.969c-1.687-.282-3.344-.376-5.031-.595l6.031-13.75L94.468 0h5.063l3.062 7.874L105.875 0h5.124zM90.47 0h-4.594v27.25c1.5.094 3.062.156 4.594.343zm-8.563 26.937c-4.187-.281-8.375-.53-12.656-.625V0h4.687v21.875c2.688.062 5.375.28 7.969.405zM64.25 10.657v4.687h-6.406V26H53.22V0h13.125v4.687h-8.5v5.97zm-18.906-5.97V26.25c-1.563 0-3.156 0-4.688.062V4.687h-4.844V0h14.406v4.687zM30.75 15.593c-2.062 0-4.5 0-6.25.095v6.968c2.75-.188 5.5-.406 8.281-.5v4.5l-12.968 1.032V0H32.78v4.687H24.5V11c1.813 0 4.594-.094 6.25-.094zM4.78 12.968v16.375A105 105 0 0 0 0 30V0h4.469l6.093 17.032V0h4.688v28.062c-1.656.282-3.344.376-5.125.625z'
      />
    </svg>
  )
}

export default Netflix
