const Joi = require('joi');
const { isValidPhoneNumber } = require('libphonenumber-js');


const noMongoKeys = (value, helpers) => {
    if (value.includes("$")) {
        return helpers.error("any.invalid");
    }
    return value;
};


const quoteValidationSchema = Joi.object({

    quoteType: Joi.string().valid('deck', 'quote').min(4).max(5).required()
        .messages({
            'any.only': 'Please select a valid quote type',
            'string.empty': 'Quote type is required',
            'string.min': 'Quote type must be at least 3 characters',
            'string.max': 'Quote type must be less than 100 characters'
        }),

    contactInfo: Joi.object({
        fullName: Joi.string().trim().min(3).max(100).custom(noMongoKeys).required()
            .messages({
                'string.empty': 'Full name is required',
                'string.min': 'Full name must be at least 3 characters',
                'string.max': 'Full name must be less than 100 characters',
                'any.invalid': 'Full name contains invalid characters'
            }),
        companyName: Joi.string().trim().max(200).allow('').custom(noMongoKeys)
            .messages({
                'string.max': 'Company name must be less than 200 characters',
                'any.invalid': 'Company name contains invalid characters'
            }),
        phoneNumber: Joi.string().trim().required()
            .custom((value, helpers) => {
                const cleaned = value.replace(/\s+/g, '');
                if (!isValidPhoneNumber(cleaned)) {
                    return helpers.error('any.invalid');
                }
                return value;
            })
            .messages({
                'any.invalid': 'Please enter a valid phone number',
                'string.empty': 'Phone number is required'
            }),
        emailAddress: Joi.string().trim().email({ tlds: false }).required()
            .messages({
                'string.email': 'Please enter a valid email address',
                'string.empty': 'Email is required'
            }),
        preferredContactMethod: Joi.string().valid('Phone', 'Email').required()
            .messages({
                'any.only': 'Please select a valid contact method',
                'string.empty': 'Preferred contact method is required'
            })
    }).required(),

    shipmentDetails: Joi.object({
        itemDescription: Joi.string().trim().min(10).max(500).required()
            .messages({
                'string.empty': 'Item description is required',
                'string.min': 'Item description must be at least 10 characters',
                'string.max': 'Item description must be less than 500 characters'
            }),
        loadType: Joi.string()
            .valid('Standard Flatbed', 'Step Deck', 'Lowboy', 'Oversized / Over dimensional')
            .required()
            .messages({
                'any.only': 'Please select a valid load type',
                'string.empty': 'Load type is required'
            }),
        loadDimensions: Joi.object({
            length: Joi.number().positive().required()
                .messages({
                    'number.base': 'Length must be a number',
                    'number.positive': 'Length must be positive'
                }),
            width: Joi.number().positive().required()
                .messages({
                    'number.base': 'Width must be a number',
                    'number.positive': 'Width must be positive'
                }),
            height: Joi.number().positive().required()
                .messages({
                    'number.base': 'Height must be a number',
                    'number.positive': 'Height must be positive'
                }),
            unit: Joi.string().valid('ft', 'm').required()
                .messages({
                    'any.only': 'Please select a valid unit',
                    'string.empty': 'Unit is required'
                })
        }).required(),
        approximateWeight: Joi.object({
            value: Joi.number().positive().required()
                .messages({
                    'number.base': 'Weight must be a number',
                    'number.positive': 'Weight must be positive'
                }),
            unit: Joi.string().valid('lbs', 'kgs').required()
                .messages({
                    'any.only': 'Please select a valid weight unit',
                    'string.empty': 'Weight unit is required'
                })
        }).required()
    }).required(),

    pickupDelivery: Joi.object({
        pickupLocation: Joi.object({
            city: Joi.string().trim().required()
                .messages({
                    'string.empty': 'Pickup city is required'
                }),
            stateOrProvince: Joi.string().trim().required()
                .messages({
                    'string.empty': 'Pickup state/province is required'
                })
        }).required(),
        deliveryLocation: Joi.object({
            city: Joi.string().trim().required()
                .messages({
                    'string.empty': 'Delivery city is required'
                }),
            stateOrProvince: Joi.string().trim().required()
                .messages({
                    'string.empty': 'Delivery state/province is required'
                })
        }).required(),
        preferredPickupDate: Joi.date().greater('now').required()
            .messages({
                'date.greater': 'Pickup date must be in the future',
                'any.required': 'Pickup date is required'
            }),
        preferredDeliveryDate: Joi.date().greater(Joi.ref('preferredPickupDate')).required()
            .messages({
                'date.greater': 'Delivery date must be after pickup date',
                'any.required': 'Delivery date is required'
            }),
        deliveryAssistanceRequired: Joi.boolean().default(false)
    }).required(),

    specialRequirements: Joi.object({
        requiresPermitsOrEscorts: Joi.boolean().default(false),
        specialHandlingInstructions: Joi.string().trim().max(500).allow('')
            .messages({
                'string.max': 'Special handling instructions must be less than 500 characters'
            }),
        deliveryType: Joi.string().valid('Recurring', 'One-time').required()
            .messages({
                'any.only': 'Please select a valid delivery type',
                'string.empty': 'Delivery type is required'
            })
    }).required(),

    _honeypot: Joi.string().allow('')

});

module.exports = { quoteValidationSchema };
