const express = require('express');
const router = express.Router();
const ApiLog = require('../model/ApiLog');

router.get('/', async (req, res) => {
    try {
        const logs = await ApiLog.find().sort({ hitTime: -1 });
        res.status(200).json(logs);
    } catch (error) {
        console.error('Error fetching API logs:', error);
        res.status(500).json({ message: 'Internal Server Error', error: error.message });
    }
});

// Get a single API log by ID
router.get('/:id', async (req, res) => {
    try {
        const log = await ApiLog.findById(req.params.id);
        if (!log) {
            return res.status(404).json({ message: 'Log not found' });
        }
        res.status(200).json(log);
    } catch (error) {
        console.error('Error fetching single API log:', error);
        res.status(500).json({ message: 'Internal Server Error', error: error.message });
    }
});

module.exports = router; 