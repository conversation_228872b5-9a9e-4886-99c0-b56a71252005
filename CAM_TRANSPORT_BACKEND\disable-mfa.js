const axios = require('axios');

async function disableMFA() {
    console.log('🔧 DISABLING MFA FOR DHRUV USER');
    console.log('===============================\n');
    
    const userId = '685923aa3135ef8ef080a6fe';
    
    try {
        console.log('🔐 Step 1: Disabling MFA via API...');
        
        const disableResponse = await axios.post(`http://localhost:8090/mfa/disable/${userId}`, {
            // No body needed for disable
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ MFA disabled successfully!');
        console.log('Response:', JSON.stringify(disableResponse.data, null, 2));
        
        console.log('\n🎯 MFA IS NOW DISABLED!');
        console.log('✅ You can now log in normally without MFA');
        console.log('✅ No authenticator code required');
        console.log('✅ Just use username/password');
        
        console.log('\n📱 WHEN YOU\'RE READY TO RE-ENABLE MFA:');
        console.log('1. Log in to your account');
        console.log('2. Go to MFA settings page');
        console.log('3. Enable MFA and scan the new QR code');
        console.log('4. Test it properly before relying on it');
        
        // Test login without MFA
        console.log('\n🧪 Testing login without MFA...');
        const loginResponse = await axios.post('http://localhost:8090/login', {
            username: 'dhruv',
            email: '<EMAIL>',
            password: 'dhruv@123'
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Login test successful!');
        console.log('   Requires MFA:', loginResponse.data.requiresMFA || false);
        console.log('   Requires Email OTP:', loginResponse.data.requiresEmailOTP || false);
        console.log('   User MFA Enabled:', loginResponse.data.user?.mfaEnabled || false);
        
    } catch (error) {
        console.error('❌ Error disabling MFA:', error.response?.data || error.message);
    }
}

disableMFA();
