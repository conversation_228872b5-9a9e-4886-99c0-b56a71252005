self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7f1c457e0c0705539cd3bb2fecd1846801a604019a\": {\n      \"workers\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/jobs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/urgent/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/pages/user-profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": \"rsc\",\n        \"app/[lang]/pages/user-profile/page\": \"rsc\"\n      }\n    },\n    \"7f27096b32fcebfe43c05866c28f62dafa12f2af15\": {\n      \"workers\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/jobs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/urgent/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/pages/user-profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": \"rsc\",\n        \"app/[lang]/pages/user-profile/page\": \"rsc\"\n      }\n    },\n    \"7f8f15f4893a0c6661561b55de5e700090a567f747\": {\n      \"workers\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/jobs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/urgent/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/pages/user-profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": \"rsc\",\n        \"app/[lang]/pages/user-profile/page\": \"rsc\"\n      }\n    },\n    \"7fa86eb7a919b6e5f5c31f51c0435a015785819d8a\": {\n      \"workers\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/jobs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/urgent/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/pages/user-profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": \"rsc\",\n        \"app/[lang]/pages/user-profile/page\": \"action-browser\"\n      }\n    },\n    \"7fcb01234d46807f3b15bc50ada2dffdbd2b77f83d\": {\n      \"workers\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/jobs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/urgent/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/pages/user-profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": \"rsc\",\n        \"app/[lang]/pages/user-profile/page\": \"rsc\"\n      }\n    },\n    \"7feff801cb850da8076429e1cb16b5d49fc5f9e303\": {\n      \"workers\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/contact/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/jobs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/(dashboard)/(private)/apps/user/urgent/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/[lang]/pages/user-profile/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[lang]/pages/user-profile/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/app/server/actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/[lang]/(dashboard)/(private)/apps/user/contact/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/jobs/page\": \"rsc\",\n        \"app/[lang]/(dashboard)/(private)/apps/user/urgent/page\": \"rsc\",\n        \"app/[lang]/pages/user-profile/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"3KGK2IZEJivyYixf6KYUDaQdI235ZnG7L25SngUYmG8=\"\n}"