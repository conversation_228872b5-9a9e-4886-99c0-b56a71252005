const nodemailer = require('nodemailer');
const { google } = require('googleapis');
const dotenv = require('dotenv');
const ejs = require('ejs');
const path = require('path');

dotenv.config();

const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;
const REFRESH_TOKEN = process.env.REFRESH_TOKEN;
const SMTP_FROM = process.env.SMTP_FROM;

const oAuth2Client = new google.auth.OAuth2(
    CLIENT_ID,
    CLIENT_SECRET,
    REDIRECT_URI
);
oAuth2Client.setCredentials({ refresh_token: REFRESH_TOKEN });

const importanceHeaders = {
    high: {
        'X-Priority': '1',
        'X-MSMail-Priority': 'High',
        'Importance': 'high'
    },
    normal: {
        'X-Priority': '3',
        'X-MSMail-Priority': 'Normal',
        'Importance': 'normal'
    },
    low: {
        'X-Priority': '5',
        'X-MSMail-Priority': 'Low',
        'Importance': 'low'
    }
};

const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        type: 'OAuth2',
        user: SMTP_FROM,
        clientId: CLIENT_ID,
        clientSecret: CLIENT_SECRET,
        refreshToken: REFRESH_TOKEN
    }
});

/**
 * Send an email, optionally rendering an EJS template.
 * @param {Object} options
 * @param {string} options.to - Recipient email address
 * @param {string} options.subject - Email subject
 * @param {string} [options.text] - Plain text body
 * @param {string} [options.html] - HTML body (used if no template)
 * @param {string} [options.template] - EJS template filename (in /email_templates)
 * @param {Object} [options.templateData] - Data for EJS template
 * @param {Array} [options.attachments] - Attachments
 * @param {string} [options.importance] - Email importance
 */
async function sendEmail({ to, subject, text, html, template, templateData = {}, attachments = [], importance = 'normal', headers = {} }) {
    let finalHtml = html;

    if (template) {
        const templatePath = path.join(__dirname, '../email_templates', template);
        finalHtml = await ejs.renderFile(templatePath, templateData);
    }

    try {
        const accessToken = await oAuth2Client.getAccessToken();
        const mailOptions = {
            from: `CAM Transport <${SMTP_FROM}>`,
            to,
            subject,
            text,
            html: finalHtml,
            replyTo: SMTP_FROM,
            headers: {
                ...(importanceHeaders[importance] || importanceHeaders.normal),
                ...headers
            },
            ...(attachments?.length > 0 ? { attachments } : {})
        };

        const result = await transporter.sendMail({
            ...mailOptions,
            auth: {
                ...transporter.options.auth,
                accessToken: accessToken.token
            }
        });

        console.log('✅ Email sent successfully to:', to);
        return result;
    } catch (error) {
        console.error('❌ Error sending email:', error);
        throw error;
    }
}

module.exports = { sendEmail };
