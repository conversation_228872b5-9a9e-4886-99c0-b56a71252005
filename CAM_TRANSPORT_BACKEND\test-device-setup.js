const API_BASE_URL = 'http://localhost:8090';
const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';

console.log('🧪 Testing Device Setup with Existing MFA User');
console.log('==============================================');

async function testDeviceSetup() {
    try {
        console.log('\n1️⃣ Testing device setup for user with existing MFA...');
        
        const setupResponse = await fetch(`${API_BASE_URL}/mfa/setup/1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                deviceName: 'iPhone 15 Pro'
            })
        });

        const setupResult = await setupResponse.json();
        console.log('Device setup result:', setupResult.success ? '✅ SUCCESS' : '❌ FAILED');
        
        if (setupResult.success) {
            console.log('✅ Device setup successful!');
            console.log('Secret returned:', setupResult.data.secret);
            console.log('Expected secret:', ADMIN_SECRET);
            console.log('Secret matches:', setupResult.data.secret === ADMIN_SECRET ? '✅ YES' : '❌ NO');
            console.log('Device ID:', setupResult.data.deviceId);
            console.log('Device Name:', setupResult.data.deviceName);
            console.log('QR code generated:', setupResult.data.qrCode ? '✅ YES' : '❌ NO');
            
            if (setupResult.data.secret === ADMIN_SECRET) {
                console.log('\n🎉 Perfect! The device setup is using the existing master secret!');
                console.log('📱 This means users can scan the same QR code on multiple devices.');
            } else {
                console.log('\n⚠️  Warning: Device setup generated a new secret instead of using the master secret.');
                console.log('This would break multi-device functionality.');
            }
        } else {
            console.log('Device setup failed:', setupResult.message);
        }

        console.log('\n2️⃣ Testing another device setup (should use same secret)...');
        
        const setup2Response = await fetch(`${API_BASE_URL}/mfa/setup/1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                deviceName: 'Android Galaxy S24'
            })
        });

        const setup2Result = await setup2Response.json();
        console.log('Second device setup result:', setup2Result.success ? '✅ SUCCESS' : '❌ FAILED');
        
        if (setup2Result.success) {
            console.log('Second device secret:', setup2Result.data.secret);
            console.log('Secrets match:', setup2Result.data.secret === ADMIN_SECRET ? '✅ YES' : '❌ NO');
            
            if (setup2Result.data.secret === ADMIN_SECRET) {
                console.log('✅ Excellent! Both devices use the same master secret!');
            }
        }

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

testDeviceSetup();
