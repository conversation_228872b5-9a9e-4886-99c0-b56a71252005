const mongoose = require('mongoose');
const Login = require('./model/Login');
const crypto = require('crypto');
require('dotenv').config();

/**
 * Reset admin user's MFA to use the new master secret approach
 */

async function resetAdminMFA() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/cam_transport', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('🔗 Connected to MongoDB');

        // Find admin user
        const adminUser = await Login.findOne({
            $or: [
                { username: 'admin' },
                { email: '<EMAIL>' },
                { adminId: '1' }
            ]
        });

        if (!adminUser) {
            console.log('❌ Admin user not found');
            return;
        }

        console.log('👤 Found admin user:', {
            id: adminUser._id,
            username: adminUser.username,
            email: adminUser.email,
            adminId: adminUser.adminId,
            mfaEnabled: adminUser.mfaEnabled,
            hasMfaSecret: !!adminUser.mfaSecret,
            deviceCount: adminUser.mfaDevices?.length || 0
        });

        // Reset MFA with master secret
        const fixedSecret = 'JBSWY3DPEHPK3PXP'; // Fixed secret for testing

        // Set the master secret
        adminUser.mfaSecret = fixedSecret;

        // Create a new device (for tracking only, no individual secret)
        const deviceId = crypto.randomBytes(16).toString('hex');
        const mfaDevice = {
            deviceId,
            deviceName: 'Admin Test Authenticator',
            isActive: true,
            registeredAt: new Date(),
            lastUsedAt: new Date(),
            deviceInfo: {
                userAgent: 'Reset Script',
                ipAddress: '127.0.0.1',
                platform: 'Admin'
            }
        };

        // Generate backup codes
        const backupCodes = [];
        for (let i = 0; i < 10; i++) {
            backupCodes.push({
                code: crypto.randomBytes(4).toString('hex').toUpperCase(),
                used: false,
                usedAt: null
            });
        }

        // Enable MFA with new structure
        adminUser.mfaEnabled = true;
        adminUser.mfaSetupAt = new Date();
        adminUser.mfaDevices = [mfaDevice];
        adminUser.backupCodes = backupCodes;

        await adminUser.save();

        console.log('✅ Admin MFA reset completed!');
        console.log('🔑 Master Secret:', fixedSecret);
        console.log('📱 Device ID:', deviceId);
        console.log('🔐 Backup codes generated:', backupCodes.length);

        // Verify the setup
        const updatedUser = await Login.findById(adminUser._id);
        console.log('\n🔍 Verification:');
        console.log('  - MFA Enabled:', updatedUser.mfaEnabled);
        console.log('  - Has Master Secret:', !!updatedUser.mfaSecret);
        console.log('  - Master Secret:', updatedUser.mfaSecret);
        console.log('  - Active Devices:', updatedUser.mfaDevices?.filter(d => d.isActive).length || 0);
        console.log('  - Backup Codes:', updatedUser.backupCodes?.length || 0);

    } catch (error) {
        console.error('❌ Reset failed:', error);
    } finally {
        // Close the database connection
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

// Run the reset
if (require.main === module) {
    console.log('🚀 Starting Admin MFA Reset...');
    resetAdminMFA()
        .then(() => {
            console.log('✅ Reset script completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ Reset script failed:', error);
            process.exit(1);
        });
}

module.exports = { resetAdminMFA };
