const Joi = require('joi');
const { isValidPhoneNumber } = require('libphonenumber-js');

const noMongoKeys = (value, helpers) => {
    if (typeof value === 'string' && (value.includes('$'))) {
        return helpers.error('any.invalid');
    }
    return value;
};

const urgentInquiryValidationSchema = Joi.object({
    full_name: Joi.string()
        .trim()
        .min(5)
        .max(100)
        .custom(noMongoKeys)
        .required()
        .messages({
            'string.base': 'Name must be a string',
            'string.empty': 'Name is required',
            'string.min': 'Name must be at least 5 characters',
            'string.max': 'Name must be less than 100 characters',
            'any.required': 'Name is required',
            'any.invalid': 'Invalid characters detected in Name',
        }),

    email: Joi.string()
        .trim()
        .email({ tlds: { allow: false } })
        .custom(noMongoKeys)
        .required()
        .messages({
            'string.email': 'Please enter a valid email address',
            'string.empty': 'Email is required',
            'any.required': 'Email is required',
            'any.invalid': 'Invalid characters detected in Email',
        }),

    phone_number: Joi.string()
        .trim()
        .required()
        .custom((value, helpers) => {
            const cleaned = value.replace(/\s+/g, '');
            if (!isValidPhoneNumber(cleaned)) {
                return helpers.error('any.invalid');
            }
            if (value.includes('$') || value.includes('.')) {
                return helpers.error('any.invalid');
            }
            return value;
        })
        .messages({
            'any.invalid': 'Please enter a valid phone number without invalid characters',
            'string.empty': 'Contact number is required',
            'any.required': 'Contact number is required',
        }),

    urgency_type: Joi.string()
        .trim()
        .valid(
            'Shipment Delay',
            'Vehicle Breakdown',
            'Delivery Issue',
            'Lost/Damaged Cargo',
            'Delivery Refusal',
            'Other(Please Specify)'
        )
        .required()
        .messages({
            'any.only': 'Urgency type must be one of the predefined values',
            'any.required': 'Urgency type is required',
        }),

    other_urgency: Joi.when('urgency_type', {
        is: 'Other(Please Specify)',
        then: Joi.string()
            .trim()
            .max(100)
            .required()
            .custom(noMongoKeys)
            .messages({
                'string.empty': 'Other urgency description is required',
                'string.max': 'Other urgency must be 100 characters or less',
                'any.required': 'Other urgency description is required',
                'any.invalid': 'Invalid characters detected in Other urgency description',
            }),
        otherwise: Joi.string().optional().allow(''),
    }),

    ref_number: Joi.when('urgency_type', {
        is: Joi.valid('Shipment Delay', 'Delivery Issue'),
        then: Joi.string()
            .trim()
            .min(1)
            .required()
            .custom(noMongoKeys)
            .messages({
                'string.empty': 'Reference number is required for shipment-related urgency types',
                'any.required': 'Reference number is required for shipment-related urgency types',
                'any.invalid': 'Invalid characters detected in Reference number',
            }),
        otherwise: Joi.string().optional().allow(''),
    }),

    documents: Joi.string()
        .optional()
        .allow('')
        .custom((value, helpers) => {
            if (!value) return value;
            const allowedExtensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
            const ext = value.slice(value.lastIndexOf('.')).toLowerCase();
            if (!allowedExtensions.includes(ext)) {
                return helpers.error('any.invalid');
            }
            if (value.includes('$') || value.includes('.')) {
                // Just check for no injection, but '.' is needed in file extension, so skip here or adjust logic carefully
                // So better not check '.' in documents field as extension contains dots.
            }
            return value;
        })
        .messages({
            'any.invalid': 'Document must be in .pdf, .doc, .docx, .jpg, .jpeg, or .png format',
        }),

    brief_description: Joi.string()
        .trim()
        .max(500)
        .required()
        .custom(noMongoKeys)
        .messages({
            'string.empty': 'Brief description is required',
            'string.max': 'Brief description must be 500 characters or less',
            'any.required': 'Brief description is required',
            'any.invalid': 'Invalid characters detected in Brief description',
        }),

    _honeypot: Joi.string().allow(''),

    ip: Joi.string().optional(),
});

module.exports = { urgentInquiryValidationSchema };
