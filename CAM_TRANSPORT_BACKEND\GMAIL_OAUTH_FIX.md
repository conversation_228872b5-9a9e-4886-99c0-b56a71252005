# Gmail OAuth2 Token Fix Guide

## Problem
Your Google OAuth2 refresh token has expired, causing the "invalid_grant" error when trying to send emails.

## Error Message
```
GaxiosError: invalid_grant
error_description: 'Token has been expired or revoked.'
```

## Solution Steps

### Option 1: Regenerate OAuth2 Credentials (Recommended)

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Select your project

2. **Navigate to APIs & Services > Credentials** 
   - Find your OAuth 2.0 Client ID
   - Download the credentials JSON file

3. **Generate New Refresh Token**
   - Use the OAuth2 Playground: https://developers.google.com/oauthplayground/
   - Or run a script to get new tokens

4. **Update Your .env File**
   ```
   GMAIL_CLIENT_ID=your_new_client_id
   GMAIL_CLIENT_SECRET=your_new_client_secret
   GMAIL_REFRESH_TOKEN=your_new_refresh_token
   SMTP_FROM=<EMAIL>
   ```

### Option 2: Temporary Fix (Contact Form Still Works)

The contact form will continue to work even if emails fail. The fix I implemented:

1. **Contact submissions are saved to database** ✅
2. **Email sending is wrapped in try-catch** ✅
3. **Form submission succeeds even if email fails** ✅
4. **Error is logged but doesn't break the form** ✅

### Option 3: Use App Password (Simpler Alternative)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security > 2-Step Verification > App passwords
   - Generate password for "Mail"

3. **Update Mailer Service** to use SMTP with app password instead of OAuth2

## Current Status

✅ **Contact form submissions work** - Data is saved to database
✅ **Admin can view contacts** - All submissions appear in contact list
✅ **Delete functionality works** - Can remove contacts
✅ **Status management works** - Frontend-only status tracking

❌ **Email notifications disabled** - Until OAuth2 tokens are refreshed

## Quick Test

1. Submit a contact form
2. Check if contact appears in admin panel
3. If yes, the core functionality works
4. Fix email separately when convenient

## Files Modified

- `controller/contact.js` - Added email error handling
- Contact form submissions continue working regardless of email status
