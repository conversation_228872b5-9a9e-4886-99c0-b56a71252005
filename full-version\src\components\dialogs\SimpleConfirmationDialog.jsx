'use client'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import IconButton from '@mui/material/IconButton'

const SimpleConfirmationDialog = ({ 
  open, 
  onClose, 
  onConfirm, 
  title = 'Confirm Action', 
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmColor = 'error',
  icon = 'tabler-alert-triangle'
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
    >
      <DialogTitle className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <i className={`${icon} text-xl`} />
          <Typography variant='h6'>{title}</Typography>
        </div>
        <IconButton size='small' onClick={onClose}>
          <i className='tabler-x' />
        </IconButton>
      </DialogTitle>
      
      <DialogContent>
        <Typography variant='body1' color='text.secondary'>
          {message}
        </Typography>
      </DialogContent>
      
      <DialogActions className='p-4'>
        <Button
          variant='tonal'
          color='secondary'
          onClick={onClose}
        >
          {cancelText}
        </Button>
        <Button
          variant='contained'
          color={confirmColor}
          onClick={onConfirm}
        >
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default SimpleConfirmationDialog
