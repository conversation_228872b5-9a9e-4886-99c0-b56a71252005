const axios = require('axios');

async function testNewOTPSystem() {
    console.log('🧪 Testing New OTP Management System');
    console.log('=====================================\n');
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    try {
        // Step 1: First login attempt
        console.log('📧 Step 1: Triggering first login (should generate OTP)...');
        const login1 = await axios.post('http://localhost:8090/login', credentials, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ First login response:', JSON.stringify(login1.data, null, 2));
        const userId = login1.data.user?.id || login1.data.user?._id;
        console.log(`👤 User ID: ${userId}\n`);
        
        // Wait 2 seconds
        console.log('⏳ Waiting 2 seconds...\n');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Step 2: Second login attempt (should invalidate first OTP)
        console.log('📧 Step 2: Triggering second login (should invalidate first OTP)...');
        const login2 = await axios.post('http://localhost:8090/login', credentials, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ Second login response:', JSON.stringify(login2.data, null, 2));
        console.log('\n🎯 Expected behavior:');
        console.log('- First OTP should be invalidated');
        console.log('- New OTP should be generated and sent via email');
        console.log('- Only the latest OTP should work');
        console.log('- OTP should expire in 5 minutes\n');
        
        // Step 3: Try to verify with an invalid OTP
        console.log('❌ Step 3: Testing with invalid OTP (123456)...');
        try {
            const invalidOTPTest = await axios.post('http://localhost:8090/login/verify-email-otp', {
                userId: userId,
                otp: 123456
            }, {
                headers: { 'Content-Type': 'application/json' }
            });
            console.log('❌ Unexpected success with invalid OTP');
        } catch (error) {
            console.log('✅ Correctly rejected invalid OTP:', error.response?.data?.message);
        }
        
        console.log('\n📧 Check your email for the latest OTP and use it to verify!');
        console.log('🔐 The OTP will expire in 5 minutes from the second login attempt.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

testNewOTPSystem();
