'use client'

// React Imports
import { useState } from 'react'

// MUI Imports
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import Grid from '@mui/material/Grid2'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Avatar from '@mui/material/Avatar'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'

// API Imports
import { downloadResume } from '@/services/jobApi'

const ApplicantDetailsModal = ({ open, onClose, applicantData }) => {
  if (!applicantData) return null

  // Date formatting function - consistent with other sections
  const formatDateTime = (dateString) => {
    if (!dateString) return 'Not provided'

    try {
      // Handle both ISO string and already formatted dates
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return dateString // Return original if invalid date

      // Format: MM/DD/YYYY HH:MM AM/PM (using actual submission time from database)
      const dateOptions = {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      }
      const timeOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }

      const formattedDate = date.toLocaleDateString('en-US', dateOptions)
      const formattedTime = date.toLocaleTimeString('en-US', timeOptions)

      return `${formattedDate} ${formattedTime}`
    } catch (error) {
      return dateString // Return original if formatting fails
    }
  }

  // Date only formatting function (for Date of Birth)
  const formatDateOnly = (dateString) => {
    if (!dateString) return 'Not provided'

    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return dateString

      // Format: MM/DD/YYYY (for Date of Birth, we don't need time)
      const dateOptions = {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      }

      return date.toLocaleDateString('en-US', dateOptions)
    } catch (error) {
      return dateString
    }
  }

  const handleDownloadResume = async () => {
    try {
      if (!applicantData.resume) {
        alert('No resume file found for this applicant.')
        return
      }

      console.log('Downloading resume for:', applicantData.fullName)
      console.log('Resume path:', applicantData.resume)

      await downloadResume(applicantData.resume, applicantData.fullName)
      alert('Resume downloaded successfully!')
    } catch (error) {
      console.error('Error downloading resume:', error)

      let errorMessage = 'Failed to download resume. '
      if (error.message.includes('404') || error.message.includes('not found')) {
        errorMessage += 'The resume file was not found on the server.'
      } else if (error.message.includes('network')) {
        errorMessage += 'Please check your internet connection.'
      } else {
        errorMessage += 'Please try again or contact support.'
      }

      alert(errorMessage)
    }
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle className="flex items-center justify-between">
        <Typography variant="h4" style={{ fontSize: '1.8rem' }}>Applicant Details</Typography>
        <IconButton onClick={onClose}>
          <i className="tabler-x" />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Grid container spacing={6}>
          {/* Applicant Profile Section */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <div className="flex items-center gap-6 mb-6">
                  <Avatar
                    sx={{ width: 120, height: 120, fontSize: '3.5rem', fontWeight: 'bold' }}
                  >
                    {applicantData.fullName?.charAt(0)?.toUpperCase()}
                  </Avatar>
                  <div>
                    <Typography variant="h3" className="mb-3 font-bold" style={{ fontSize: '2.2rem' }}>
                      {applicantData.fullName}
                    </Typography>
                    <Typography variant="h5" color="text.secondary" className="font-medium" style={{ fontSize: '1.4rem' }}>
                      {applicantData.email}
                    </Typography>
                  </div>
                </div>

                <Divider className="mb-6" />

                {/* Personal Information */}
                <Typography variant="h5" className="mb-4 font-bold" style={{ fontSize: '1.6rem' }}>
                  Personal Information
                </Typography>
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      First Name
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.first_name || applicantData.fullName?.split(' ')[0] || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Last Name
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.last_name || applicantData.fullName?.split(' ')[1] || 'N/A'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Phone Number
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.phone_number || applicantData.phone || '+****************'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Date of Birth
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {formatDateOnly(applicantData.dob)}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Address
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.address || 'Not provided'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Job Information */}
          <Grid size={{ xs: 12 }}>
            <Card>
              <CardContent>
                <Typography variant="h5" className="mb-4 font-bold" style={{ fontSize: '1.6rem' }}>
                  Job Information
                </Typography>
                <Grid container spacing={4}>
                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Position
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.position || 'Software Developer'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Employment Type
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.employment_type || applicantData.employmentType || 'Full-time'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Specific Driving Role
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.specific_driving_role || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Specific Non-Driving Role
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.specific_non_driving_role || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Commercial License
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.commercial_license || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Other Job
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.other_job || 'None'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Preferred Start Date
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.preferred_start_date && applicantData.preferred_start_date !== 'Flexible'
                        ? formatDateOnly(applicantData.preferred_start_date)
                        : (applicantData.preferred_start_date || 'Flexible')}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Willing to Relocate
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.relocate || 'Not specified'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Experience
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.experience || '2-3 years'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Resume
                    </Typography>
                    <div className="flex items-center gap-3">
                      <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                        {applicantData.resume ? 'Available' : 'Not uploaded'}
                      </Typography>
                      {applicantData.resume && (
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={handleDownloadResume}
                          startIcon={<i className="tabler-download" />}
                          sx={{
                            fontSize: '0.75rem',
                            padding: '4px 12px',
                            '&:hover': {
                              backgroundColor: 'primary.light',
                              transform: 'scale(1.02)'
                            }
                          }}
                        >
                          Download
                        </Button>
                      )}
                    </div>
                  </Grid>

                  <Grid size={{ xs: 12 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Work Reason
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem', lineHeight: '1.4' }}>
                      {applicantData.work_reason || 'Looking for new opportunities and career growth.'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Reference
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.reference || 'Available upon request'}
                    </Typography>
                  </Grid>

                  <Grid size={{ xs: 12, sm: 6 }}>
                    <Typography variant="h6" color="text.secondary" className="mb-2 font-medium" style={{ fontSize: '1.1rem' }}>
                      Other Reference
                    </Typography>
                    <Typography variant="h6" className="font-bold" style={{ fontSize: '1.3rem' }}>
                      {applicantData.other_reference || 'None provided'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default ApplicantDetailsModal
