// Next Imports
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { getToken } from 'next-auth/jwt'

export async function GET(req) {
  try {
    // Get the URL parameters
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')
    const otp = searchParams.get('otp')

    if (!userId || !otp) {
      return NextResponse.json(
        { success: false, message: 'Invalid verification link. Missing userId or OTP.' },
        { status: 400 }
      )
    }

    // Forward the verification request to your backend
    const backendUrl = process.env.API_URL || 'http://localhost:8000'
    const verificationUrl = `${backendUrl}/verify-otp/${userId}/${otp}`

    const response = await fetch(verificationUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      // Verification successful
      return NextResponse.json(
        { success: true, message: 'Email verified successfully' },
        { status: 200 }
      )
    } else {
      // Verification failed
      const errorData = await response.text()
      return NextResponse.json(
        { success: false, message: 'Verification failed', details: errorData },
        { status: response.status }
      )
    }
  } catch (error) {
    console.error('Verification error:', error)
    return NextResponse.json(
      { success: false, message: 'An error occurred during verification' },
      { status: 500 }
    )
  }
}


