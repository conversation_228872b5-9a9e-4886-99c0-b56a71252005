const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config();

// Import the Login model
const Login = require('./model/Login');

async function fixUserEmail() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log('🔗 Connected to MongoDB');
        
        // Find the user by ID (from the previous test)
        const userId = '685923aa3135ef8ef080a6fe';
        const user = await Login.findById(userId);
        
        if (!user) {
            console.log('❌ User not found');
            return;
        }
        
        console.log('📋 Current user data:');
        console.log(`Username: ${user.username}`);
        console.log(`Email: ${user.email}`);
        console.log(`Password: ${user.password?.substring(0, 20)}...`);
        console.log(`Role: ${user.role}`);
        console.log(`Active: ${user.isActive}`);
        console.log(`Verified: ${user.isVerified}`);
        console.log(`MFA Enabled: ${user.mfaEnabled}`);
        
        // Update the user with correct email and password
        console.log('\n🔧 Updating user with correct credentials...');
        
        const hashedPassword = await bcrypt.hash('dhruv@123', 10);
        
        user.email = '<EMAIL>';
        user.username = 'dhruv';
        user.password = hashedPassword;
        user.isActive = true;
        user.isVerified = true;
        user.mfaEnabled = false; // This will trigger email OTP
        
        await user.save();
        
        console.log('✅ User updated successfully!');
        console.log('New email:', user.email);
        console.log('New username:', user.username);
        
        // Test the login again
        console.log('\n🧪 Testing login with updated credentials...');
        
        const axios = require('axios');
        
        try {
            const response = await axios.post('http://localhost:8090/login', {
                username: 'dhruv',
                email: '<EMAIL>',
                password: 'dhruv@123'
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('✅ Login Success!');
            console.log('Response:', JSON.stringify(response.data, null, 2));
            
            if (response.data.requiresEmailOTP) {
                console.log('\n📧 Email OTP Required!');
                console.log('The system has sent an OTP to your email.');
                console.log('To complete login, you need to:');
                console.log('1. Check your email for the OTP code');
                console.log('2. Use the /login/verify-email-otp endpoint with the OTP');
                console.log('3. Or use the frontend to enter the OTP');
            }
            
        } catch (error) {
            console.log('❌ Login failed:', error.response?.data || error.message);
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n🔌 Database connection closed');
    }
}

if (require.main === module) {
    fixUserEmail().then(() => {
        console.log('\n✅ Fix completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Fix failed:', error);
        process.exit(1);
    });
}
