const axios = require('axios');

async function testCompleteEmailOTPFlow() {
    console.log('🧪 Testing Complete Email OTP Flow...');
    console.log('=' .repeat(60));
    
    const credentials = {
        username: 'dhruv',
        email: '<EMAIL>',
        password: 'dhruv@123'
    };
    
    console.log('📋 Test Credentials:');
    console.log(`Username: ${credentials.username}`);
    console.log(`Email: ${credentials.email}`);
    console.log(`Password: ${credentials.password}`);
    console.log('');
    
    // Step 1: Test Backend Login API
    console.log('Step 1: Testing Backend Login API...');
    try {
        const backendResponse = await axios.post('http://localhost:8090/login', credentials, {
            headers: { 'Content-Type': 'application/json' },
            timeout: 10000
        });
        
        console.log('✅ Backend Login API Response:');
        console.log('Status:', backendResponse.status);
        console.log('Data:', JSON.stringify(backendResponse.data, null, 2));
        
        if (backendResponse.data.requiresEmailOTP) {
            console.log('🎯 Backend correctly requires Email OTP!');
        } else {
            console.log('⚠️ Backend does not require Email OTP - this is the issue!');
            return;
        }
        
    } catch (error) {
        console.log('❌ Backend Login API Failed:');
        console.log('Error:', error.response?.data || error.message);
        return;
    }
    
    console.log('');
    
    // Step 2: Test Frontend NextAuth API
    console.log('Step 2: Testing Frontend NextAuth API...');
    try {
        const nextAuthResponse = await axios.post('http://localhost:3000/api/auth/callback/credentials', {
            username: credentials.username,
            email: credentials.email,
            password: credentials.password,
            redirect: 'false',
            json: 'true'
        }, {
            headers: { 
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            },
            timeout: 10000
        });
        
        console.log('✅ NextAuth API Response:');
        console.log('Status:', nextAuthResponse.status);
        console.log('Data:', JSON.stringify(nextAuthResponse.data, null, 2));
        
    } catch (error) {
        console.log('❌ NextAuth API Failed:');
        if (error.response) {
            console.log('Status:', error.response.status);
            console.log('Data:', error.response.data);
        } else {
            console.log('Error:', error.message);
        }
    }
    
    console.log('');
    console.log('=' .repeat(60));
    console.log('🔍 ANALYSIS:');
    console.log('');
    console.log('1. If Backend API returns requiresEmailOTP: true ✅');
    console.log('2. But Frontend allows login without OTP ❌');
    console.log('3. Then the issue is in NextAuth configuration');
    console.log('');
    console.log('💡 SOLUTION:');
    console.log('- Check browser console for NextAuth logs');
    console.log('- Verify JWT and session callbacks preserve OTP flags');
    console.log('- Ensure frontend login component detects otpPending flag');
    console.log('');
    console.log('📧 Next Steps:');
    console.log('1. Try logging in through the frontend');
    console.log('2. Check browser console for debug logs');
    console.log('3. Look for "Session user flags" log message');
    console.log('4. Verify otpPending is true in the session');
}

if (require.main === module) {
    testCompleteEmailOTPFlow().then(() => {
        console.log('\n✅ Test completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
}
