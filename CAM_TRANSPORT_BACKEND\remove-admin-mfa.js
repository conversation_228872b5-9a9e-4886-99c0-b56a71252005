const mongoose = require('mongoose');
const Login = require('./model/Login');
require('dotenv').config();

async function removeAdminMFA() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/cam_transport', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });

        console.log('🔗 Connected to MongoDB');

        // Find the admin user
        const adminUser = await Login.findOne({ adminId: '1' });

        if (!adminUser) {
            console.log('❌ Admin user not found');
            return;
        }

        console.log('👤 Found admin user:', {
            id: adminUser._id,
            adminId: adminUser.adminId,
            username: adminUser.username,
            email: adminUser.email,
            mfaEnabled: adminUser.mfaEnabled,
            hasMfaSecret: !!adminUser.mfaSecret,
            deviceCount: adminUser.mfaDevices?.length || 0
        });

        // Completely disable and remove MFA
        adminUser.mfaEnabled = false;
        adminUser.mfaSecret = null;
        adminUser.mfaDevices = [];
        adminUser.backupCodes = [];
        adminUser.mfaSetupAt = null;
        adminUser.lastMfaUsed = null;

        await adminUser.save();

        console.log('✅ MFA completely removed from admin user!');
        console.log('');
        console.log('🔍 Verification:');
        console.log(`  MFA Enabled: ${adminUser.mfaEnabled}`);
        console.log(`  MFA Secret: ${adminUser.mfaSecret || 'None'}`);
        console.log(`  Devices: ${adminUser.mfaDevices?.length || 0}`);
        console.log(`  Backup Codes: ${adminUser.backupCodes?.length || 0}`);
        console.log('');
        console.log('📱 Next Steps:');
        console.log('1. Remove CAM Transport accounts from both Google and Microsoft Authenticator');
        console.log('2. Log in to the admin account - it will auto-setup MFA with a fresh secret');
        console.log('3. Scan the same QR code on both devices');
        console.log('4. Test authentication with both apps');

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
    }
}

removeAdminMFA();
