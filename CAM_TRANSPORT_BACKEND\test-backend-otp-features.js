const axios = require('axios');

async function testBackendOTPFeatures() {
    console.log('🔍 Testing if backend has new OTP management features...\n');
    
    try {
        // Test 1: Generate OTP and check response
        console.log('📧 Test 1: Generating OTP...');
        const loginResponse = await axios.post('http://localhost:8090/login', {
            username: 'dhruv',
            email: '<EMAIL>',
            password: 'dhruv@123'
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        
        console.log('✅ OTP generation response:', JSON.stringify(loginResponse.data, null, 2));
        
        const userId = loginResponse.data.user?.id || loginResponse.data.user?._id;
        
        // Test 2: Try OTP verification with detailed logging
        console.log('\n🔍 Test 2: Testing OTP verification with invalid OTP...');
        try {
            const otpResponse = await axios.post('http://localhost:8090/login/verify-email-otp', {
                userId: userId,
                otp: 999999
            }, {
                headers: { 'Content-Type': 'application/json' }
            });
            console.log('❌ Unexpected success:', otpResponse.data);
        } catch (error) {
            console.log('✅ Expected failure:', error.response?.data);
            
            // Check if we get detailed error messages (indicates new code is running)
            if (error.response?.data?.message === 'Invalid OTP') {
                console.log('🎯 Backend has NEW OTP management code!');
            } else if (error.response?.data?.message === 'No OTP found. Please request a new login.') {
                console.log('⚠️  Backend might have OLD OTP management code');
            }
        }
        
        console.log('\n📋 SUMMARY:');
        console.log('- If you see detailed logging in backend console = NEW code');
        console.log('- If you see minimal logging = OLD code');
        console.log('- Backend server needs restart to load new OTP features');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

testBackendOTPFeatures();
