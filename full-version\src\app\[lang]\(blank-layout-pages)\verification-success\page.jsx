'use client'

// React Imports
import { useEffect, useState } from 'react'

// Next Imports
import { useRouter } from 'next/navigation'

// MUI Imports
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'

// Component Imports
import Logo from '@components/layout/shared/Logo'

// Hook Imports
import { useImageVariant } from '@core/hooks/useImageVariant'

// Styled Components
const MaskImg = styled('img')({
  blockSize: '100%',
  inlineSize: '100%',
  position: 'absolute'
})

const VerificationIllustration = styled('img')(({ theme }) => ({
  zIndex: 2,
  blockSize: 'auto',
  maxInlineSize: '100%',
  margin: theme.spacing(20, 0),
  [theme.breakpoints.down(1536)]: {
    margin: theme.spacing(15, 0)
  },
  [theme.breakpoints.down(1200)]: {
    margin: theme.spacing(10, 0)
  }
}))

const VerificationSuccess = ({ mode }) => {
  // States
  const [isLoading, setIsLoading] = useState(true)
  const [verificationStatus, setVerificationStatus] = useState(null)

  // Vars
  const darkImg = '/images/pages/auth-mask-dark.png'
  const lightImg = '/images/pages/auth-mask-light.png'
  const darkIllustration = '/images/illustrations/auth/v2-verify-dark.png'
  const lightIllustration = '/images/illustrations/auth/v2-verify-light.png'

  // Hooks
  const router = useRouter()
  const authBackground = useImageVariant(mode, lightImg, darkImg)
  const characterIllustration = useImageVariant(mode, lightIllustration, darkIllustration)

  useEffect(() => {
    // Simulate verification check
    const timer = setTimeout(() => {
      setVerificationStatus('success')
      setIsLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  const handleContinue = () => {
    router.push('/pages/user-profile')
  }

  const handleBackToLogin = () => {
    router.push('/login')
  }

  if (isLoading) {
    return (
      <div className='flex bs-full justify-center items-center'>
        <Card className='flex flex-col items-center p-8'>
          <CardContent className='flex flex-col items-center gap-4'>
            <CircularProgress size={60} />
            <Typography variant='h5'>Verifying your login...</Typography>
            <Typography variant='body2' color='text.secondary' className='text-center'>
              Please wait while we verify your OTP. This may take a few moments.
            </Typography>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className='flex bs-full justify-center'>
      <div className='flex bs-full items-center justify-center flex-1 min-bs-[100dvh] relative p-6 max-md:hidden'>
        <VerificationIllustration src={characterIllustration} alt='verification-illustration' />
        <MaskImg alt='mask' src={authBackground} />
      </div>
      <div className='flex justify-center items-center bs-full bg-backgroundPaper !min-is-full p-6 md:!min-is-[unset] md:p-12 md:is-[480px]'>
        <div className='absolute block-start-5 sm:block-start-[33px] inline-start-6 sm:inline-start-[38px]'>
          <Logo />
        </div>
        <div className='flex flex-col gap-6 is-full sm:is-auto md:is-full sm:max-is-[400px] md:max-is-[unset] mbs-8 sm:mbs-11 md:mbs-0'>
          <Card className='flex flex-col items-center p-8'>
            <CardContent className='flex flex-col items-center gap-6 text-center'>
              <Box className='flex items-center justify-center w-20 h-20 rounded-full bg-success-light'>
                <i className='tabler-check text-4xl text-success-main' />
              </Box>
              
              <div className='flex flex-col gap-2'>
                <Typography variant='h4' className='text-success-main'>
                  🎉 You're Verified! 🎉
                </Typography>
                <Typography variant='h6'>
                  Login Successful
                </Typography>
              </div>
              
              <Typography variant='body1' color='text.secondary' className='text-center'>
                Your admin login has been successfully verified. You can now access the system and manage users.
              </Typography>
              
              <div className='flex flex-col gap-4 w-full'>
                <Button
                  fullWidth
                  variant='contained'
                  color='success'
                  onClick={handleContinue}
                  size='large'
                >
                  Continue to Profile
                </Button>
                
                <Button 
                  fullWidth 
                  variant='outlined' 
                  onClick={handleBackToLogin}
                  size='large'
                >
                  Back to Login
                </Button>
              </div>
              
              <Typography variant='caption' color='text.secondary' className='text-center mt-4'>
                Verification completed on: {new Date().toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default VerificationSuccess

