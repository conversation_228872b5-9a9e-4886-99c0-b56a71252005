const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
require('dotenv').config();

// Import the Login model
const Login = require('./model/Login');

async function debugUserLogin() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL, {
            ssl: true,
            serverSelectionTimeoutMS: 5000,
            connectTimeoutMS: 10000
        });
        
        console.log('🔗 Connected to MongoDB');
        
        // Check what users exist
        console.log('\n📋 All users in database:');
        const allUsers = await Login.find({}, 'username email password role isActive isVerified mfaEnabled');
        
        allUsers.forEach((user, index) => {
            console.log(`${index + 1}. Username: ${user.username}`);
            console.log(`   Email: ${user.email}`);
            console.log(`   Password (hashed): ${user.password?.substring(0, 20)}...`);
            console.log(`   Role: ${user.role}`);
            console.log(`   Active: ${user.isActive}`);
            console.log(`   Verified: ${user.isVerified}`);
            console.log(`   MFA Enabled: ${user.mfaEnabled}`);
            console.log('   ---');
        });
        
        // Test specific login credentials
        const testCredentials = {
            email: '<EMAIL>',
            username: 'dhruv',
            password: 'dhruv@123'
        };
        
        console.log('\n🧪 Testing login with credentials:');
        console.log(`Email: ${testCredentials.email}`);
        console.log(`Username: ${testCredentials.username}`);
        console.log(`Password: ${testCredentials.password}`);
        
        // Find user by email or username
        const user = await Login.findOne({
            $or: [
                { username: testCredentials.username },
                { email: testCredentials.email }
            ]
        });
        
        if (!user) {
            console.log('❌ No user found with that email or username');
            
            // Create the user with correct credentials
            console.log('\n🔧 Creating user with correct credentials...');
            
            const hashedPassword = await bcrypt.hash(testCredentials.password, 10);
            
            const newUser = await Login.create({
                username: testCredentials.username,
                email: testCredentials.email,
                password: hashedPassword,
                role: 'admin',
                isActive: true,
                isVerified: true, // Set to true so no email verification needed
                mfaEnabled: false, // Set to false so email OTP will be used
                ipAddress: '127.0.0.1',
                location: 'Local',
                lastLoginDate: new Date(),
                adminId: `ADMIN_${Date.now()}`
            });
            
            console.log('✅ User created successfully:', newUser.email);
            
        } else {
            console.log('✅ User found:', user.email);
            
            // Test password verification
            console.log('\n🔍 Testing password verification...');
            
            let isPasswordValid = false;
            
            if (user.username === 'admin' && testCredentials.password === 'admin') {
                isPasswordValid = true;
                console.log('✅ Admin plain text password match');
            } else {
                isPasswordValid = await bcrypt.compare(testCredentials.password, user.password);
                console.log(`🔍 bcrypt.compare result: ${isPasswordValid}`);
            }
            
            if (!isPasswordValid) {
                console.log('❌ Password does not match');
                console.log('🔧 Updating user password...');
                
                const hashedPassword = await bcrypt.hash(testCredentials.password, 10);
                user.password = hashedPassword;
                user.isVerified = true; // Ensure user is verified
                user.isActive = true; // Ensure user is active
                await user.save();
                
                console.log('✅ Password updated successfully');
            } else {
                console.log('✅ Password matches!');
            }
            
            // Check user status
            console.log('\n📊 User Status:');
            console.log(`Active: ${user.isActive}`);
            console.log(`Verified: ${user.isVerified}`);
            console.log(`MFA Enabled: ${user.mfaEnabled}`);
        }
        
        // Test the login API endpoint
        console.log('\n🌐 Testing login API endpoint...');
        
        const axios = require('axios');
        
        try {
            const response = await axios.post('http://localhost:8090/login', {
                username: testCredentials.username,
                email: testCredentials.email,
                password: testCredentials.password
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('✅ Login API Success:', response.data.message);
            console.log('📧 Requires Email OTP:', response.data.requiresEmailOTP);
            console.log('🔐 Requires MFA:', response.data.requiresMFA);
            
        } catch (error) {
            console.log('❌ Login API Error:', error.response?.data || error.message);
        }
        
    } catch (error) {
        console.error('❌ Debug script error:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n🔌 Database connection closed');
    }
}

// Run the debug script
if (require.main === module) {
    debugUserLogin().then(() => {
        console.log('\n✅ Debug script completed');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Debug script failed:', error);
        process.exit(1);
    });
}

module.exports = { debugUserLogin };
