import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/libs/auth'

export default async function Page() {
    const session = await getServerSession(authOptions)

    // If no session, redirect to login
    if (!session) {
        redirect('/login')
        return null
    }

    // If user has session, redirect to profile (regardless of verification status)
    redirect('/pages/user-profile')
    return null
}
