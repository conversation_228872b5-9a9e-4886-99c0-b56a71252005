const speakeasy = require('speakeasy');

// Test the fixed secret that should be used for admin user
const ADMIN_SECRET = 'JBSWY3DPEHPK3PXP';
const API_BASE_URL = 'http://localhost:8090';

console.log('🧪 Testing MFA Master Secret Implementation');
console.log('==========================================');

async function testMFAMasterSecret() {
    try {
        console.log('\n1️⃣ Testing admin login (should auto-setup MFA with master secret)...');
        
        const loginResponse = await fetch(`${API_BASE_URL}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin'
            })
        });

        const loginResult = await loginResponse.json();
        console.log('Login result:', loginResult.success ? '✅ SUCCESS' : '❌ FAILED');
        
        if (!loginResult.success) {
            console.log('Login failed:', loginResult.message);
            return;
        }

        console.log('User MFA enabled:', loginResult.user.mfaEnabled);
        console.log('User requires MFA:', loginResult.user.requiresMFA);

        if (loginResult.user.mfaEnabled) {
            console.log('\n2️⃣ Testing MFA verification with master secret...');
            
            // Generate current TOTP token using the fixed secret
            const currentToken = speakeasy.totp({
                secret: ADMIN_SECRET,
                encoding: 'base32'
            });
            
            console.log(`🔑 Generated token: ${currentToken}`);
            
            // Test MFA verification
            const mfaResponse = await fetch(`${API_BASE_URL}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: 'admin',
                    email: '<EMAIL>',
                    password: 'verified',
                    mfaToken: currentToken,
                    step: 'mfa'
                })
            });

            const mfaResult = await mfaResponse.json();
            console.log('MFA verification result:', mfaResult.success ? '✅ SUCCESS' : '❌ FAILED');
            
            if (!mfaResult.success) {
                console.log('MFA verification failed:', mfaResult.message);
            } else {
                console.log('✅ MFA verification successful!');
            }

            console.log('\n3️⃣ Testing multiple device simulation...');
            console.log('This simulates using the same secret on multiple devices:');
            
            // Simulate multiple devices generating the same token
            for (let i = 1; i <= 3; i++) {
                const deviceToken = speakeasy.totp({
                    secret: ADMIN_SECRET,
                    encoding: 'base32'
                });
                
                console.log(`📱 Device ${i} generates token: ${deviceToken}`);
                
                // Test if this token would work
                const deviceTestResponse = await fetch(`${API_BASE_URL}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        email: '<EMAIL>',
                        password: 'verified',
                        mfaToken: deviceToken,
                        step: 'mfa'
                    })
                });

                const deviceTestResult = await deviceTestResponse.json();
                console.log(`   Device ${i} verification:`, deviceTestResult.success ? '✅ SUCCESS' : '❌ FAILED');
                
                if (!deviceTestResult.success) {
                    console.log(`   Error: ${deviceTestResult.message}`);
                }
                
                // Small delay to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            console.log('\n4️⃣ Testing MFA device setup (should use master secret)...');
            
            const setupResponse = await fetch(`${API_BASE_URL}/mfa/setup/1`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    deviceName: 'Test Device'
                })
            });

            const setupResult = await setupResponse.json();
            console.log('Device setup result:', setupResult.success ? '✅ SUCCESS' : '❌ FAILED');
            
            if (setupResult.success) {
                console.log('✅ Device setup successful!');
                console.log('Secret matches admin secret:', setupResult.data.secret === ADMIN_SECRET ? '✅ YES' : '❌ NO');
                console.log('QR code generated:', setupResult.data.qrCode ? '✅ YES' : '❌ NO');
            } else {
                console.log('Device setup failed:', setupResult.message);
            }
        }

        console.log('\n🎉 Test completed!');
        console.log('\n📋 Summary:');
        console.log('- Admin user should have MFA auto-enabled with master secret');
        console.log('- All devices use the same secret (JBSWY3DPEHPK3PXP)');
        console.log('- Multiple devices can generate valid tokens');
        console.log('- Same QR code can be scanned on multiple devices');
        console.log('- Works with Google Authenticator, Microsoft Authenticator, Authy, etc.');

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testMFAMasterSecret();
